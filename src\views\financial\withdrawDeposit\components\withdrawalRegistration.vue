<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="时间类型" clearable style="width: 85px; margin: 0 0 5px 0">
					<el-option label="日期" value="日期" />
					<el-option label="操作时间" value="操作时间" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-select v-model="query.platform" placeholder="平台" class="publicCss itemCss" clearable filterable @change="fetchShopList">
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model.trim="query.shopId" placeholder="店铺" class="publicCss itemCss" clearable filterable>
					<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.platformShopId" class="publicCss" style="width: 110px" placeholder="平台店铺ID" clearable maxlength="50" />
				<el-select v-model="query.amountType" placeholder="金额类型" clearable style="width: 85px; margin: 0 0 5px 0">
					<el-option label="店铺余额" value="店铺余额" />
					<el-option label="提现金额" value="提现金额" />
				</el-select>
				<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2" style="width: 160px; margin: 0 5px 5px 0" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				<el-select v-model="query.dataSource" placeholder="提现类型" class="publicCss itemCss" clearable filterable>
					<el-option key="RPA" label="RPA" value="RPA" />
					<el-option key="人工" label="人工" value="人工" />
				</el-select>
				<el-input v-model.trim="query.operatorUserName" placeholder="操作人" class="publicCss itemCss" clearable maxlength="50" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss itemCss" clearable filterable>
					<el-option key="待抓取" label="待抓取" value="待抓取" />
					<el-option key="已抓取" label="已抓取" value="已抓取" />
					<el-option key="已提现" label="已提现" value="已提现" />
					<el-option key="提现失败" label="提现失败" value="提现失败" />
					<el-option key="已编辑" label="已编辑" value="已编辑" />
				</el-select>
				<el-input v-model.trim="query.ownTag" class="publicCss" style="width: 120px" placeholder="店铺负责人" clearable maxlength="50" />
				<el-select v-model="query.isCommit" placeholder="是否提交" class="publicCss itemCss" clearable filterable>
					<el-option key="是" label="是" :value="true" />
					<el-option key="否" label="否" :value="false" />
				</el-select>
				<el-select v-model="query.accountName" placeholder="提现账户名(别名)" class="publicCss" clearable filterable>
					<el-option v-for="item in alipayCashWithdrawal.filter((item) => item.bankType.trim() !== '支付宝')" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.aliPayAccountName" placeholder="支付宝账户(别名)" class="publicCss" clearable filterable>
					<el-option v-for="item in alipayCashWithdrawal.filter((item) => item.bankType.trim() === '支付宝')" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				isNeedCheckBox
				@select="checkboxChange"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawInfoTemp"
			>
				<template #toolbar_buttons>
					<el-button @click="onSubmitRegistration" type="primary">提交登记</el-button>
					<div style="border: 1px solid #dcdfe6; border-radius: 5px; display: flex; align-items: center; padding: 0 10px; margin-left: 10px">
						<el-input v-model.trim="pleaseEnter" class="publicCss inputboBor" style="width: 180px" placeholder="模糊店铺名至对应行" clearable maxlength="50" @blur="pleaseEnterBlur" />
						<div style="height: 20px; width: 1px; background-color: #eee; margin-right: 15px"></div>
						<el-icon :size="20" style="cursor: pointer; margin-right: 10px; color: #dcdfe6" @click="scrollUp">
							<ArrowUp />
						</el-icon>
						<el-icon :size="20" style="cursor: pointer; color: #dcdfe6" @click="scrollDown">
							<ArrowDown />
						</el-icon>
					</div>
					<el-button @click="batchPayouts" type="primary" class="ml5">批量提现</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="编辑" width="400" draggable overflow @close="handleClose" style="margin-top: -18vh !important">
		<div>
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="店铺名" :label-width="'90px'">
					{{ singleform.shopName }}
				</el-form-item>
				<el-form-item label="提现账户名" :label-width="'90px'">
					<el-select v-model="singleform.account" placeholder="提现账户名" class="btnGroup" clearable filterable @change="accountChnene($event, 1)">
						<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="提现支付宝" :label-width="'90px'">
					<el-select v-model="singleform.aliPayAccount" placeholder="提现支付宝" class="btnGroup" clearable filterable @change="accountChnene($event, 2)">
						<el-option v-for="item in withdrawAlipay" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="提现金额" :label-width="'90px'" prop="withDrawAmount">
					<el-input-number v-model="singleform.withDrawAmount" placeholder="请输入" :min="0" :max="**************" :controls="false" class="btnGroup" />
				</el-form-item>
				<el-form-item label="店铺余额" :label-width="'90px'">
					<el-input-number v-model="singleform.shopBalance" placeholder="请输入" :min="0" :max="**************" :controls="false" class="btnGroup" />
				</el-form-item>
				<el-form-item label="提现时间" :label-width="'90px'" prop="occurrenceTime">
					<el-date-picker
						v-model="singleform.occurrenceTime"
						type="datetime"
						placement="right"
						placeholder="提现时间"
						format="YYYY-MM-DD HH:mm:ss"
						date-format="YYYY-MM-DD"
						time-format="HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						class="btnGroup"
					/>
				</el-form-item>
				<el-form-item label="原因" :label-width="'90px'">
					<el-input
						v-model="singleform.reason"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						class="btnGroup"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog v-model="batchPayoutsVisible" title="批量提现" :width="batchPayoutsWidth" draggable overflow @close="handleClose" style="margin-top: -18vh !important" :close-on-click-modal="false">
		<batchPayoutsPage v-if="batchPayoutsVisible" style="width: 100%; height: 100%" @changeWidth="changeWidth" @close="batchPayoutsVisible = false" />
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryWithDrawInfoTemp, UpdateWithDrawInfoTemp, GetWithDrawShopList, SyncWithDrawInfoTemp, BatchGetWithDrawShopData } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import type { FormInstance } from 'element-plus';
import dayjs from 'dayjs';
import { bankList, platformlist, expressCompany } from '/@/utils/tools';
import Account from '/@/views/login/component/account.vue';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const batchPayoutsPage = defineAsyncComponent(() => import('./batchPayouts.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const batchPayoutsVisible = ref<boolean>(false);
const batchPayoutsWidth = ref<number>(600);
const ruleFormRef = ref<FormInstance | null>(null);
const nameList = ref<Public.options[]>([]);
const withdrawAlipay = ref<Public.options[]>([]);
const shopNamelist = ref<Public.options[]>([]);
const alipayCashWithdrawal = ref<any[]>([]);
interface CheckboxItem {
	id: string | number;
}
interface CheckboxItem {
	id: string | number;
	withDrawTime?: string;
	occurrenceTime?: string;
	modifiedTime?: string;
	createdTime?: string;
	[key: string]: any; // Allow additional fields
}
const checkboxList = ref<CheckboxItem[]>([]);
const table = ref();
const pleaseEnter = ref('');
interface TableItem {
	shopName: string;
	id: string;
}
const tableData = ref<TableItem[]>([]);
const filteredRows = ref<TableItem[]>([]);
const currentIndex = ref(-1);
const singleform = ref<{
	shopName: string;
	account: string;
	withDrawAmount: string | undefined;
	shopBalance: string | undefined;
	occurrenceTime: string;
	reason: string;
	aliPayAccount: string;
	id: string;
	aliPayAccountName: string;
	accountName: string;
	modifiedTime: string;
	status: string;
}>({
	shopName: '',
	account: '',
	withDrawAmount: undefined,
	shopBalance: undefined,
	reason: '',
	aliPayAccount: '',
	occurrenceTime: '',
	id: '',
	aliPayAccountName: '',
	accountName: '',
	modifiedTime: '',
	status: '',
});

const singlerules = {
	withDrawAmount: [{ required: true, message: '请输入提现金额', trigger: 'blur' }],
	occurrenceTime: [{ required: true, message: '请选择提现时间', trigger: 'blur' }],
};
const query = ref({
	startTime: '',
	endTime: '',
	platform: '',
	shopId: '',
	platformShopId: '',
	amountType: '',
	timeType: '',
	account: '',
	dataSource: '',
	status: '',
	maxAmount: undefined,
	minAmount: undefined,
	userName: '',
	userId: '',
	operatorUserName: '',
	ownTag: '',
	isCommit: '',
	accountName: '',
	aliPayAccountName: '',
});
const batchPayoutsInfo = ref<{
	shopName: string;
	dateTime: string;
}>({
	shopName: '',
	dateTime: '',
});

const pleaseEnterBlur = () => {
	if (!pleaseEnter.value) {
		return;
	}
	filteredRows.value = tableData.value.filter((item) => item.shopName.includes(pleaseEnter.value));
	currentIndex.value = filteredRows.value.length > 0 ? 0 : -1;
	if (currentIndex.value !== -1) {
		table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
	} else {
		ElMessage.warning('未找到匹配的行');
	}
};

const scrollUp = () => {
	if (!pleaseEnter.value || filteredRows.value.length === 0) {
		ElMessage.warning('没有可滚动的行');
		return;
	}
	if (currentIndex.value > 0) {
		currentIndex.value -= 1;
	} else {
		currentIndex.value = filteredRows.value.length - 1;
	}
	table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
};

const scrollDown = () => {
	if (!pleaseEnter.value || filteredRows.value.length === 0) {
		ElMessage.warning('没有可滚动的行');
		return;
	}
	if (currentIndex.value < filteredRows.value.length - 1) {
		currentIndex.value += 1;
	} else {
		currentIndex.value = 0;
	}
	table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
};

const accountChnene = (e: any, val: number) => {
	if (val == 1) {
		singleform.value.aliPayAccount = '';
	} else {
		singleform.value.account = '';
	}
};

const formatOccurrenceTime = (occurrenceTime: Date | string) => {
	if (occurrenceTime) {
		return dayjs(occurrenceTime).format('YYYY-MM-DD HH:mm:ss');
	}
	return '';
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (!valid) return;
		const formattedTime = formatOccurrenceTime(singleform.value.occurrenceTime);
		const { success } = await UpdateWithDrawInfoTemp({ ...singleform.value, occurrenceTime: formattedTime });
		if (success) {
			getList();
			if (singleform.value.aliPayAccount) {
				withdrawAlipay.value.forEach((item) => {
					if (item.value === singleform.value.aliPayAccount) {
						singleform.value.aliPayAccountName = item.label;
					}
				});
			} else {
				singleform.value.aliPayAccountName = '';
			}
			if (singleform.value.account) {
				nameList.value.forEach((item) => {
					if (item.value === singleform.value.account) {
						singleform.value.accountName = item.label;
					}
				});
			} else {
				singleform.value.accountName = '';
			}
			singleform.value.modifiedTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
			singleform.value.status = '已编辑';
			const index = tableData.value.findIndex((item) => item.id == singleform.value.id);
			if (index !== -1) {
				tableData.value[index] = { ...tableData.value[index], ...singleform.value };
			}
			const checkboxIndex = checkboxList.value.findIndex((item: any) => item.id == singleform.value.id);
			if (checkboxIndex !== -1) {
				checkboxList.value[checkboxIndex] = { ...checkboxList.value[checkboxIndex], ...singleform.value };
			}
			window.$message.success('编辑成功');
			table.value.onAssignedData(tableData.value);
			editVisible.value = false;
		}
	});
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const handleSubmit = () => {
	if (ruleFormRef.value) {
		submitForm(ruleFormRef.value);
	}
};
const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;

const formatTimeFields = () => {
	checkboxList.value = checkboxList.value.map((item) => {
		const fields = ['withDrawTime', 'occurrenceTime', 'modifiedTime', 'createdTime'];
		const newItem = { ...item };
		fields.forEach((field) => {
			const value = item[field];
			if (!value) return;
			// 如果不是标准格式就格式化
			if (typeof value === 'string' && !dateTimeRegex.test(value)) {
				const formatted = dayjs(value).isValid() ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : value;
				newItem[field] = formatted;
			}
		});
		return newItem;
	});
};

const onSubmitRegistration = async () => {
	if (checkboxList.value.length === 0) {
		ElMessage.warning('请选择要提交登记的数据');
		return;
	}
	ElMessageBox.confirm('是否提交登记?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			formatTimeFields();
			const { success } = await SyncWithDrawInfoTemp(checkboxList.value);
			if (success) {
				window.$message.success('提交登记成功');
				editVisible.value = false;
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	singleform.value.occurrenceTime = row.modifiedTime ? dayjs(row.modifiedTime).format('YYYY-MM-DD HH:mm:ss') : dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
	editVisible.value = true;
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'withDrawTime', title: '日期', width: '90', formatter: 'formatDate' },
	{ sortable: true, field: 'platform', title: '平台', width: '70', formatter: 'formatPlatform' },
	{ sortable: true, field: 'shopName', title: '店铺名' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID', width: '100' },
	{ sortable: true, field: 'accountName', title: '提现账户名(别名)', width: '140' },
	{ sortable: true, field: 'aliPayAccountName', title: '支付宝账号(别名)', width: '140' },
	{ sortable: true, field: 'shopBalance', title: '店铺余额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'withDrawAmount', title: '提现金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'dataSource', title: '提现类型', width: '95' },
	{ sortable: true, field: 'occurrenceTime', title: '操作时间', width: '140' },
	{ sortable: true, field: 'modifiedUserName', title: '操作人', width: '90' },
	{ sortable: true, field: 'ownTag', title: '店铺负责人', width: '100' },
	{ sortable: true, field: 'status', title: '状态', width: '75' },
	{ sortable: true, field: 'isCommit', title: '是否提交', width: '70', formatter: (row: any) => (row.isCommit ? '是' : '否') },
	{
		title: '操作',
		align: 'center',
		width: '90',
		type: 'btnList',
		minWidth: '90',
		direction: 'column',
		field:'**************',
		btnList: [{ title: '编辑', handle: onEdit, isDisabled: (row) => (row.isCommit == true ? true : false) }],
		fixed: 'right',
	},
]);

const changeWidth = () => {
	batchPayoutsWidth.value = 1580;
};

const batchPayouts = () => {
	batchPayoutsWidth.value = 600;
	batchPayoutsVisible.value = true;
};

const fetchShopList = async () => {
	const params = {
		platform: query.value.platform,
		currentPage: 1,
		pageSize: ********,
	};
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	withdrawAlipay.value = data1.list.filter((item: any) => item.bankType.trim() === '支付宝').map((item: any) => ({ label: item.accountName, value: item.account }));
	nameList.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银的数据');
		})
		.map((item: any) => ({ label: item.accountName, value: item.account }));
	alipayCashWithdrawal.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银');
		})
		.map((item: any) => ({
			label: item.accountName,
			value: item.account,
			bankType: item.bankType?.trim(),
		}));
};
const disposeProps = async (data: any, callback: Function) => {
	tableData.value = data.data.list;
	callback(data);
};
onMounted(() => {
	query.value.timeType = '日期';
	query.value.startTime = dayjs().format('YYYY-MM-DD');
	query.value.endTime = dayjs().format('YYYY-MM-DD');
	getAllDept();
	fetchShopList();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 90%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

.itemCss {
	width: 128px;
}
.inputboBor :deep(.el-input__wrapper) {
	border: 1px solid rgb(0, 0, 0, 0) !important;
	box-shadow: none !important;
}
</style>
