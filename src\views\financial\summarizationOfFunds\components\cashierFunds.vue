<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end"
					style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20250313094053" :tableCols="tableCols" showsummary isIndexFixed :query="query"
				:query-api="PageCNFund" :export-api="ExportCNFund" :asyncExport="{ title: '出纳资金', isAsync: false }"
				@footerCellClick="onSummaryTotalMap">
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="trendChart.totalMapVisible" width="60%" draggable overflow @close="lengendObject = {}">
		<div>
			<dataRange v-model:startDate="trendChart.start" v-model:endDate="trendChart.end" :clearable="false"
				startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 260px" @change="onTrendChartMethod()" />
			<lineChart v-if="trendChart.totalMapVisible" :chartData="trendChart.analysisData" ref="sumChart" :thisStyle="{
				width: '100%',
				height: '600px',
				'box-sizing': 'border-box',
				'line-height': '600px',
			}" @onLegendMethod="onLegendMethod" :lengendObject="lengendObject" />
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, nextTick } from 'vue';
import { PageCNFund, ExportCNFund } from '/@/api/financewh/funds';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { QueryCNFundCharAnalysis } from '/@/api/financewh/fundsAnalysis';
const query = ref({
	start: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
	platform: null,
});
const lengendObject = ref({});
const table = ref();
const sumChart = ref(); //总资产趋势图组件
const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

const onLegendMethod = (val: any) => {
  lengendObject.value = val
};

const onSummaryTotalMap = async (row: any, column: any) => {
	trendChart.value.columns = column;
	trendChart.value.end = query.value.end;
	trendChart.value.start = dayjs(trendChart.value.end).subtract(30, 'day').format('YYYY-MM-DD');
	onTrendChartMethod();
};

const onTrendChartMethod = async () => {
	const res = await QueryCNFundCharAnalysis({
		start: trendChart.value.start,
		end: trendChart.value.end,
		column: trendChart.value.columns,
	});
	trendChart.value.analysisData = res;
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'receiptDate', title: '日期', width: '90', formatter: 'formatDate' },
	{
		title: '收入',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'purRefundIn', title: '采购款退款收入', width: '130', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'expClaimsIn', title: '快递理赔收入', align: 'right', formatter: 'fmtAmt0', width: '115' },
			{ summaryEvent: true, sortable: true, field: 'otherIn', title: '其他收入', align: 'right', formatter: 'fmtAmt0', width: '90' },
			{ summaryEvent: true, sortable: true, field: 'totalIn', title: '收入合计', align: 'right', formatter: 'fmtAmt0', width: '90', color: 'red' },
		],
	},
	{
		title: '支出',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'purchaseOut', title: '采购款', align: 'right', formatter: 'fmtAmt0', width: '80' },
			{ summaryEvent: true, sortable: true, field: 'expressOut', title: '快递费', align: 'right', formatter: 'fmtAmt0', width: '80' },
			{ summaryEvent: true, sortable: true, field: 'marketingOut', title: '营销费用', align: 'right', formatter: 'fmtAmt0', width: '90' },
			{ summaryEvent: true, sortable: true, field: 'dailyOut', title: '日常支出', align: 'right', formatter: 'fmtAmt0', width: '90' },
			{ summaryEvent: true, sortable: true, field: 'wagesOut', title: '工资', align: 'right', formatter: 'fmtAmt0', width: '70' },
			{ summaryEvent: true, sortable: true, field: 'totalOut', title: '支出合计', align: 'right', formatter: 'fmtAmt0', width: '90', color: 'red' },
		],
	},
	{
		title: '支出中包含以下数据',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'independentAccounting', title: '独立核算', align: 'right', formatter: 'fmtAmt0', width: '90' },
			{ summaryEvent: true, sortable: true, field: 'crossPurchaseFund', title: '跨境采购款+日常费用', width: '200', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{ sortable: true, field: 'updateTime', title: '更新时间', align: 'right', formatter: 'formatTime' },
]);
</script>

<style scoped lang="scss"></style>
