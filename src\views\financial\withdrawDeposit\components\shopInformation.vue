<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.platform" placeholder="平台" class="publicCss" clearable filterable @change="fetchShopList">
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model.trim="query.shopId" placeholder="店铺" class="publicCss" clearable filterable>
					<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.platformShopId" placeholder="平台店铺ID" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.account" placeholder="提现银行卡号" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.accountUserName" placeholder="提现账户名" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.dataSource" placeholder="提现类型" class="publicCss" clearable filterable>
					<el-option key="RPA" label="RPA" value="RPA" />
					<el-option key="人工" label="人工" value="人工" />
				</el-select>
				<el-select v-model="query.enabled" placeholder="启用" class="publicCss" clearable filterable>
					<el-option key="启用" label="启用" :value="true" />
					<el-option key="禁用" label="禁用" :value="false" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="getStoreInformation" type="primary">获取店铺信息</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				keyField="shopId"
				:pageSize="50"
				id="shopInformation202411151153"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				:query="query"
				:isAsc="false"
				:query-api="QueryWithDrawShopInfo"
			>
				<template #toolbar_buttons>
					<el-button @click="startImport" type="primary">导入</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<el-button @click="downLoadMb" type="primary">下载导入模版</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="编辑" width="450" draggable overflow>
		<div style="padding-top: 10px">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="店铺名" :label-width="'100px'">
					{{ singleform.shopName }}
				</el-form-item>
				<el-form-item label="平台店铺ID" :label-width="'100px'">
					{{ singleform.platformShopId }}
				</el-form-item>
				<el-form-item label="提现信息" :label-width="'100px'">
					<el-select v-model="singleform.account" placeholder="提现信息" class="btnGroup" clearable filterable>
						<el-option v-for="item in accountList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="提现类型" :label-width="'100px'" prop="dataSource">
					<el-select v-model="singleform.dataSource" placeholder="提现类型" class="btnGroup" clearable filterable>
						<el-option key="RPA" label="RPA" value="RPA" />
						<el-option key="人工" label="人工" value="人工" />
					</el-select>
				</el-form-item>
				<el-form-item label="店铺负责人" :label-width="'100px'">
					<el-input v-model.trim="singleform.ownTag" placeholder="店铺负责人" class="btnGroup" clearable maxlength="10" />
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave(ruleFormRef)"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important">
		<div style="height: 100px">
			<el-upload
				ref="uploadFile"
				class="upload-demo"
				:auto-upload="false"
				:multiple="false"
				:limit="1"
				action=""
				accept=".xlsx"
				:file-list="fileLists"
				:data="fileparm"
				:http-request="onUploadFile"
				:on-success="onUploadSuccess"
				:on-change="onUploadChange"
				:on-remove="onUploadRemove"
			>
				<template #trigger>
					<el-button size="small" type="primary">选取文件</el-button>
				</template>
				<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
			</el-upload>
		</div>
		<div style="display: flex; justify-content: end; align-items: center">
			<el-button @click="dialogVisible = false">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import {
	QueryWithDrawShopInfo,
	UpdateWithDrawShopInfo,
	ExportWithDrawShopInfo,
	GetWithDrawShopList,
	UpdateWithDrawShopInfoStatus,
	SyncWithDrawShopInfo,
	ImportWithDrawShopBankCard,
} from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { bankList, platformlist, expressCompany, downLoadFile } from '/@/utils/tools';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const dialogVisible = ref(false);
const fileLists = ref([]);
const fileparm = ref({});
const uploadLoading = ref(false);
const uploadFile = ref();
const nameList = ref<Public.options[]>([]);
const accountList = ref<Public.options[]>([]);
const shopNamelist = ref<Public.options[]>([]);
const ruleFormRef = ref<FormInstance>();
const table = ref();
const singleform = ref<{
	shopName: string;
	reason: string;
	platformShopId: string;
	accountUserName: string;
	dataSource: string;
	ownTag: string;
	account: string;
}>({
	shopName: '',
	reason: '',
	platformShopId: '',
	accountUserName: '',
	dataSource: '',
	account: '',
	ownTag: '',
});

const singlerules = {
	account: [{ required: true, message: '请选择提现银行卡号', trigger: 'blur' }],
	dataSource: [{ required: true, message: '请选择提现类型', trigger: 'blur' }],
};
const query = ref({
	platform: '',
	shopName: '',
	shopId: '',
	platformShopId: '',
	account: '',
	accountUserName: '',
	dataSource: '',
	enabled: '',
	ownTag: '',
});

const downLoadMb = () => {
	window.open('/excel/cwManage/店铺信息导入模版.xlsx', '_self');
};

const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	var res = await ImportWithDrawShopBankCard(form);
	if (res?.success) window.$message({ message: '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
//导入弹窗
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
};

const getStoreInformation = async () => {
	ElMessageBox.confirm('此操作将获取店铺信息', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await SyncWithDrawShopInfo({});
			if (success) {
				ElMessage.success('获取店铺信息成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({ type: 'info', message: '已取消' });
		});
};

const onSingleSave = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			const { success } = await UpdateWithDrawShopInfo({ ...singleform.value });
			if (success) {
				window.$message.success('编辑成功');
				editVisible.value = false;
				getList();
			}
		} else {
			ElMessage.error('表单验证失败!');
		}
	});
};

const exportProps = async () => {
	await ExportWithDrawShopInfo({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onDisable = (row: any) => {
	let title = row.enabled == false ? '启用' : '禁用';
	ElMessageBox.confirm('此操作将' + title + '此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await UpdateWithDrawShopInfoStatus({ ...row, enabled: row.enabled == false ? true : false });
			if (success) {
				ElMessage.success(title + '成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({ type: 'info', message: '已取消' });
		});
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	editVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'platform', title: '平台', width: '90', formatter: 'formatPlatform' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '300' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID' },
	{ sortable: true, field: 'account', title: '提现银行卡号' },
	{ sortable: true, field: 'bankType', title: '银行行别' },
	{ sortable: true, field: 'accountUserName', title: '提现账户名' },
	{ sortable: true, field: 'dataSource', title: '提现类型' },
	{ sortable: true, field: 'ownTag', title: '店铺负责人' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '120',
		field: '**************',
		btnList: [
			{ title: '编辑', handle: onEdit },
			{ title: '启用', handle: onDisable, isDisabled: (row) => row.enabled == true },
			{ title: '禁用', handle: onDisable, isDisabled: (row) => row.enabled == false },
		],
	},
]);

const fetchShopList = async () => {
	const params = {
		platform: query.value.platform,
		currentPage: 1,
		pageSize: ********,
	};
	query.value.shopId = '';
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};

const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list
		.filter((item: any) => {
			if (!item.showUserName || !item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银的数据');
		})
		.map((item: any) => ({ label: item.showUserName, value: item.account }));

	accountList.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银的数据');
		})
		.map((item: any) => ({ label: `${item.account}-${item.bankType}-${item.showUserName}`, value: item.account }));
};
onMounted(() => {
	getAllDept();
	fetchShopList();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 90%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
</style>
