<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-select v-model="query.platformList" placeholder="平台" class="publicCss" style="width: 200px" clearable filterable multiple collapse-tags collapse-tags-tooltip @change="onchangeplatform">
					<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model.trim="query.shopIdList" placeholder="店铺" class="publicCss" style="width: 200px" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in shopNamelist" :key="item.shopId" :label="item.shopName" :value="item.shopId" />
				</el-select>
				<el-input v-model.trim="query.feeType" class="publicCss" placeholder="扣点项目" clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="onAddMethod" type="primary">新增</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable showsummary ref="table" id="deductionPointSetting202502161137" :tableCols="tableCols" :pageSize="50" :query="query" :isAsc="false" :isNeedPager="false" :queryApi="GetFeeSetting">
				<template #toolbar_buttons> </template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="logsVisible" title="日志" width="1000" draggable overflow style="margin-top: -5svh !important">
		<div style="height: 400px">
			<vxetable
				v-if="logsVisible"
				showsummary
				ref="table"
				id="logsVisibleSetting202502161137"
				:tableCols="tableColslogs"
				:pageSize="50"
				:query="logsquery"
				:isAsc="false"
				orderBy="lastUpdateTime"
				:isNeedPager="false"
				:queryApi="GetFeeSettingHistory"
			>
				<template #toolbar_buttons> </template>
			</vxetable>
		</div>
	</el-dialog>

	<el-dialog v-model="editVisible" :title="editTitle ? '编辑扣点项目' : '新增扣点项目'" width="400" draggable overflow @close="handleClose" style="margin-top: -25vh !important">
		<div v-loading="formLoading">
			<el-form label-width="auto" :model="ruleForm" :rules="rules" style="max-width: 600px" ref="ruleFormRef">
				<el-form-item label="平台:" label-position="right" prop="platform">
					<el-select v-model="ruleForm.platform" placeholder="请选择平台" class="publicCss" clearable filterable @change="fetchShopList">
						<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="店铺名称:" label-position="right" prop="shopId">
					<el-select v-model.trim="ruleForm.shopId" placeholder="请选择店铺名称" class="publicCss" clearable filterable>
						<el-option v-for="item in editShopNamelist" :key="item.shopId" :label="item.shopName" :value="item.shopId" />
					</el-select>
				</el-form-item>
				<el-form-item label="扣点项目:" label-position="right" prop="feeType">
					<el-input v-model.trim="ruleForm.feeType" class="publicCss" placeholder="请输入扣点项目" clearable maxlength="50" />
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, computed, nextTick } from 'vue';
import { GetFeeSetting, GetFeeSettingHistory, EditFeeSetting, SetFeeSetting, DeleteFeeSetting } from '/@/api/cwManager/cwFundsDailyBalance';
import { GetWithDrawShopList } from '/@/api/cwManager/withDrawInfo';
import { ElMessageBox } from 'element-plus';
import { platformlist } from '/@/utils/tools';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const pageLoading = ref(false);
const timeRange = ref('');
const table = ref();
const logsVisible = ref(false);
const editTitle = ref(false);
const editShopNamelist = ref<any>([]);
interface Query {
	startDate: string;
	endDate: string;
	feeType: string;
	platformList: any[]; // 根据实际数据定义类型
	shopIdList: string[];
}

const logsquery = ref({
	batchNumber: '',
});

const query = ref<Query>({
	startDate: '',
	endDate: '',
	feeType: '',
	platformList: [],
	shopIdList: [],
});
const shopNamelist = ref<any>([]);
const ruleFormRef = ref();
const formLoading = ref(false);
const editVisible = ref(false);
const ruleForm = ref({
	platform: '', //平台
	shopId: '', //店铺
	feeType: '', //扣点项目
});
const rules = {
	platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
	shopId: [{ required: true, message: '请选择店铺', trigger: 'change' }],
	feeType: [{ required: true, message: '请输入扣点项目', trigger: 'blur' }],
};

const onEditMethod = (row: any) => {
	handleClose();
	ruleForm.value = JSON.parse(JSON.stringify(row));
	editTitle.value = true;
	editVisible.value = true;
	setTimeout(() => {
		fetchShopList(ruleForm.value.platform || null);
	}, 100);
};

const onAddMethod = () => {
	handleClose();
	editTitle.value = false;
	editVisible.value = true;
};

const onchangeplatform = async (val: any) => {
	if (val.length == 0) {
		shopNamelist.value = [];
		query.value.shopIdList = [];
		fetchShopList(null);
		return;
	}
	//获取店铺信息
	val.forEach((item: any) => {
		if (shopNamelist.value.length > 0) {
			const isItemInShopList = shopNamelist.value.some((shop: any) => shop.platform === item);
			if (!isItemInShopList) {
				fetchShopList(item);
			}
		} else {
			fetchShopList(item);
		}
	});
	//删除this.shopList中val中没有的
	shopNamelist.value = shopNamelist.value.filter((shop: any) => val.includes(shop.platform));
	//判断是否有选中的店铺
	if (shopNamelist.value.length > 0) {
		const isItemInShopList = shopNamelist.value.some(
			(shop: any) => query.value.shopIdList.includes(String(shop.shopId)) // 强制转换为字符串
		);
		if (!isItemInShopList) {
			query.value.shopIdList = [];
		}
	}
};

const fetchShopList = async (e: any) => {
	const params = {
		platform: e,
		currentPage: 1,
		pageSize: 10000000,
	};
	const { data, success } = await GetWithDrawShopList(params);
	if (editVisible.value) {
		ruleForm.value.shopId = ruleForm.value.platform != e ? '' : ruleForm.value.shopId ? ruleForm.value.shopId : '';
		editShopNamelist.value = data;
	} else {
		shopNamelist.value = shopNamelist.value.concat(data).map((item: any) => ({
			shopName: item.shopName,
			shopId: item.shopId,
			platform: item.platform ? item.platform : e ? e : null,
		}));
	}
};

const filteredPlatformList = computed(() => {
	return platformlist.filter((item) => item.label !== '未知');
});

const onDeleteMethod = async (row: any) => {
	ElMessageBox.confirm('是否删除该数据?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await DeleteFeeSetting({ batchNumber: row.batchNumber });
			if (success) {
				window.$message.success('删除成功');
				table.value.getList();
			}
		})
		.catch(() => {});
};

const onLogMethod = async (row: any) => {
	if (!row.batchNumber) {
		window.$message.error('暂无该批次');
		return;
	}
	logsquery.value.batchNumber = row.batchNumber;
	logsVisible.value = true;
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
	editVisible.value = false;
	logsVisible.value = false;
	ruleForm.value = {
		platform: '', //平台
		shopId: '', //店铺
		feeType: '', //扣点项目
	};
};

const handleSubmit = () => {
	if (ruleFormRef.value) {
		submitForm(ruleFormRef.value);
	}
};

const submitForm = async (formEl: any | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid: any, fields: any) => {
		if (!valid) return;
		let success = false;
		formLoading.value = true;
		const response = editTitle.value ? await EditFeeSetting({ ...ruleForm.value }) : await SetFeeSetting({ ...ruleForm.value });
		formLoading.value = false;
		success = response.success;
		if (success) {
			window.$message.success('操作成功');
			editVisible.value = false;
			getList();
		}
	});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'createTime', title: '创建时间', width: '140' },
	{ sortable: true, field: 'platform', title: '平台', width: '110', formatter: 'formatPlatform' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '140' },
	{ sortable: true, field: 'feeType', title: '扣点项目', width: '140' },
	{ sortable: true, field: 'lastUpdateTime', title: '最后更新时间', width: '140' },
	{ sortable: true, field: 'lastUpdateUser', title: '最后更新人', width: '140' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '110',
		fixed: 'right',
		btnList: [
			{ title: '编辑', handle: onEditMethod },
			{ title: '日志', handle: onLogMethod },
			{ title: '删除', handle: onDeleteMethod },
		],
	},
]);

const tableColslogs = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'createTime', title: '创建时间', width: '140' },
	{ sortable: true, field: 'platform', title: '平台', width: '110', formatter: 'formatPlatform' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: 'auto' },
	{ sortable: true, field: 'feeType', title: '扣点项目', width: '140' },
	{ sortable: true, field: 'lastUpdateTime', title: '最后更新时间', width: '140' },
	{ sortable: true, field: 'lastUpdateUser', title: '最后更新人', width: '120' },
]);

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(0, 'day').format('YYYY-MM-DD');
		query.value.startDate = timeRange.value;
		query.value.endDate = timeRange.value;
	}
	fetchShopList(null);
});
</script>

<style scoped lang="scss">
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

::v-deep .el-select__tags-text {
	max-width: 65px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}
</style>
