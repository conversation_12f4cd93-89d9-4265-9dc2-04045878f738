<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.toAccountName" class="publicCss" placeholder="对方名称" clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="************" :tableCols="tableCols" :query="query" isNeedDisposeProps @disposeProps="disposeProps" :queryApi="QueryRevenueAccount">
				<template #toolbar_buttons>
					<el-button @click="handleAdd(null)" type="primary">新增</el-button>
					<el-button @click="startImport" type="primary">导入</el-button>
					<el-button @click="onImportTemplate" type="primary">下载导入模版</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="addVisible" :title="isEdit ? '编辑' : '新增'" width="20%" draggable overflow :close-on-click-modal="false">
		<el-form label-width="auto" :model="ruleForm" :rules="rules" style="max-width: 600px">
			<el-form-item label="对方账号:" label-position="right" prop="toAccount">
				<el-input v-model.trim="ruleForm.toAccount" class="publicCss" placeholder="请输入收款账号" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="对方名称:" label-position="right" prop="toAccountName">
				<el-input v-model.trim="ruleForm.toAccountName" class="publicCss" placeholder="请输入对方名称" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="费用类型:" label-position="right" prop="feeType">
				<el-input v-model.trim="ruleForm.feeType" class="publicCss" placeholder="请输入费用类型" clearable maxlength="50" />
			</el-form-item>
		</el-form>
		<div style="display: flex; justify-content: center; margin-top: 20px">
			<el-button @click="addVisible = false">取消</el-button>
			<el-button type="primary" @click="Submit" v-reclick="1000">保存</el-button>
		</div>
	</el-dialog>

	<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important;">
		<div style="height: 100px">
			<el-upload
				ref="uploadFile"
				class="upload-demo"
				:auto-upload="false"
				:multiple="false"
				:limit="1"
				action=""
				accept=".xlsx"
				:file-list="fileLists"
				:data="fileparm"
				:http-request="onUploadFile"
				:on-success="onUploadSuccess"
				:on-change="onUploadChange"
				:on-remove="onUploadRemove"
			>
				<template #trigger>
					<el-button size="small" type="primary">选取文件</el-button>
				</template>
				<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
			</el-upload>
		</div>
		<div style="display: flex; justify-content: end; align-items: center">
			<el-button @click="dialogVisible = false">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, reactive } from 'vue';
import { ElMessageBox, FormRules, ElUpload } from 'element-plus';
import { InsertOrUpdateRevenueAccount, QueryRevenueAccount, ImportRevenueAccount, ExportTotalReportInAmount, DeleteRevenueAccount } from '/@/api/cwManager/cashierSet';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dialogVisible = ref(false);
const fileLists = ref([]);
const fileparm = ref({});
const uploadLoading = ref(false);
const uploadFile = ref();
const addVisible = ref(false);
const isEdit = ref(false);
const table = ref();
interface ExpenseType {
	toAccount: string;
}
const tableData = ref<ExpenseType[]>([]);
const query = ref({
	toAccountName: '',
});
const ruleForm = ref({
	toAccount: '',
	toAccountName: '',
	feeType: '',
});
const rules = ref<FormRules>({
	feeType: [{ required: true, message: '请输入费用类型', trigger: 'blur' }],
	toAccount: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
	toAccountName: [{ required: true, message: '请输入对方名称', trigger: 'blur' }],
});
const clear = () => {
	ruleForm.value = {
		toAccount: '',
		toAccountName: '',
		feeType: '',
	};
};
const exportProps = async () => {
	await ExportTotalReportInAmount({ ...query.value })
    window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};


const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	var res = await ImportRevenueAccount(form);
	if (res?.success) window.$message({ message: '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
//导入弹窗
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
};

const onImportTemplate = () => {
	const aLink = document.createElement('a');
	aLink.href = '/excel/cwManage/收入类型设置导入模版.xlsx';
	aLink.setAttribute('download', '收入类型设置导入模版.xlsx');
	document.body.appendChild(aLink);
	aLink.click();
	document.body.removeChild(aLink);
};

const handleAdd = (row: any) => {
	clear();
	isEdit.value = false;
	addVisible.value = true;
};
const edit = async (row: any) => {
	clear();
	isEdit.value = true;
	ruleForm.value = row ? JSON.parse(JSON.stringify(row)) : ruleForm.value;
	addVisible.value = true;
};
const handleDelete = async (row: any) => {
	ElMessageBox.confirm('此操作将删除该数据,是否继续?', '提示消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await DeleteRevenueAccount(row);
			if (success) {
				window.$message.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消删除');
		});
};
const Submit = async () => {
	let verify = false;
	if (!isEdit.value) {
		// for (let i = 0; i < tableData.value.length; i++) {
		// 	if (ruleForm.value.toAccount == tableData.value[i].toAccount) {
		// 		window.$message.error('该账号已存在');
		// 		verify = true;
		// 		break;
		// 	}
		// }
	}
	if (verify) return;
	if (!ruleForm.value.toAccount || !ruleForm.value.toAccountName || !ruleForm.value.feeType) {
		window.$message.error('请填写完整信息');
		return;
	}
	const { success } = await InsertOrUpdateRevenueAccount(ruleForm.value);
	if (success) {
		addVisible.value = false;
		window.$message.success(isEdit.value ? '保存成功' : '新增成功');
		getList();
	}
};
const getList = () => {
	table.value.getList();
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'toAccount', title: '对方账号' },
	{ sortable: true, field: 'toAccountName', title: '对方名称' },
	{ sortable: true, field: 'feeType', title: '费用类型' },
	{ sortable: true, field: 'modifiedUserName', title: '添加人' },
	{ sortable: true, field: 'modifiedTime', title: '添加时间' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		btnList: [
			{ title: '编辑', handle: edit },
			{ title: '删除', handle: handleDelete },
		],
	},
]);
const disposeProps = async (data: any, callback: Function) => {
	tableData.value = data.data.list;
	callback(data);
};
</script>

<style scoped lang="scss"></style>
