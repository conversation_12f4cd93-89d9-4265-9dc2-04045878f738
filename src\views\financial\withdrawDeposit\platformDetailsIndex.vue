<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="提现网银日报表" name="first" style="height: 100%">
					<onlineBankingDaily />
				</el-tab-pane>
				<el-tab-pane label="公司账户余额" name="third" style="height: 100%" lazy>
					<companyAccountBalance />
				</el-tab-pane>
				<el-tab-pane label="流水数据" name="sixth" style="height: 100%" lazy>
					<turnoverDetailIndex />
				</el-tab-pane>
				<el-tab-pane label="提现信息" name="fourth" style="height: 100%" lazy>
					<withdrawalIndex />
				</el-tab-pane>
				<el-tab-pane label="店铺信息" name="fifth" style="height: 100%" lazy>
					<shopInformation />
				</el-tab-pane>
				<el-tab-pane label="提现网银月账单" name="second" style="height: 100%" lazy>
					<onlineBankingMonthly :expenseTypeList="expenseTypeList" />
				</el-tab-pane>
				<el-tab-pane label="月账单导入" name="seventh" style="height: 100%" lazy>
					<monthlyBillImport :expenseTypeList="expenseTypeList" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from 'vue';
const onlineBankingMonthly = defineAsyncComponent(() => import('./components/onlineBankingMonthly.vue'));
const withdrawalIndex = defineAsyncComponent(() => import('./components/withdrawalIndex.vue'));
const companyAccountBalance = defineAsyncComponent(() => import('./components/companyAccountBalance.vue'));
const onlineBankingDaily = defineAsyncComponent(() => import('./components/onlineBankingDaily.vue'));
const shopInformation = defineAsyncComponent(() => import('./components/shopInformation.vue'));
const turnoverDetailIndex = defineAsyncComponent(() => import('./components/turnover/turnoverDetailIndex.vue'));
const monthlyBillImport = defineAsyncComponent(() => import('./components/monthlyBillImport.vue'));
const activeName = ref('first');
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
const expenseTypeList = ref<any[]>([]);
onMounted(async () => {
	const { data, success } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success) return;
	expenseTypeList.value = data.list;
});
</script>
