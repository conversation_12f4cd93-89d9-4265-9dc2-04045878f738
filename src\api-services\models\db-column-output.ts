/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface DbColumnOutput
 */
export interface DbColumnOutput {

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    tableName?: string | null;

    /**
     * @type {number}
     * @memberof DbColumnOutput
     */
    tableId?: number;

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    dbColumnName?: string | null;

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    propertyName?: string | null;

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    dataType?: string | null;

    /**
     * @type {any}
     * @memberof DbColumnOutput
     */
    propertyType?: any | null;

    /**
     * @type {number}
     * @memberof DbColumnOutput
     */
    length?: number;

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    columnDescription?: string | null;

    /**
     * @type {string}
     * @memberof DbColumnOutput
     */
    defaultValue?: string | null;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isNullable?: boolean;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isIdentity?: boolean;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isPrimarykey?: boolean;

    /**
     * @type {any}
     * @memberof DbColumnOutput
     */
    value?: any | null;

    /**
     * @type {number}
     * @memberof DbColumnOutput
     */
    decimalDigits?: number;

    /**
     * @type {number}
     * @memberof DbColumnOutput
     */
    scale?: number;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isArray?: boolean;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isJson?: boolean;

    /**
     * @type {boolean}
     * @memberof DbColumnOutput
     */
    isUnsigned?: boolean | null;

    /**
     * @type {number}
     * @memberof DbColumnOutput
     */
    createTableFieldSort?: number;
}
