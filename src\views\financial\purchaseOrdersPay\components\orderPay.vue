<template>
	<div v-loading="pageLoading">
		<div>
			<div class="mb20">
				支付链接: <el-link type="danger" :href="data.payUrl" target="_blank">{{ data.payUrl }}</el-link>
			</div>
			<div style="display: flex">
				<div class="mr20">支付金额:{{ data.totalAmount }}</div>
				<div class="mr20">采购数量:{{ data.totalCount }}/30</div>
				<div class="mr20">账号:{{ data.payAccount }}</div>
			</div>
		</div>
		<div style="height: 570px" v-if="data.lists.length > 0">
			<vxetable id="**************" :tableCols="tableCols" height="90%" :data="data.lists" :isNeedQueryApi="false" :remote="false" :isNeedPager="false" />
		</div>
		<div class="btnGroup">
			<el-button @click="handleClose">取消</el-button>
			<el-button type="success" @click="payAndApproved(1)">已付款&审核通过</el-button>
			<el-button type="primary" @click="payAndApproved(2)" class="custom-button"> 已审核待确认 </el-button>
		</div>
	</div>

	<el-dialog v-model="chooseBankVisible" append-to-body title="选择银行" width="15%" draggable :close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
		<el-form ref="formRef" style="max-width: 600px" :model="bankForm" label-width="auto" class="demo-ruleForm">
			<el-form-item label="银行:" prop="onlineBankId">
				<el-select v-model="bankForm.onlineBankId" filterable remote reserve-keyword placeholder="卡号" :remote-method="remoteMethod" :loading="remoteLoading" style="width: 200px">
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="图片" prop="age" v-if="data.lists.length == 1 && verify">
				<uploadMf v-model:imagesStr="bankForm.images" :upstyle="{ height: 40, width: 40 }" :limit="9" v-if="chooseBankVisible" />
			</el-form-item>
		</el-form>
		<div class="btnGruop1">
			<el-button @click="chooseBankVisible = false" type="primary">取消</el-button>
			<el-button @click="handleAgree" type="primary" v-reclick="1000">确定</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { GetPurchaseOrdersProcessAliOrderUrl, PassWaitPayPurchaseOrdersProcess, PassConfirmWaitPayPurchaseOrdersProcess } from '/@/api/cwManager/processPayOrApproved';
import { ElMessageBox, ElLoading } from 'element-plus';
import { QueryOnlineBankSet, QueryOnlineBankSetSelect } from '/@/api/cwManager/cashierSet';
const props = defineProps({
	instanceIds: { type: Array, default: () => [] },
});
const emits = defineEmits(['close', 'getList']);
const data = ref<any>({
	payUrl: '',
	totalAmount: '',
	totalCount: '',
	payAccount: '',
	lists: [],
});
const bankForm = ref<{
	onlineBankId: string | number;
	images: string[];
}>({
	onlineBankId: '',
	images: [],
});
const chooseBankVisible = ref(false);
const onlineBankId = ref();
const bankList = ref<Public.options[]>([]);
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const payLink = ref('');
const ids = ref([]);
const verify = ref(false);
const pageLoading = ref(false);
const remoteLoading = ref(false);
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'status', title: '状态' },
	{ sortable: true, field: 'isOrderAmountSame', title: '匹配状态', formatter: (row: any) => (row.isOrderAmountSame == 1 ? '正常' : row.isOrderAmountSame == 0 ? '异常' : '') },
	{ sortable: true, field: 'businessId', title: '审批编码' },
	{ sortable: true, field: 'indexNo', title: '采购单号' },
	{ sortable: true, field: 'payType', title: '支付方式' },
	{ sortable: true, field: 'orderNo', title: '订单编号' },
	{ sortable: true, field: 'amount', title: '金额' },
	{ sortable: true, field: 'payAccount', title: '账号' },
]);
const handleClose = () => {
	ElMessageBox.confirm('确定关闭吗?')
		.then(() => {
			emits('close');
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const submitCreateLink = async () => {
	pageLoading.value = true;
	try {
		const { data: data1, success } = await GetPurchaseOrdersProcessAliOrderUrl({ instanceIds: props.instanceIds });
		if (success) {
			data.value = data1;
			await remoteMethod('');
			let obj;
			obj = bankList.value.filter((item: any) => item.name == data.value?.lists[0].payAccount)[0];
			bankForm.value.onlineBankId = obj ? obj.value : '';
			ids.value = data.value?.lists.map((item: any) => item.instanceId) || [];
		}
	} catch (error) {
		pageLoading.value = false;
	} finally {
		pageLoading.value = false;
	}
};

const payAndApproved = async (val: number) => {
	if (val == 1) {
		verify.value = true;
	} else {
		bankForm.value.images = [];
		verify.value = false;
	}
	chooseBankVisible.value = true;
};
const handleAgree = async () => {
	if (!bankForm.value.onlineBankId) return window.$message.error('请选择银行');
	const loadingInstance1 = ElLoading.service({ fullscreen: true });
	let success;
	if (verify.value) {
		const response = await PassWaitPayPurchaseOrdersProcess({
			instanceIds: ids.value,
			type: 'agree',
			onlineBankId: bankForm.value.onlineBankId,
			images: bankForm.value.images,
		});
		success = response.success;
	} else {
		const response = await PassConfirmWaitPayPurchaseOrdersProcess({
			instanceIds: ids.value,
			onlineBankId: bankForm.value.onlineBankId,
		});
		success = response.success;
	}
	if (success) {
		window.$message.success('审核成功');
		chooseBankVisible.value = false;
		emits('close');
		emits('getList');
	}
	loadingInstance1.close();
};
const remoteMethod = async (account: string) => {
	// if (!account) return;
	remoteLoading.value = true;
	const { data: data1, success } = await QueryOnlineBankSetSelect({ account, accountName: data.value?.lists[0].payAccount });
	if (success) {
		bankList.value = data1.map((item: any) => {
			return {
				label: item.accountName + '-' + item.bankType + '-' + item.account,
				value: item.id,
				name: item.busAccountName,
			};
		});
		remoteLoading.value = false;
	} else {
		bankList.value = [];
		remoteLoading.value = false;
	}
};
onMounted(() => {
	submitCreateLink();
});
</script>

<style scoped lang="scss">
.btnGroup {
	display: flex;
	justify-content: end;
}
.btnGruop1 {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}
/* 默认状态 */
.custom-button {
	background-color: #409eff; /* 自定义默认背景色 */
	color: #fff; /* 自定义默认文字颜色 */
}
/* 鼠标悬停状态 */
.custom-button:hover {
	background-color: #66b1ff; /* 自定义hover背景色 */
}
.custom-button:active {
	background-color: #3a8ee6; /* 自定义按下时的背景色 */
}
</style>
