<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange
					v-model:startDate="query.startDate"
					v-model:endDate="query.endDate"
					class="publicCss"
					:clearable="false"
					startPlaceholder="开始时间"
					endPlaceholder="结束时间"
					style="width: 230px"
				/>
				<el-input v-model.trim="query.platformShopId" placeholder="平台店铺ID" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.shopIdList" placeholder="店铺" class="publicCss" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" style="width: 200px">
					<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.status" placeholder="请选择状态" class="publicCss" style="width: 170px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in statusList" :key="item" :label="item" :value="item"></el-option>
				</el-select>
				<el-select v-model="query.errorReason" placeholder="店铺违规状态" class="publicCss" style="width: 170px" clearable filterable>
					<el-option label="违规店铺" value="违规店铺"></el-option>
					<el-option label="非违规店铺" value="非违规店铺"></el-option>
				</el-select>
				<el-select v-model="query.checkStatus" placeholder="验算" clearable class="publicCss">
					<el-option label="验算正确" value="验算正确" />
					<el-option label="验算错误" value="验算错误" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="pullProps" type="primary">拉取</el-button>
					<el-button @click="Payouts" type="primary">提现</el-button>
					<el-button @click="check" type="primary">验算</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<div class="container-relative">
				<div class="radio-group-absolute">
					<el-radio-group v-model="radioValue" size="small" @change="getList">
						<el-radio-button label="货款余额" value="货款余额" />
						<el-radio-button label="推广账户" value="推广账户" />
						<el-radio-button label="保证金账户" value="保证金账户" />
					</el-radio-group>
					<el-button @click="exportProps('root')" type="primary" class="ml5" :disabled="rootDisabled">导出</el-button>
					<el-button @click="onLockUnlock('lock')" type="primary" class="ml5" :disabled="rootDisabled">锁定</el-button>
					<el-button @click="onLockUnlock('unlock')" type="primary" class="ml5" :disabled="rootDisabled">解锁</el-button>
					<span class="shop-text" :title="shopWithDraw">{{ shopWithDraw }}</span>
				</div>
				<vxetable
					showsummary
					ref="table"
					v-if="radioValue === '货款余额'"
					id="platformFundPdd2025021210571"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'withDrawAmountBalance' && col.field !== 'shopDepositBalance' && col.field !== 'activityDepositBalance' && col.field !== 'balance' && col.field !== 'netIncomeOutcome'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetPddDailyBalance"
					@footerCellClick="onSummaryTotalMap"
					isNeedDisposeProps
					@disposeProps="disposeProps"
				>
					<template #endBalance="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="endBalance" />
					</template>
					<template #goodsIncome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="goodsIncome" />
					</template>
					<template #fee="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="fee" />
					</template>
					<template #totalIncome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalIncome" :min="0" :max="99999999" />
					</template>
					<template #totalOutcome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalOutcome" :min="-99999999" :max="0" />
					</template>
				</vxetable>
				<vxetable
					showsummary
					ref="table1"
					v-if="radioValue === '推广账户'"
					id="platformFundPdd2025021210572"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'openingBalance' &&
								col.field !== 'endBalance' &&
								col.field !== 'realTimeBalance' &&
								col.field !== 'availableBalance' &&
								col.field !== 'frozenAmount' &&
								col.field !== 'withDrawAmount' &&
								col.field !== 'fee' &&
								col.field !== 'shopDepositBalance' &&
								col.field !== 'activityDepositBalance' &&
								col.field !== 'goodsIncome' &&
								col.field !== 'oldFrozenAmount' &&
								col.field !== 'addFrozenAmount' &&
								col.field !== 'promotionalFeeAmount' &&
								col.field !== 'promotionalFeeRecharge' &&
								col.field !== 'reduceFrozenAmount' &&
								col.field !== 'checkStatus' &&
								col.field !== 'netCashFlow'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetPddMarketingAccount"
					@footerCellClick="onSummaryTotalMap"
				>
				</vxetable>
				<vxetable
					showsummary
					ref="table2"
					v-if="radioValue === '保证金账户'"
					id="platformFundPdd2025021210573"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'openingBalance' &&
								col.field !== 'endBalance' &&
								col.field !== 'availableBalance' &&
								col.field !== 'frozenAmount' &&
								col.field !== 'withDrawAmount' &&
								col.field !== 'fee' &&
								col.field !== 'realTimeBalance' &&
								col.field !== 'balance' &&
								col.field !== 'withDrawAmountBalance' &&
								col.field !== 'goodsIncome' &&
								col.field !== 'oldFrozenAmount' &&
								col.field !== 'addFrozenAmount' &&
								col.field !== 'promotionalFeeAmount' &&
								col.field !== 'promotionalFeeRecharge' &&
								col.field !== 'reduceFrozenAmount' &&
								col.field !== 'checkStatus' &&
								col.field !== 'netCashFlow'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetPddDepositAccount"
					@footerCellClick="onSummaryTotalMap"
				>
				</vxetable>
			</div>
		</template>
	</Container>

	<el-dialog v-model="detailVisible" :title="detailName" :width="radioValue === '货款余额' ? '50%' : '60%'" draggable overflow>
		<div>
			<div style="display: flex; align-items: center; margin-bottom: 10px; gap: 10px" v-if="radioValue !== '货款余额'">
				<dataRange v-model:startDate="detailInfo.startDate" v-model:endDate="detailInfo.endDate" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-input v-model.trim="detailInfo.shopName" placeholder="店铺" clearable maxlength="50" style="width: 180px" />
				<el-select v-model="detailInfo.financialTypeList" placeholder="财务类型" style="width: 200px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in finanList" :key="item" :label="item" :value="item"></el-option>
				</el-select>
				<el-button type="primary" @click="onDetailGetList" style="margin-left: 10px">查询</el-button>
			</div>
			<div style="height: 400px">
				<vxetable
					showsummary
					ref="table3"
					v-if="radioValue === '货款余额' && detailVisible"
					id="platformFundPdd20250212105713"
					:tableCols="
						tableCols2.filter(
							(col) =>
								col.field !== 'serialNumberMerchantOrder' &&
								col.field !== 'depositType' &&
								col.field !== 'financialType' &&
								col.field !== 'rpaDate' &&
								col.field !== 'remark' &&
								col.field !== 'serviceDescription'
						)
					"
					:pageSize="50"
					:query="detailInfo"
					:isAsc="false"
					:queryApi="GetPddDailyBalanceDetail"
				>
					<template #toolbar_buttons>
						<el-button @click="exportProps('hk')" type="primary" class="ml5" :disabled="hkDetailsDisabled">导出</el-button>
					</template>
				</vxetable>
				<vxetable
					showsummary
					ref="table4"
					v-if="radioValue === '推广账户' && detailVisible"
					id="platformFundPdd20250212105722"
					:tableCols="tableCols2.filter((col) => col.field !== 'transactionType' && col.field !== 'depositType' && col.field !== 'transactionCount')"
					:pageSize="50"
					:query="detailInfo"
					:isAsc="false"
					:queryApi="GetPddMarketingAccountDetail"
				>
				</vxetable>
				<vxetable
					showsummary
					ref="table5"
					v-if="radioValue === '保证金账户' && detailVisible"
					id="platformFundPdd20250212105731"
					:tableCols="tableCols2.filter((col) => col.field !== 'transactionType' && col.field !== 'serialNumberMerchantOrder' && col.field !== 'transactionCount')"
					:pageSize="50"
					:query="detailInfo"
					:isAsc="false"
					:queryApi="GetPddDepositAccountDetail"
				>
				</vxetable>
			</div>
		</div>
	</el-dialog>

	<el-dialog v-model="totalMapVisible" width="60%" draggable overflow>
		<div>
			<dataRange
				v-model:startDate="trendChart.startDate"
				v-model:endDate="trendChart.endDate"
				:clearable="false"
				startPlaceholder="开始时间"
				endPlaceholder="结束时间"
				style="width: 260px"
				@change="onTrendChartMethod(2)"
			/>
			<lineChart
				v-if="totalMapVisible"
				:chartData="analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '600px',
					'box-sizing': 'border-box',
					'line-height': '600px',
				}"
			/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import {
	GetPddDailyBalance,
	GetPddDailyBalanceDetail,
	GetPddMarketingAccount,
	GetPddMarketingAccountDetail,
	GetPddDepositAccount,
	GetPddDepositAccountDetail,
	ExportPddDailyBalance,
	ExportPddDailyBalanceDetail,
	ExportPddDepositAccount,
	ExportPddDepositAccountDetail,
	ExportPddMarketingAccount,
	LockDailyBalanceData,
	UnLockDailyBalanceData,
	GetPddDailyBalanceTotalMap,
	GetPddMarketingAccountTotalMap,
	GetPddDepositAccountTotalMap,
	EditPddDailyBalance,
	InitShopData,
	SyncShopDataWithDrawAmount,
	CheckShopDataDailyBalance,
} from '/@/api/cwManager/cwFundsDailyBalance';
import { GetWithDrawShopList } from '/@/api/cwManager/withDrawInfo';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
import { ElMessageBox, ElMessage } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const editableNumber = defineAsyncComponent(() => import('/@/components/yhCom/editableNumber.vue'));
const pageLoading = ref(false);
const timeRange = ref('');
const radioValue = ref('货款余额');
const statusList = ref(['待导入', '已导入', '已锁定']);
const shopNamelist = ref<Public.options[]>([]);
const finanList = ref<any>([]);
const checkboxList = ref<any>([]);
const table = ref();
const table1 = ref();
const table2 = ref();
const table3 = ref();
const table4 = ref();
const table5 = ref();
const detailVisible = ref(false);
const rootDisabled = ref(false);
const hkDetailsDisabled = ref(false);
const detailInfo = ref({
	startDate: '',
	endDate: '',
	shopName: '',
	status: [],
	financialTypeList: [],
	date: '',
});
const detailName = ref('');
const totalMapName = ref('');
const totalMapVisible = ref(false);
const analysisData = ref<any>({});
const sumChart = ref();
const shopWithDraw = ref('');
const trendChart = ref({
	startDate: '',
	endDate: '',
	shopIdList: [] as Array<string>,
	status: [] as Array<string>,
	platformShopId: '',
});
const query = ref({
	startDate: '',
	endDate: '',
	shopName: '',
	status: [],
	shopIdList: [],
	platformShopId: '',
	errorReason: '',
	checkStatus: '',
});
const tableData = ref<any[]>([]);
const backupTableData = ref<any[]>([]);

const formatFixedAmt = (value: number) => (value ? value.toFixed(2) : 0);

const findRowIndex = (row: any) => tableData.value.findIndex((item: any) => item.mesgID === row.mesgID);

const pullProps = async () => {
	ElMessageBox.confirm(`${query.value.startDate}-${query.value.endDate}的已导入数据会丢失，是否确认拉取？`, '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await InitShopData({ startDate: query.value.startDate, endDate: query.value.endDate, platform: 2 });
			if (success) {
				ElMessage.success('拉取成功');
				getList();
			} else {
				ElMessage.error('拉取失败，请稍后再试！');
			}
		})
		.catch(() => {});
};

const Payouts = async () => {
	const { success, data } = await SyncShopDataWithDrawAmount({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('提现成功');
	} else {
		ElMessage.error('提现失败，请稍后再试！');
	}
};

const check = async () => {
	const { success, data } = await CheckShopDataDailyBalance({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('验算成功');
	} else {
		ElMessage.error('验算失败，请稍后再试！');
	}
};

// 获取当前选中的表格
const getCurrentTable = () => {
	const tableRefs = {
		货款余额: table,
		推广账户: table1,
		保证金账户: table2,
	};
	return tableRefs[radioValue.value as keyof typeof tableRefs];
};

// 更新表格数据并同步 UI
const updateTableData = (row: any, status: boolean) => {
	if (status && row.status === '已锁定') {
		ElMessage.warning('已锁定数据不能编辑，请解锁后编辑');
		return;
	}
	const index = findRowIndex(row);
	if (index !== -1) {
		tableData.value[index].statusVerify = status;
		getCurrentTable()?.value.onAssignedData(tableData.value);
	}
};

// 编辑
const handleEdit = (row: any) => updateTableData(row, true);

// 取消
const handleCancel = (row: any) => {
	const index = findRowIndex(row);
	if (index !== -1) {
		// 恢复备份数据
		BACKUP_FIELDS.forEach((field) => {
			tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
		});
		updateTableData(row, false);
	}
};

// 保存
const handleSave = async (row: any) => {
	let a = BACKUP_FIELDS;
	const index = findRowIndex(row);
	if (index === -1) return;
	updateTableData(row, false);
	// 删除备份字段
	BACKUP_FIELDS.forEach((field) => {
		delete row[`${field}_Backup`];
	});
	try {
		const response = await EditPddDailyBalance({ ...row });
		// 处理 API 返回的数据
		if (response.success) {
			backupTableData.value = [...tableData.value]; // 深拷贝，避免数据引用问题
			ElMessage.success('保存成功');
			// 创建新的备份
			BACKUP_FIELDS.forEach((field) => {
				tableData.value[index][`${field}_Backup`] = tableData.value[index][field];
			});
			getCurrentTable()?.value.getList();
		} else {
			// ElMessage.error(response.msg || '保存失败');
			a.forEach((field) => {
				tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
			});
		}
	} catch (error) {
		console.error('API 请求失败:', error);
		ElMessage.error('保存请求失败，请稍后再试！');
	}
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const onLockUnlock = async (type: 'lock' | 'unlock') => {
	if (checkboxList.value.length === 0) {
		ElMessage.warning('请选择需要锁定/解锁的数据');
		return;
	}
	ElMessageBox.confirm(`确定要${type === 'lock' ? '锁定' : '解锁'}这些数据吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			let radiotype = '';
			if (radioValue.value === '货款余额') {
				radiotype = '货款';
			} else if (radioValue.value === '推广账户') {
				radiotype = '营销';
			} else if (radioValue.value === '保证金账户') {
				radiotype = '保证金';
			}
			const params = {
				platform: 2,
				tableType: radiotype,
				mesgIdList: checkboxList.value.map((item: any) => item.mesgID),
			};
			const { success } = type === 'lock' ? await LockDailyBalanceData(params) : await UnLockDailyBalanceData(params);
			if (success) {
				ElMessage.success('操作成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage.info(`已取消${type === 'lock' ? '锁定' : '解锁'}`);
		});
};

const onDetailGetList = async () => {
	const tableRefs = {
		货款余额: table3,
		推广账户: table4,
		保证金账户: table5,
	};
	const currentTable = tableRefs[radioValue.value as keyof typeof tableRefs];
	currentTable.value.query.currentPage = 1;
	currentTable.value.getList();
};

const onDetailcount = async (row: any) => {
	detailInfo.value = JSON.parse(JSON.stringify(row));
	detailInfo.value.startDate = dayjs(detailInfo.value.date).format('YYYY-MM-DD');
	detailInfo.value.endDate = dayjs(detailInfo.value.date).format('YYYY-MM-DD');
	detailInfo.value.status = [];
	detailInfo.value.financialTypeList = row.financialType ? row.financialType.split(',') : [];
	detailInfo.value.shopName = detailInfo.value.shopName;
	detailName.value = radioValue.value + '明细';
	if (radioValue.value === '推广账户') {
		finanList.value = ['提现', '充值', '单店满返', '跨店满返', '限时免单', '评价有礼', '买就返', '日常跨店满返', '技术服务费'];
	} else if (radioValue.value === '保证金账户') {
		finanList.value = ['提现', '充值', '扣款', '汇入', '技术服务费'];
	}
	detailVisible.value = true;
};

const exportProps = async (type: 'root' | 'hk') => {
	const handleExport = async (exportFunc: any, fileNamePrefix: string, platform: string, tableQuery: any) => {
		try {
			const data = await exportFunc({ ...query.value, ...tableQuery });
			if (data) {
				const aLink = document.createElement('a');
				const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.download = `${fileNamePrefix}-${platform}${new Date().toLocaleString()}.xlsx`;
				document.body.appendChild(aLink); // 兼容Firefox
				aLink.click();
				document.body.removeChild(aLink);
			}
		} finally {
			rootDisabled.value = false;
		}
	};
	interface ExportConfig {
		func: (params: any, config?: any) => Promise<any>;
		prefix: string;
		query: () => any;
	}
	const rootConfig: Record<string, ExportConfig> = {
		货款余额: { func: ExportPddDailyBalance, prefix: '货款余额', query: () => table.value.query },
		保证金账户: { func: ExportPddDepositAccount, prefix: '保证金账户', query: () => table2.value.query },
		推广账户: { func: ExportPddMarketingAccount, prefix: '推广账户', query: () => table1.value.query },
	};
	const hkConfig: Record<string, ExportConfig> = {
		货款余额: { func: ExportPddDailyBalanceDetail, prefix: '货款余额明细', query: () => table3.value.query },
		保证金账户: { func: ExportPddDepositAccountDetail, prefix: '保证金余额明细', query: () => table5.value.query },
	};
	if (type === 'hk' && radioValue.value === '推广账户') {
		return; // 推广账户没有明细导出
	}
	const config = type === 'root' ? rootConfig[radioValue.value] : hkConfig[radioValue.value];
	if (config) {
		await handleExport(config.func, config.prefix, '拼多多', config.query());
	}
};

const onSummaryTotalMap = async (row: any, column: any, shopId: string) => {
	trendChart.value.shopIdList = query.value.shopIdList ? query.value.shopIdList : [];
	trendChart.value.status = query.value.status ? query.value.status : [];
	trendChart.value.platformShopId = query.value.platformShopId ? query.value.platformShopId : '';
	onTrendChartMethod(1);
};

const onTrendChartMethod = async (val: number) => {
	const apiMap = {
		货款余额: {
			name: '货款余额趋势图',
			api: GetPddDailyBalanceTotalMap,
		},
		推广账户: {
			name: '推广账户趋势图',
			api: GetPddMarketingAccountTotalMap,
		},
		保证金账户: {
			name: '保证金账户趋势图',
			api: GetPddDepositAccountTotalMap,
		},
	};
	const config = apiMap[radioValue.value as keyof typeof apiMap];
	if (!config) return;
	totalMapName.value = config.name;
	if (val === 1) {
		trendChart.value.startDate = dayjs(query.value.endDate).subtract(1, 'month').format('YYYY-MM-DD');
		trendChart.value.endDate = query.value.endDate;
	} else {
		trendChart.value.startDate = dayjs(trendChart.value.startDate).format('YYYY-MM-DD');
		trendChart.value.endDate = dayjs(trendChart.value.endDate).format('YYYY-MM-DD');
	}
	const params = {
		...query.value,
		startDate: trendChart.value.startDate,
		endDate: trendChart.value.endDate,
		shopIdList: trendChart.value.shopIdList,
		status: trendChart.value.status,
		platformShopId: trendChart.value.platformShopId,
	};
	const res = await config.api(params);
	analysisData.value = res;
	sumChart.value?.reSetChart(analysisData.value);
	totalMapVisible.value = true;
};

const onTotalMap = async (row: any) => {
	trendChart.value.shopIdList = [row.shopId];
	trendChart.value.platformShopId = row.platformShopId;
	onTrendChartMethod(1);
};

const getList = () => {
	const tableRefs = {
		货款余额: table,
		推广账户: table1,
		保证金账户: table2,
	};
	const currentTable = tableRefs[radioValue.value as keyof typeof tableRefs];
	currentTable.value.clearSelection();
	currentTable.value.query.currentPage = 1;
	currentTable.value.getList();
	checkboxList.value = [];
};
const BACKUP_FIELDS = ['endBalance', 'goodsIncome', 'fee', 'totalIncome', 'totalOutcome'] as const;

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.statusVerify = false;
		// 创建备份
		BACKUP_FIELDS.forEach((field) => {
			item[`${field}_Backup`] = item[field];
		});
	});
	tableData.value = data.data.list;
	shopWithDraw.value = data.data.extData.shopWithDraw ? data.data.extData.shopWithDraw : '';
	backupTableData.value = JSON.parse(JSON.stringify(data.data.list));
	callback(data);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'date', title: '日期', width: '85', formatter: 'formatDate' },
	{ sortable: true, field: 'rpaDate', title: '抓取时间', width: '135' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID', width: '100' },
	{ sortable: true, field: 'shopId', title: '店铺ID', width: '76' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '220' },
	{ summaryEvent: true, sortable: true, field: 'openingBalance', title: '期初余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'endBalance', title: '期末余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	// { sortable: true, field: 'realTimeBalance', title: '实时余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'availableBalance', title: '可用余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'goodsIncome', title: '货款收入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'frozenAmount', title: '冻结金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'oldFrozenAmount', title: '昨日冻结', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'addFrozenAmount', title: '增加冻结', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'reduceFrozenAmount', title: '减少冻结', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'promotionalFeeAmount', title: '推广费金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	// { summaryEvent: true,sortable: true, field: 'promotionalFeeRecharge', title: '推广费充值', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'withDrawAmount', title: '提现', width: '110', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'fee', title: '扣点', width: '110', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'balance', title: '余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'withDrawAmountBalance', title: '可用余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'shopDepositBalance', title: '店铺保证金金额', width: '140', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'activityDepositBalance', title: '活动保证金金额', width: '140', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalIncome', title: '总收入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalOutcome', title: '总支出', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'netIncomeOutcome', title: '净收支', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'netCashFlow', title: '净现金流入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'checkStatus', title: '验算', width: '70' },
	{ sortable: true, field: 'status', title: '状态', width: '70' },
	{
		align: 'center',
		type: 'btnList',
		width: '170',
		fixed: 'right',
		field:'20250608094653',
		btnList: [
			{ title: '明细', handle: onDetailcount },
			{ title: '趋势图', handle: onTotalMap },
			{ title: '编辑', handle: handleEdit, isDisabled: (row) => (radioValue.value === '货款余额' ? row.statusVerify : true), permissions: 'FinancialFundsEditor' },
			{ title: '保存', handle: handleSave, isDisabled: (row) => (radioValue.value === '货款余额' ? !row.statusVerify : true), permissions: 'FinancialFundsEditor' },
			{ title: '取消', handle: handleCancel, isDisabled: (row) => (radioValue.value === '货款余额' ? !row.statusVerify : true), permissions: 'FinancialFundsEditor' },
		],
	},
]);

const tableCols2 = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'date', title: '日期', width: '140', formatter: 'formatDate' },
	{ sortable: true, field: 'rpaDate', title: '入账时间', width: '100' },
	{ sortable: true, field: 'shopName', title: '店铺', width: '200' },
	{ sortable: true, field: 'transactionType', title: '交易类型', width: '100' },
	{ sortable: true, field: 'serialNumberMerchantOrder', title: '商户订单号', width: '100' },
	{ sortable: true, field: 'depositType', title: '保证金类型', width: '100' },
	{ sortable: true, field: 'financialType', title: '财务类型', width: '100' },
	{ sortable: true, field: 'amount', title: '金额(元)', width: '100' },
	{ sortable: true, field: 'transactionCount', title: '笔数', width: '70' },
	{ sortable: true, field: 'remark', title: '备注', width: '70' },
	{ sortable: true, field: 'serviceDescription', title: '业务描述', width: '100' },
]);

const fetchShopList = async () => {
	const params = {
		currentPage: 1,
		pageSize: 10000000,
		platform: 2,
	};
	query.value.shopIdList = [];
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		query.value.startDate = timeRange.value;
		query.value.endDate = timeRange.value;
	}
	fetchShopList();
});
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 65px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.container-relative {
	position: relative;
	width: 100%;
	height: 100%;
}

.radio-group-absolute {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	display: flex;
	align-items: center;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
.shop-text {
	color: red;
	font-size: 12px;
	margin-left: 5px;
	max-width: 900px;
	min-width: 100px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
}
</style>
