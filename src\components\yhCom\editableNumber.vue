<template>
	<el-input-number
		v-if="modelValue.statusVerify"
		:model-value="modelValue[field]"
		@update:model-value="handleValueChange"
		:min="props.min"
		:max="props.max"
		:controls="false"
		:precision="2"
		placeholder="请输入"
		:style="{ width: '100%' }"
	/>
	<span v-else>{{ formatFixedAmt(modelValue[field]) }}</span>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { formatters } from '/@/utils/vxetableFormats';
const props = withDefaults(
	defineProps<{
		modelValue: any;
		field: string;
		max?: number;
		min?: number;
	}>(),
	{
		min: -99999999,
		max: 99999999,
	}
);

const emit = defineEmits<{
	(e: 'update:modelValue', value: any): void;
}>();

const handleValueChange = (value: number) => {
	emit('update:modelValue', {
		...props.modelValue,
		[props.field]: value,
	});
};

const formatFixedAmt = (value: number) => {
	return value ? formatters.fmtAmt2(value) : value;
};
</script>

<style scoped lang="scss">
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
