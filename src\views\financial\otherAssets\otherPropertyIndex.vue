<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="其他资产" name="first" style="height: 100%">
					<elseAsset />
				</el-tab-pane>
				<!-- <el-tab-pane label="仓储资产" name="second" style="height: 100%" lazy>
					<storageAsset />
				</el-tab-pane> -->
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const elseAsset = defineAsyncComponent(() => import('./components/elseAsset.vue'));
const storageAsset = defineAsyncComponent(() => import('./components/storageAsset.vue'));
const activeName = ref('first');
</script>
