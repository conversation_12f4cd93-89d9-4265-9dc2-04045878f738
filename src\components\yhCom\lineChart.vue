<template>
	<div style="height: 100%; width: 100%; padding-left: 10px; overflow: auto">
		<div v-if="chartData && chartData.series && chartData.series != null && chartData.series.length > 0" :id="'lineChart' + random" :style="props.thisStyle"></div>
		<div v-else>没有可展示的图表!</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineProps, nextTick, defineExpose } from 'vue';
import * as echarts from 'echarts';
const emit = defineEmits(['onLegendMethod']);
const random = ref('');
const props = defineProps({
	thisStyle: {
		type: Object,
		default: function () {
			return {
				width: '100%',
				height: '550px',
				'box-sizing': 'border-box',
				'line-height': '360px',
			};
		},
	},
	chartData: {
		type: Object,
		default: () => {
			return {};
		},
	},
	gridStyle: {
		type: Object,
		default: function () {
			return {
				top: '20%',
				left: '10%',
				right: '4%',
				bottom: '5%',
				containLabel: false,
			};
		},
	},
	// 默认勾选图例
	lengendObject: {
		type: Object,
		default: () => {
			return {};
		},
	},
});
const chatProps = ref({
	legend: [],
	xAxis: [],
	series: [],
	title: '',
});
const option = ref({});
var selectedLegend: Record<string, boolean> = {};
if (props.chartData.selectedLegend) {
	props.chartData.legend.forEach((f: any) => {
		if (!props.chartData.selectedLegend.includes(f)) selectedLegend[f] = false;
	});
}
const getCharts = (val: any) => {
	option.value = {
		title: {
			text: val.title ? val.title : '',
		},
		tooltip: {
			trigger: 'axis',
		},
		legend: {
			selected: props.lengendObject && Object.keys(props.lengendObject).length > 0 ? props.lengendObject : selectedLegend,
			top: 30,
		},
		toolbox: {
			show: true,
			feature: {
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {},
			},
		},
		xAxis: {
			type: 'category',
			data: val.xAxis,
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				formatter: function (value: number) {
					const absValue = Math.abs(value);
					if (absValue >= 10000) {
						return (value / 10000).toFixed(1) + '万';
					}
					return value;
				},
			},
			splitLine: {
				show: true,
				lineStyle: {
					type: 'dashed',
				},
			},
		},
		grid: props.gridStyle,
		series: val.series,
	};
};
const reSetChart = (val: any) => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);

	myChart && myChart.dispose();
	getCharts(val);
	createChart();
};
const createChart = () => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);
	option.value && myChart.setOption(option.value);
	myChart.on('legendselectchanged', function (event: any) {
		console.log('Clicked legend: ' + event.name); //当前点击图例
		console.log('Selected legends: ', event.selected); //所有图例
		// 过滤出值为false的项
		const selectedTrueItems = Object.keys(event.selected)
			.filter((key) => event.selected[key] === false)
			.reduce(
				(obj, key) => {
					obj[key] = event.selected[key];
					return obj;
				},
				{} as Record<string, boolean>
			);
		emit('onLegendMethod', selectedTrueItems || {});
	});
	window.addEventListener('resize', () => {
		myChart.resize();
	});
};
onMounted(() => {
	var e = 10;
	var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
		a = t.length,
		n = '';
	for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
	random.value = n;
	nextTick(() => {
		chatProps.value = JSON.parse(JSON.stringify(props.chartData));
		getCharts(chatProps.value);
		createChart();
	});
});
defineExpose({
	reSetChart,
});
</script>

<style scoped lang="scss"></style>
