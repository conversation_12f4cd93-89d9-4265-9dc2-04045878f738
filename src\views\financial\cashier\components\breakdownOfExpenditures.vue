<template>
	<Container>
		<template #content>
			<vxetable ref="table" id="20240915093955" :tableCols="tableCols" :query="query" />
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'status', title: '支出明细' },
	{ sortable: true, field: 'cascadePath', title: '实际支出金额' },
]);
const options = ref<Public.options[]>([]);
const table = ref();
const query = ref({
	startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
	endDate: dayjs().endOf('month').format('YYYY-MM-DD'),
	maxNum: null,
	minNum: null,
	ddnum: '',
	area: '',
	status: '',
});
const getList = () => {
	table.value.getList();
};
</script>

<style scoped lang="scss"></style>
