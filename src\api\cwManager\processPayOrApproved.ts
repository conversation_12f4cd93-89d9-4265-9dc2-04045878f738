import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/processPayOrApproved/`;

//待付款采购单列表
export const GetWaitPayPurchaseOrderList = (params: any) => request.post(apiPrefix + 'GetWaitPayPurchaseOrderList', params);

//合并支付，返回支付链接
export const GetPurchaseOrdersProcessAliOrderUrl = (params: any) => request.post(apiPrefix + 'GetPurchaseOrdersProcessAliOrderUrl', params);

//通过钉钉流程
export const PassWaitPayPurchaseOrdersProcess = (params: any) => request.post(apiPrefix + 'PassWaitPayPurchaseOrdersProcess', params);

//拉取采购单列表 GetWaitPayPurchaseOrdersByCashier
export const GetWaitPayPurchaseOrdersByCashier = () => request.post(apiPrefix + 'GetWaitPayPurchaseOrdersByCashier');

export const ExportWaitPayPurchaseOrderList = (params: any, config = { responseType: 'blob' } as any) => {
	return request.post(apiPrefix + 'ExportWaitPayPurchaseOrderList', params, config);
};

//跟进流程号获取审批信息 GetCwDingProcessInfoByInstanceId
export const GetCwDingProcessInfoByInstanceId = (params: any) => request.post(apiPrefix + 'GetCwDingProcessInfoByInstanceId', params);

//修改账号 ChangePayInfoWaitPayPurchaseOrdersProcess
export const ChangePayInfoWaitPayPurchaseOrdersProcess = (params: any, config = {}) => request.post(apiPrefix + 'ChangePayInfoWaitPayPurchaseOrdersProcess', params, config);

//已审核待确认 PassConfirmWaitPayPurchaseOrdersProcess
export const PassConfirmWaitPayPurchaseOrdersProcess = (params: any, config = {}) => request.post(apiPrefix + 'PassConfirmWaitPayPurchaseOrdersProcess', params, config);

//刷新1688匹配状态  processPayOrApproved
export const RepairAliOrderAmount = (params: any, config = {}) => request.post(apiPrefix + `RepairAliOrderAmount`, params, config);

//同步钉钉  RepairDingInfo
export const RepairDingInfo = (params: any, config = {}) => request.post(apiPrefix + `RepairDingInfo`, params, config);

//GetPayExceptCount
export const GetPayExceptCount = (params: any, config = {}) => request.post(apiPrefix + `GetPayExceptCount`, params, config);
