<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.account" placeholder="账户名" class="publicCss" clearable filterable>
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.timeType" placeholder="时间类型" :clearable="false" style="width: 85px; margin: 0 0 5px 0">
					<el-option label="复核时间" value="复核时间" />
					<el-option label="操作时间" value="操作时间" />
					<el-option label="发生时间" value="发生时间" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-input v-model.trim="query.accountUserName" placeholder="用户名(模糊搜索账户名)" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.cardNature" placeholder="银行性质" class="publicCss" clearable filterable>
					<el-option v-for="item in natureList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.cardType" placeholder="网银类型" class="publicCss" clearable filterable>
					<el-option key="提现网银" label="提现网银" value="提现网银" />
					<el-option key="其他对公网银" label="其他对公网银" value="其他对公网银" />
					<el-option key="出纳账单" label="出纳账单" value="出纳账单" />
				</el-select>
				<!-- <el-select v-model="query.cardNature" placeholder="状态" class="publicCss" clearable filterable>
					<el-option key="待复核" label="待复核" value="待复核" />
					<el-option key="已复核" label="已复核" value="已复核" />
				</el-select> -->
				<el-select v-model="query.isWarning" placeholder="标志" class="publicCss" clearable filterable>
					<el-option label="红色" style="color: red" :value="true">
						<el-icon> <Flag /> </el-icon>红色
					</el-option>
					<el-option label="黑色" style="color: black" :value="false">
						<el-icon> <Flag /> </el-icon>黑色
					</el-option>
				</el-select>
				<!-- <el-input v-model.trim="query.operator" class="publicCss" placeholder="操作人" clearable maxlength="50" /> -->
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="onRegisterMethod" type="primary">登记</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedCheckBox
				@select="checkboxChange"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawBankCard"
			>
				<template #isWarning="{ row }">
					<el-icon :style="{ color: row.isWaringColor ? row.isWaringColor : '', cursor: 'pointer' }" @click="onFlagToggle(row)">
						<Flag />
					</el-icon>
				</template>
				<template #toolbar_buttons>
					<el-button @click="onOneClickReview" type="primary">一键复核</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="数据登记" width="400" draggable overflow @close="handleClose">
		<div v-loading="formLoading">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="业务类型" :label-width="'90px'" prop="operatorType">
					<el-select v-model="singleform.operatorType" placeholder="业务类型" class="btnGroup" clearable filterable @change="operatorChange">
						<el-option key="提现" label="提现" value="提现" />
						<el-option key="过账" label="过账" value="过账" />
						<el-option key="申请款" label="申请款" value="申请款" />
						<el-option key="期初" label="期初" value="期初" />
						<el-option key="调整" label="调整" value="调整" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="平台" :label-width="'90px'" prop="platform" v-if="singleform.operatorType == '提现'">
					<el-select v-model="singleform.platform" placeholder="平台" class="btnGroup" clearable filterable @change="platformChange">
						<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item> -->
				<el-form-item label="店铺名称" :label-width="'90px'" prop="shopId" v-if="singleform.operatorType == '提现'">
					<el-select v-model.trim="singleform.shopId" placeholder="店铺名称" class="btnGroup" clearable filterable @change="shopChange">
						<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="提现支付宝" :label-width="'90px'" prop="aliPayAccount" v-if="withdraw">
					<el-select v-model="singleform.aliPayAccount" placeholder="提现支付宝" class="btnGroup" clearable filterable @change="handleAliPayChange">
						<el-option v-for="item in withdrawAlipay" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item> -->
				<el-form-item label="发生时间" :label-width="'90px'" prop="occurrenceTime">
					<el-date-picker
						v-model="singleform.occurrenceTime"
						type="datetime"
						placeholder="发生时间"
						format="YYYY-MM-DD HH:mm:ss"
						date-format="YYYY-MM-DD"
						time-format="HH:mm:ss"
						style="width: 190px"
					/>
				</el-form-item>
				<el-form-item label="账户" :label-width="'90px'" prop="account">
					<el-select v-model="singleform.account" placeholder="账户" class="btnGroup" clearable filterable>
						<el-option v-for="item in accountList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="对方账户" :label-width="'90px'" prop="toAccount" v-if="singleform.operatorType == '申请款' || singleform.operatorType == '过账'">
					<el-select v-model="singleform.toAccount" placeholder="对方账户" class="btnGroup" clearable filterable>
						<el-option v-for="item in accountList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="调整方向" :label-width="'90px'" prop="isPay" v-if="singleform.operatorType == '调整'">
					<el-select v-model="singleform.isPay" placeholder="调整方向" class="btnGroup" clearable filterable>
						<el-option key="收入" label="收入" :value="false" />
						<el-option key="支出" label="支出" :value="true" />
					</el-select>
				</el-form-item>
				<el-form-item label="发生金额" :label-width="'90px'" prop="amount">
					<el-input v-model.trim="singleform.amount" placeholder="发生金额" class="btnGroup" clearable maxlength="20" @input="handleAmountInput" @blur="handleAmountBlur" />
				</el-form-item>
				<el-form-item label="摘要" :label-width="'90px'" style="white-space: pre-wrap; word-break: break-all">
					<el-input
						v-model="singleform.remark"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						style="width: 700px"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog v-model="recombinedVisible" title="数据核对" width="1700" draggable overflow>
		<div style="height: 700px">
			<awaitingReview v-if="recombinedVisible" :reviewInfo="reviewInfo" />
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import {
	QueryWithDrawBankCard,
	UpdateWithDrawCompanyAccountBalance,
	ExportWithDrawCompanyAccountBalance,
	GetWithDrawShopList,
	WithDrawFlowRegister,
	UpdateWithDrawBankCardWarning,
	BatchVerifyWithDrawFlow,
	QueryWithDrawShopInfo,
} from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { ElMessageBox, ElLoading } from 'element-plus';
import { platformlist } from '/@/utils/tools';
import type { FormInstance } from 'element-plus';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const awaitingReview = defineAsyncComponent(() => import('./awaitingReview.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const recombinedVisible = ref(false);
const withdraw = ref(false);
const formLoading = ref(false);
const MIN_VALUE = ref(0);
const MAX_VALUE = ref(9999999);
const ruleFormRef = ref<FormInstance | null>(null);
const shopNamelist = ref<Public.options[]>([]);
const withdrawAlipay = ref<Public.options[]>([]);
const nameList = ref<Public.options[]>([]);
const accountList = ref<Public.options[]>([]);
const saveData = ref<Public.options[]>([]);
const natureList = ref<Public.options[]>([]);
interface StoreItem {
	shopId: string | number;
	account: string;
	platform: number;
}
const storeData = ref<StoreItem[]>([]);
const reviewInfo = ref({});
const checkboxList = ref([]);
const table = ref();
const singleform = ref<{
	remark: string;
	shopId: string;
	platform: string;
	occurrenceTime: string;
	operatorType: string;
	toAccount: string;
	isPay: boolean;
	account: string;
	amount: number | undefined | string;
	aliPayAccount: string;
}>({
	remark: '',
	shopId: '',
	platform: '',
	occurrenceTime: '',
	operatorType: '提现',
	toAccount: '',
	isPay: false,
	account: '',
	amount: undefined,
	aliPayAccount: '',
});

const singlerules = {
	remark: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
	shopId: [{ required: true, message: '请选择店铺', trigger: 'blur' }],
	occurrenceTime: [{ required: true, message: '请选择发生时间', trigger: 'blur' }],
	operatorType: [{ required: true, message: '请选择业务类型', trigger: 'blur' }],
	account: [{ required: true, message: '请选择账户', trigger: 'blur' }],
	amount: [{ required: true, message: '请输入发生金额', trigger: 'blur' }],
	toAccount: [{ required: true, message: '请选择对方账户', trigger: 'blur' }],
	isPay: [{ required: true, message: '请选择调整方向', trigger: 'blur' }],
	aliPayAccount: [{ validator: validateAliPayAccount, trigger: 'blur' }],
};
const query = ref({
	startTime: '',
	endTime: '',
	account: '',
	timeType: '操作时间',
	accountUserName: '',
	operator: '',
	payTimeStart: '',
	payTimeEnd: '',
	cardNature: '',
	cardType: '',
	isWarning: '',
});

function validateAliPayAccount(rule: any, value: any, callback: any) {
	if (!value && !singleform.value.account) {
		callback(new Error('请选择提现支付宝'));
	} else {
		callback();
	}
}
function validateAccount(rule: any, value: any, callback: any) {
	if (!value && !singleform.value.aliPayAccount) {
		callback(new Error('请选择账户'));
	} else {
		callback();
	}
}
// 响应值变化
// const handleAliPayChange = () => {
// 	if (singleform.value.account) {
// 		singlerules.account[0].validator({}, singleform.value.account, () => {});
// 	}
// };
const handleAmountInput = (value: string) => {
	const validValue = value.match(/^\d*(\.\d{0,2})?/g)?.[0] || '';
	const numericValue = parseFloat(validValue);
	if (!isNaN(numericValue) && (numericValue < MIN_VALUE.value || numericValue > MAX_VALUE.value)) {
		singleform.value.amount = '';
	} else {
		singleform.value.amount = validValue; // 更新到绑定值
	}
};

const handleAmountBlur = () => {
	if (singleform.value.amount !== undefined && singleform.value.amount !== null) {
		const numericValue = parseFloat(singleform.value.amount.toString());
		if (isNaN(numericValue)) {
			singleform.value.amount = '';
		} else if (numericValue < MIN_VALUE.value) {
			singleform.value.amount = MIN_VALUE.value.toFixed(2);
		} else if (numericValue > MAX_VALUE.value) {
			singleform.value.amount = MAX_VALUE.value.toFixed(2);
		} else {
			singleform.value.amount = numericValue.toFixed(2);
		}
	} else {
		singleform.value.amount = '';
	}
};

const handleAccountChange = () => {
	if (singleform.value.aliPayAccount) {
		singlerules.aliPayAccount[0].validator({}, singleform.value.aliPayAccount, () => {});
	}
};

const onOneClickReview = async () => {
	if (checkboxList.value.length == 0) {
		window.$message.error('请选择需要复核的数据');
		return;
	}
	ElMessageBox.confirm('是否一键复核?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const rowList = checkboxList.value;
			const { data, success } = await BatchVerifyWithDrawFlow(rowList);
			if (success) {
				window.$message.success(data);
				getList();
				checkboxList.value = [];
				table.value.clearSelection(); //清空选中
			}
		})
		.catch(() => {});
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const platformChange = async (e: any) => {
	singleform.value.shopId = '';
	onAcquireShop(e);
	return;
	if (e == '1' || e == '8' || e == '9' || e == '10') {
		withdraw.value = true;
	} else {
		withdraw.value = false;
		singleform.value.aliPayAccount = '';
	}
};

const shopChange = (e: any) => {
	const shop = storeData.value.find((item: any) => item.shopId == e);
	if (shop) {
		singleform.value.account = shop.account;
	} else {
		singleform.value.account = '';
		singleform.value.aliPayAccount = '';
	}
};

const onFlagToggle = async (row: any) => {
	const { success } = await UpdateWithDrawBankCardWarning({ account: row.account, isWarning: !row.isWarning });
	if (success) {
		getList();
	}
};

const operatorChange = (e: any) => {
	if (e == '过账' || e == '申请款') {
		accountList.value = saveData.value.filter((item: any) => item.cardType.includes('提现网银') || item.cardType.includes('其他对公网银'));
	} else if (e == '提现') {
		accountList.value = saveData.value.filter((item: any) => item.cardType.includes('提现网银'));
	} else {
		accountList.value = saveData.value;
	}
	singleform.value.remark = '';
	singleform.value.shopId = '';
	singleform.value.toAccount = '';
	singleform.value.account = '';
	singleform.value.amount = undefined;
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const handleSubmit = () => {
	if (ruleFormRef.value) {
		submitForm(ruleFormRef.value);
	}
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (!valid) return;
		if (singleform.value.operatorType == '申请款' || singleform.value.operatorType == '过账') {
			if (singleform.value.toAccount == singleform.value.account) {
				window.$message.error('对方账户不能与账户相同');
				return;
			}
		}
		formLoading.value = true;
		const formattedOccurrenceTime = dayjs(singleform.value.occurrenceTime).format('YYYY-MM-DD HH:mm:ss');
		const formData = { ...singleform.value, occurrenceTime: formattedOccurrenceTime };
		formData.amount = singleform.value.amount ? Number(singleform.value.amount) : 0;
		const { success } = await WithDrawFlowRegister(formData);
		formLoading.value = false;
		if (success) {
			window.$message.success('登记成功');
			editVisible.value = false;
			getList();
		}
	});
};

const onRecombined = async (row: any) => {
	reviewInfo.value = JSON.parse(JSON.stringify(row));
	recombinedVisible.value = true;
};

const onClearDataMethod = async () => {
	singleform.value = {
		remark: '',
		shopId: '',
		occurrenceTime: '',
		operatorType: '提现',
		toAccount: '',
		isPay: false,
		account: '',
		amount: undefined,
		aliPayAccount: '',
		platform: '',
	};
};

//登记
const onRegisterMethod = async () => {
	operatorChange('提现');
	onClearDataMethod();
	singleform.value.occurrenceTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
	editVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'isWarning', title: '标志', width: '65' },
	{ sortable: true, field: 'accountName', title: '账户名', width: '150' },
	{ sortable: true, field: 'cardNature', title: '银行性质', width: '95' },
	{ sortable: true, field: 'cardType', title: '网银类型' },
	{ sortable: true, field: 'unVerifyBalance', title: '余额(复核前)', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'warningCount', title: '待复核笔数', formatter: 'fmtAmt0', align: 'right', type: 'click', handle: (row: any) => onRecombined(row) },
	{ sortable: true, field: 'warningAmount', title: '待复核金额', formatter: 'fmtAmt2', align: 'right', type: 'click', handle: (row: any) => onRecombined(row) },
	{ sortable: true, field: 'verifyBalance', title: '余额(复核后)', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'verifyUserName', title: '最后一次复核人' },
	{ sortable: true, field: 'verifyTime', title: '复核时间' },
	{ sortable: true, field: 'operatorUserName', title: '最后一次操作人' },
	{ sortable: true, field: 'operatorTime', title: '操作时间' },
]);
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	withdrawAlipay.value = data1.list.filter((item: any) => item.bankType.trim() === '支付宝').map((item: any) => ({ label: item.accountName, value: item.account }));
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
	natureList.value = data1.list
		.map((item: any) => ({ label: item.cardNature, value: item.cardNature }))
		.filter((item: any) => item.label !== null && item.value !== null)
		.reduce((acc: any, current: any) => {
			const exists = acc.some((item: any) => item.label === current.label && item.value === current.value);
			if (!exists) {
				acc.push(current);
			}
			return acc;
		}, []);

	accountList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account, cardType: item.cardType }));
	saveData.value = accountList.value;
	const { data: data2, success: success2 } = await QueryWithDrawShopInfo({ currentPage: 1, pageSize: 999999 });
	if (!success2) return;
	storeData.value = data2.list;

	onAcquireShop('');
};

const onAcquireShop = async (e: any) => {
	const params = {
		platform: e,
		currentPage: 1,
		pageSize: ********,
	};
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.imgs = item.imgs ? item.imgs.split(',') : [];
		item.withDrawDate = dayjs(item.withDrawDate).format('YYYY-MM-DD');
		item.isWaringColor = item.isWarning == true ? 'red' : item.isWarning == false ? 'black' : '';
	});
	callback(data);
};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
