<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.status" placeholder="姓名" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.area" placeholder="部门" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.area" placeholder="使用平台" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.area" placeholder="姓名/账户名" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.area" placeholder="行名" class="publicCss">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-button @click="getList" type="primary">查询</el-button>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20240915093955" :tableCols="tableCols" :query="query" isNeedCheckBox>
				<template #toolbar_buttons>
					<el-button @click="getList" type="primary">批量审批</el-button>
				</template>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const handleAdd = () => {
	console.log('同意');
};
const handleDelete = () => {
	console.log('拒绝');
};
const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'status', title: '审批编号' },
	{ sortable: true, field: 'platform', title: '流程名称', formatter: 'formatPlatform' },
	{ sortable: true, field: 'procode', title: '费用类型', formatter: 'formatLinkProcode' },
	{ sortable: true, field: 'cascadePath', title: '审批状态' },
	{ sortable: true, field: 'applierDepts', title: '发起时间' },
	{ sortable: true, field: 'assetsName', title: '发起人姓名' },
	{ sortable: true, field: 'url', title: '发起人部门' },
	{ sortable: true, field: 'hd1', title: '申请事由' },
	{ sortable: true, field: 'hd2', title: '使用平台' },
	{ sortable: true, field: 'hd3', title: '金额(元)' },
	{ sortable: true, field: 'hd4', title: '付款方式' },
	{ sortable: true, field: 'hd5', title: '姓名/账户名' },
	{ sortable: true, field: 'hd6', title: '卡号' },
	{ sortable: true, field: 'hd7', title: '行名' },
	{ sortable: true, field: 'hd8', title: '支付日期' },
	{ sortable: true, field: 'hd8', title: '备注' },
	{ sortable: true, field: 'hd8', title: '采购单号' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		btnList: [
			{ title: '同意', handle: handleAdd },
			{ title: '拒绝', handle: handleDelete },
		],
	},
]);
const options = ref<Public.options[]>([]);
const table = ref();
const query = ref({
	startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
	endDate: dayjs().endOf('month').format('YYYY-MM-DD'),
	maxNum: null,
	minNum: null,
	ddnum: '',
	area: '',
	status: '',
});
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
</script>

<style scoped lang="scss"></style>
