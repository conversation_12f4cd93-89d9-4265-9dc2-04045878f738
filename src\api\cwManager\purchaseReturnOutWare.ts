import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/PurchaseReturnOutWare/`;

//获取采购退货出库-财务列表
export const GetPurchaseReturnOutWareList = (params: any) => request.post(apiPrefix + 'GetPurchaseReturnOutWareList', params);

//导出采购退货出库-财务列表
export const ExportPurchaseReturnOutWareList = (params: any) => request.post(apiPrefix + 'ExportPurchaseReturnOutWareList', params);

//采购退货出库-修改数据
export const GetPurchaseReturnOutWare = (params: any) => request.post(apiPrefix + 'GetPurchaseReturnOutWare' + (params.instanceId ? '?instanceId=' + params.instanceId : ''), params);

//采购退货出库-修改数据
export const UpdatePurchaseReturnOutWare = (params: any) => request.post(apiPrefix + 'UpdatePurchaseReturnOutWare', params);

//采购退货出库-修改审批状态
export const ChangePurchaseReturnOutWareStatus = (params: any) => request.post(apiPrefix + 'ChangePurchaseReturnOutWareStatus', params);
