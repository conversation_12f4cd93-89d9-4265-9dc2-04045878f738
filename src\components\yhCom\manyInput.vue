<template>
	<div class="all_boady" style="width: 100%">
		<el-tooltip style="width: 100%" effect="dark" content="enter键支持多行输入" placement="bottom-start">
			<el-input
				:suffix-icon="InfoFilled"
				@change="keyChange"
				:key="keys"
				:maxlength="maxlength ? maxlength : ''"
				:clearable="clearable ? clearable : istrue"
				@clear="clear"
				:style="width ? { width: width } : { width: '100%' }"
				v-model.trim="state.input"
				:placeholder="placeholder ? placeholder : '请输入'"
				@keyup.enter.native="keyUp('form')"
				@input="onInputCheck($event)"
			></el-input>
		</el-tooltip>
		<el-dialog draggable :title="title ? title : '标题'" v-model="state.dialogVisible" append-to-body :width="bounceWidth">
			<el-input
				style="height: 380px"
				type="textarea"
				:maxlength="maxlength ? maxlength : ''"
				:rows="row ? row : 20"
				:placeholder="placeholdertext ? placeholdertext : '请分行输入'"
				@input="maxinput"
				v-model="state.textarea"
			>
			</el-input>

			<div slot="footer" class="dialog-footer" style="text-align: center; margin: 10px 0">
				<el-button style="width: 80px" @click="state.dialogVisible = false">取 消</el-button>
				<el-button style="width: 80px" type="primary" @click="submit">{{ btn ? btn : '确认' }}</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, onUnmounted, watch, defineAsyncComponent, computed, defineProps, useSlots } from 'vue';
import { ElMessage } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';
const props = defineProps({
	//是否验证数字
	verifyNumber: {
		type: Boolean,
		default: false,
	},
	pageLoading: {
		type: Boolean,
		default: false,
	},
	keys: {
		type: String,
	},
	inputt: {
		type: String,
		default: '',
	},
	title: {
		type: String,
	},
	row: {
		type: Number,
	},
	placeholder: {
		type: String,
	},
	placeholdertext: {
		type: String,
	},
	btn: {
		type: String,
	},
	clearable: {
		type: Boolean,
		default: true,
	},
	clearabletext: {
		type: Boolean,
		default: false,
	},
	maxlength: {
		type: Number,
	},
	//宽度
	width: {
		type: String,
	},
	//弹框宽度
	bounceWidth: {
		type: String,
		default: '30%',
	},
	maxRows: {
		type: Number,
		default: () => {
			return 50;
		},
	},
	//有值时且弹出弹框
	valuedOpen: {
		type: Boolean,
		default: () => {
			return false;
		},
	},
});
const state = reactive({
	input: '',
	dialogVisible: false,
	textarea: '',
	istrue: false,
});

watch(
	props,
	(e) => {
		state.input = e.inputt;
		if (props.clearabletext) {
			if (!props.inputt || props.inputt == '') {
				state.textarea = '';
			}
		}
	},
	{
		deep: true,
		immediate: true,
	}
);

onMounted(async () => {
	state.input = props.inputt;
});
const emit = defineEmits(['update:inputt', 'callback', 'entersearch']);

const keyChange = (e) => {
	// this.$emit('callback', this.input);
	emit('update:inputt', e);
};
const onInputCheck = (e: any) => {
	if (e && props.verifyNumber) {
		const reg = /^[0-9]*$/;
		if (!reg.test(e)) {
			ElMessage.error('请输入数字');
			state.input = '';
			return;
		}
	}
};
const keyUp = (e) => {
	//有值时且弹出弹框,回显数据并不再继续向下执行
	if (props.valuedOpen) {
		state.textarea = state.input.split(',').join('\n');
		state.dialogVisible = true;
		return;
	}

	if (state.input) {
		// state.$emit('callback', state.input);
		emit('entersearch', state.input);
	} else {
		state.dialogVisible = true;
	}
};
const handleClose = (done) => {
	// this.$confirm('确认关闭？')
	//   .then(_ => {
	//     done();
	//   })
	//   .catch(_ => {});
	//  ElMessage.confirm('确认关闭？', '警告', {
	//     confirmButtonText: '确定',
	//     cancelButtonText: '取消',
	//     type: 'warning',
	//    })
	//     .then(() => {
	//      done();
	//     })
	//     .catch((err) => {

	//     });
	done();
};
const submit = () => {
	state.dialogVisible = false;
	var string = state.textarea.trim().replace(/\n/g, ',');
	state.input = string.replace(/\s*/g, '');
	emit('update:inputt', string);
	// emit('callback',string);
};
//计文本域输入行数并做截断处理
const count = (params) => {
	var index = params.indexOf('\n');
	var num = 0;
	while (index !== -1) {
		num++;
		index = params.indexOf('\n', index + 1);
	}
	return num;
};
const clear = () => {
	state.input = '';
	emit('callback', '');
	state.dialogVisible = false;
};
const maxinput = () => {
	const allstring = state.textarea.toString();
	if (state.maxlength < allstring.length) {
		// this.$message('内容过长，请重新输入');
		ElMessage.error('内容过长，请重新输入');
		return;
	}
	//限制最大输入行数
	var number = count(allstring);

	const arr = state.textarea.split('\n');
	let strin = arr[number - 1];
	//对输入数据数组进行去重处理
	var newArr = []; //新数组存放去重后的值
	var repeatArr = state.textarea.split('\n'); //取原数组
	for (var i = 0; i < repeatArr.length; i++) {
		if (newArr.indexOf(repeatArr[i]) === -1) {
			newArr.push(repeatArr[i]);
		}
	}
	state.textarea = newArr.join('\n');
	if (number > props.maxRows) {
		strin = arr[props.maxRows];
		state.textarea = state.textarea.slice(0, state.textarea.indexOf(strin));
		ElMessage.error('最多输入' + props.maxRows + '行');
		return;
	}
};
</script>

<style lang="scss" scoped>
.all_boady {
	margin: 0 auto;
}

::v-deep .el-dialog__wrapper {
	transition-duration: 0.3s;
	// background-color: aqua;
}
</style>
