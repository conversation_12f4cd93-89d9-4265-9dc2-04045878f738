import { createApp, defineAsyncComponent } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { directive } from '/@/directive/index';
import { i18n } from '/@/i18n/index';
import other from '/@/utils/other';
import ElementPlus from 'element-plus';
import '/@/theme/index.scss';
const pagination = defineAsyncComponent(() => import('./components/pagination/index.vue')); //懒加载分页组件
const Container = defineAsyncComponent(() => import('/@/components/container/index.vue'));
// 动画库
import 'animate.css';
// 栅格布局
import VueGridLayout from 'vue-grid-layout';
// 电子签名
import VueSignaturePad from 'vue-signature-pad';
import { ElMessage } from 'element-plus';
// 组织架构图
import vue3TreeOrg from 'vue3-tree-org';
import 'vue3-tree-org/lib/vue3-tree-org.css';
// VForm3 表单设计
import VForm3 from 'vform3-builds';
import 'vform3-builds/dist/designer.style.css';
// 关闭自动打印
import { disAutoConnect } from 'vue-plugin-hiprint';
disAutoConnect();
import '/@/utils/tableFormats';
// 完整导入 UI 组件库
import VxeUI from 'vxe-pc-ui';
import 'vxe-pc-ui/lib/style.css';
// 完整导入 表格库
import VxeUITable from 'vxe-table';
import 'vxe-table/lib/style.css';
import domZindex from 'dom-zindex';
// 获取页面中最大的 z-index
domZindex.getMax();
// 设置当前 z-index 起始值
domZindex.setCurrent(2000);
// 获取当前 z-index 值
domZindex.getCurrent(); // 1000
// 计算下一个并返回计算后的 z-index 值
declare global {
	interface Window {
		$message: typeof ElMessage; //全局挂载消息提示
		getdingdingurl: Function; //获取钉钉登录地址
		qrlogin: Function; //扫码登录
	}
}
window.$message = ElMessage; // 挂载全局消息提示
domZindex.getNext(); // 1001
let vxeSize: any = 'small';
let tempSize = other.globalComponentSize();
if (tempSize === 'large') {
	vxeSize = 'medium';
} else if (tempSize === 'default') {
	vxeSize = 'small';
} else if (tempSize === 'small') {
	vxeSize = 'mini';
}

VxeUI.setConfig({ size: vxeSize });
VxeUITable.setConfig({ size: vxeSize });
const app = createApp(App);
app.component('Container', Container);
app.component('pagination', pagination);
directive(app);
other.elSvg(app);

import * as ElementPlusIconsVue from '@element-plus/icons-vue'
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
	app.component(key, component)
}
app.use(pinia).use(router).use(ElementPlus).use(i18n).use(VueGridLayout).use(VForm3).use(VueSignaturePad).use(vue3TreeOrg).use(VxeUI).use(VxeUITable).mount('#app');
