/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DbType } from './db-type';
import { StatusEnum } from './status-enum';
import { TenantTypeEnum } from './tenant-type-enum';
 /**
 * 
 *
 * @export
 * @interface AddTenantInput
 */
export interface AddTenantInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddTenantInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddTenantInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddTenantInput
     */
    isDelete?: boolean;

    /**
     * 用户Id
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    userId?: number;

    /**
     * 机构Id
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    orgId?: number;

    /**
     * 主机
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    host?: string | null;

    /**
     * @type {TenantTypeEnum}
     * @memberof AddTenantInput
     */
    tenantType?: TenantTypeEnum;

    /**
     * @type {DbType}
     * @memberof AddTenantInput
     */
    dbType?: DbType;

    /**
     * 数据库连接
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    connection?: string | null;

    /**
     * 数据库标识
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    configId?: string | null;

    /**
     * 从库连接/读写分离
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    slaveConnections?: string | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof AddTenantInput
     */
    orderNo?: number;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof AddTenantInput
     */
    status?: StatusEnum;

    /**
     * 电子邮箱
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    email?: string | null;

    /**
     * 电话
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    phone?: string | null;

    /**
     * 租户名称
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    name: string;

    /**
     * 租管账号
     *
     * @type {string}
     * @memberof AddTenantInput
     */
    adminAccount: string;
}
