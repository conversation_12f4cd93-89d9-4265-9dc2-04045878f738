<template>
	<div>
		<div>
			<el-button @click="onNewLineMethod" type="text">新增一行</el-button>
		</div>
		<div v-loading="listLoading">
			<el-table :data="tableData" style="width: 100%; height: 450px" border :summary-method="getSummaries" show-summary>
				<el-table-column prop="chartRemark" label="摘要" show-overflow-tooltip width="230">
					<template #default="scope">
						<el-input v-model="scope.row.chartRemark" class="btnGroup" :autosize="{ minRows: 4, maxRows: 4 }" type="textarea" placeholder="摘要" />
					</template>
				</el-table-column>
				<el-table-column prop="chartTitle" label="科目名称" width="230" show-overflow-tooltip>
					<template #default="scope">
						<el-input v-model="scope.row.chartTitle" class="btnGroup" :autosize="{ minRows: 4, maxRows: 4 }" type="textarea" placeholder="科目名称" />
					</template>
				</el-table-column>
				<el-table-column prop="chartCode" label="科目代码" width="120" show-overflow-tooltip>
					<template #default="{ row, $index }">
						<el-select v-model="row.chartCode" placeholder="科目代码" class="btnGroup" clearable filterable @change="chartCodeChange($event, $index)">
							<el-option v-for="item in accountList" :key="item.value" :label="item.value" :value="item.value" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="qty" label="数量" width="105" show-overflow-tooltip>
					<template #default="{ row, $index }">
						<el-input-number
							v-model="row.qty"
							placeholder="数量"
							class="btnGroup"
							autocomplete="off"
							:min="0"
							:max="**********"
							:precision="0"
							:controls="false"
							:disabled="!row.verify"
							@blur="valueMethod($event, $index)"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="price" label="单价" width="105" show-overflow-tooltip align="right">
					<template #default="{ row, $index }">
						<el-input-number
							v-model="row.price"
							placeholder="单价"
							class="btnGroup"
							autocomplete="off"
							:min="0"
							:max="**********"
							:precision="2"
							:controls="false"
							:disabled="!row.verify"
							@blur="valueMethod($event, $index)"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="inAmount" label="借方金额" width="110" show-overflow-tooltip align="right">
					<template #default="scope">
						<el-input-number v-model="scope.row.inAmount" placeholder="借方金额" class="btnGroup" autocomplete="off" :min="0" :max="**********" :precision="2" :controls="false" />
					</template>
				</el-table-column>
				<el-table-column prop="outAmount" label="贷方金额" width="110" show-overflow-tooltip align="right">
					<template #default="scope">
						<el-input-number v-model="scope.row.outAmount" placeholder="贷方金额" class="btnGroup" autocomplete="off" :min="0" :max="**********" :precision="2" :controls="false" />
					</template>
				</el-table-column>
				<el-table-column prop="auxiliaryType" label="辅助核算类型" width="150" show-overflow-tooltip>
					<template #default="{ row, $index }">
						<el-select v-model="row.auxiliaryType" placeholder="辅助核算类型" class="btnGroup" clearable filterable @change="auxiliaryChange($event, $index)">
							<el-option v-for="item in row.supplementaryList" :key="item" :label="item" :value="item" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="auxiliaryName" label="辅助核算名称" width="150" show-overflow-tooltip>
					<template #default="{ row, $index }">
						<el-select
							v-model="row.auxiliaryName"
							placeholder="辅助核算名称"
							class="btnGroup"
							clearable
							filterable
							@change="auxiliaryNameChange($event, $index, row)"
							remote
							reserve-keyword
							:remote-method="
								(query: any) => {
									remoteMethod(query, $index, row);
								}
							"
						>
							<el-option v-for="item in row.accountingCodeList" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="auxiliaryCode" label="辅助核算编码" width="165" show-overflow-tooltip />
				<el-table-column fixed="right" label="操作" width="50">
					<template #default="scope">
						<el-button link type="text" size="small" style="color: red" @click="onDetail(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
	<div style="display: flex; justify-content: end; margin-top: 20px">
		<el-button @click="onCancellationMethod">取消</el-button>
		<el-button type="primary" @click="onSingleSave(2)" :disabled="selectloading">保存并确认</el-button>
		<el-button type="primary" @click="onSingleSave(3)" :disabled="selectloading">保存并确认生成正式凭证</el-button>
		<el-button type="primary" @click="onSingleSave(3)" :disabled="selectloading">生成凭证</el-button>
		<el-button type="primary" @click="onSingleSave(1)" :disabled="selectloading">保存</el-button>
	</div>
</template>

<script setup lang="ts" name="">
const emit = defineEmits(['close', 'onStorageMethod']);
import { ref, onMounted, defineAsyncComponent, defineProps, PropType, h, VNode } from 'vue';
import { QueryAccountsAuxiliary, QueryAccountsChart } from '/@/api/cwManager/accountsChart';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { UpdateAccountsVouchersDetail } from '/@/api/cwManager/accountsVouchers';
import { decimal } from '/@/utils/decimal';
import { debounce } from 'lodash-es';
const selectloading = ref(false);
const listLoading = ref(false);
const tableData = ref<any>([]);
const accountData = ref<any>([]);
const accountList = ref<any>([]);
const accountDataList = ref([]);
const accountingTypeList = ref([]);
const query = ref({
	currentPage: 1,
	pageSize: 100000,
});
type SingleDataItem = {
	chartRemark?: string; // 根据实际属性补充类型定义
};

const props = defineProps({
	accountLists: {
		type: Array as PropType<{ label: string; value: string | number }[]>,
		default: () => [],
	},
	singleData: {
		type: Array, // 使用明确的类型
		default: () => [],
	},
	accountDatas: {
		type: Array,
		default: () => [],
	},
	abstract: {
		type: String,
		default: () => '',
	},
});

const getSummaries = (param: any) => {
	const { columns, data } = param;
	const sums: (string | number | VNode)[] = []; // 声明类型
	columns.forEach((column: any, index: any) => {
		if (index === 0) {
			sums[index] = h('div', ['合计']);
			return;
		}
		if (['inAmount', 'outAmount'].includes(column.property)) {
			const values = data.map((item: any) => Number(item[column.property]));
			if (!values.every((value: any) => Number.isNaN(value))) {
				sums[index] = h('b', [
					numToMoney(
						values.reduce((prev: number, curr: any) => {
							const value = Number(curr);
							if (!Number.isNaN(value)) {
								return Number(decimal(prev, value, 2, '+'));
							}
							return prev;
						}, 0)
					),
				]);
			} else {
				sums[index] = '';
			}
		} else {
			sums[index] = '';
		}
	});
	return sums;
};

const numToMoney = (num: any) => {
	// 检查输入是否为空
	if (num === null || num === undefined || num === '') {
		return '-';
	}

	// 将输入的数字转换为字符串
	const str = num.toString();

	// 分离整数部分和小数部分
	const [integerPart, decimalPart] = str.split('.');

	// 处理整数部分，千分位分隔
	const formattedInteger = integerPart
		.split('')
		.reverse()
		.reduce((prev: any, next: any, index: any) => {
			return (index % 3 ? next : next + ',') + prev;
		});

	// 合并整数部分和小数部分
	return decimalPart ? `${formattedInteger}.${decimalPart}` : `${formattedInteger}.00`;
};

const auxiliaryNameChange = (e: any, i: any, row: any) => {
	accountData.value.forEach((item2: any) => {
		tableData.value.forEach((item: any, index: number) => {
			if (i == index) {
				item.auxiliaryCode = e;
				row.accountingCodeList.forEach((item1: any) => {
					if (e == item1.code) {
						if (item2.id == item.chartCode) {
							item.chartTitle = `${item.chartCode} ${item2.fullTitle}_${item1.name}`;
						}
						item.backupName = item1.name;
						item.auxiliaryCode = item1.code;
						item.auxiliaryName = item1.code;
						item.auxiliaryId = item1.id;
						item.auxiliaryType = item1.type;
					}
				});
				if (!e && item2.id == item.chartCode) {
					// item.chartTitle = item.chartCode;
					item.chartTitle = `${item2.id} ${item2.fullTitle}`;
				}
			}
		});
	});
	if (!e) {
		onAccountingNameMethod();
	}
};

const remoteMethod = debounce(async (e: any, i: any, row: any) => {
	if (e) {
		tableData.value.forEach(async (item: any, index: any) => {
			if (i === index) {
				const { data, success } = await QueryAccountsAuxiliary({
					currentPage: 1,
					pageSize: 50,
					orderBy: '',
					isAsc: false,
					code: '',
					name: e ? e : row.accountingName,
					id: '',
					type: row.auxiliaryType,
				});
				if (!success) return;
				item.accountingCodeList = data.list.map((item: any) => ({
					code: item.code,
					name: item.name,
					id: item.id,
					type: item.type,
				}));
				item.accountingName = e;
			}
		});
	}
}, 1000);

const onDetail = (row: any) => {
	tableData.value.splice(tableData.value.indexOf(row), 1);
};

const onNewLineMethod = () => {
	listLoading.value = true;
	tableData.value.push({
		chartCode: '',
		qty: null,
		price: null,
		inAmount: null,
		outAmount: null,
		auxiliaryType: '',
		auxiliaryName: '',
		auxiliaryCode: '',
		chartTitle: '',
		chartRemark: props.abstract,
		verify: false,
	});
	listLoading.value = false;
};

const auxiliaryChange = (e: any, i: any) => {
	tableData.value.forEach((item: any, index: number) => {
		if (i == index) {
			setTimeout(async () => {
				listLoading.value = true;
				const { data, success } = await QueryAccountsAuxiliary({ currentPage: 1, pageSize: 50, orderBy: '', isAsc: false, code: '', name: '', id: '', type: e });
				listLoading.value = false;
				if (!success) return;
				item.auxiliaryName = '';
				item.auxiliaryCode = '';
				item.accountingCodeList = data.list.map((item: any) => {
					return { code: item.code, name: item.name, id: item.id, type: item.type };
				});
			}, 100);
		}
	});
	if (!e) {
		accountData.value.forEach((item2: any) => {
			tableData.value.forEach((item: any, index: number) => {
				if (!e && item2.id == item.chartCode && i == index) {
					item.chartTitle = `${item2.id} ${item2.fullTitle}`;
				}
			});
		});
	}
};

const onCancellationMethod = () => {
	emit('close');
};

const options = ref<Public.options[]>([]);
const account = defineModel('value');
const accountName = defineModel('label');
const id = defineModel('id');
const clearingMethod = (e: any, i: any) => {
	tableData.value.forEach((item: any, index: number) => {
		if (i == index) {
			if (!item.qty) {
				item.qty = null;
				item.inAmount = null;
				item.outAmount = null;
			}
			if (!item.price) {
				item.price = null;
				item.inAmount = null;
				item.outAmount = null;
			}
			if (item.qty === null && item.price === null) {
				item.outAmount = null;
				item.inAmount = null;
			}
		}
	});
};

const valueMethod = (e: any, i: any) => {
	tableData.value.forEach((item: any, index: number) => {
		if (i == index) {
			item.inAmount = null;
			item.outAmount = null;
			if (item.voucherType == '借' && item.inAmount !== 0) {
				item.inAmount = Number(decimal(item.qty ? item.qty : 0, item.price ? item.price : 0, 2, '*'));
			} else if (item.voucherType == '贷' && item.outAmount !== 0) {
				item.outAmount = Number(decimal(item.price ? item.price : 0, item.qty ? item.qty : 0, 2, '*'));
			}
		}
	});
};

const chartCodeChange = async (e: any, i: any) => {
	accountData.value.forEach((item1: any) => {
		tableData.value.forEach((item: any, index: number) => {
			if (item1.id == e && index == i) {
				if (!item1.hasQuantity) {
					item.price = null;
					item.qty = null;
					item.verify = false;
				}
				if (item1.hasQuantity) {
					item.verify = true;
				}
				item.auxiliaryCode = '';
				item.auxiliaryName = '';
				item.auxiliaryType = '';
				item.voucherType = item1.balanceDirection;
				item.chartTitle = `${item1.id} ${item1.fullTitle}`;
				item.supplementaryList = item1.supplementary ? item1.supplementary.split(',') : [];
				if (item.voucherType == '贷') {
					item.inAmount = null;
				} else if (item.voucherType == '借') {
					item.outAmount = null;
				}
			}
			if (!e && index == i) {
				Object.assign(item, {
					auxiliaryCode: '',
					auxiliaryName: '',
					auxiliaryType: '',
					chartTitle: '',
					price: null,
					qty: null,
					outAmount: null,
					inAmount: null,
				});
			}
		});
	});
};

const getBankList = async () => {
	listLoading.value = true;
	tableData.value.forEach((item: any) => {
		accountList.value.forEach((item1: any) => {
			// item.verify = false;
			item.accountingName = '';
			item.chartCode = item.chartCode ? item.chartCode.toString() : '';
			if (item.chartCode == item1.value && item1.hasQuantity) {
				item.verify = true;
			}
			if (item1.supplementary != null && item1.supplementary && item.chartCode == item1.value) {
				item.backupName = item.auxiliaryName;
				// item.chartTitle = `${item.chartCode} ${item1.fullTitle}_${item.auxiliaryName}`;
				item.chartTitle = item.chartTitle;
				item.supplementaryList = item1.supplementary ? item1.supplementary.split(',') : [];
				if (item.supplementaryList && item.supplementaryList.length == 1) {
					item.auxiliaryType = item1.supplementary;
				}
				item.accountingCodeList = [
					{
						code: item.auxiliaryCode || '',
						name: item.auxiliaryName || '',
						id: item.auxiliaryId || '',
						type: item.auxiliaryType || '',
					},
				];
				item.auxiliaryName = item.auxiliaryCode;
			}
		});
	});
	onAccountingNameMethod();
	listLoading.value = false;
};

const onAccountingNameMethod = () => {
	tableData.value.forEach(async (item: any) => {
		if (item.auxiliaryType) {
			const { data, success } = await QueryAccountsAuxiliary({ currentPage: 1, pageSize: 50, orderBy: '', isAsc: false, code: '', name: '', id: '', type: item.auxiliaryType });
			if (!success) return;
			let a = data.list.map((account: any) => {
				return { code: account.code, name: account.name, id: account.id, type: account.type };
			});
			const combinedList = [...(item.accountingCodeList || []), ...a];
			item.accountingCodeList = Array.from(new Set(combinedList.map((item) => item.id))).map((id) => combinedList.find((item) => item.id === id));
		}
	});
};

const onSingleSave = debounce(async (val: any) => {
	tableData.value.forEach((item: any) => {
		if (item.auxiliaryType) {
			item.auxiliaryName = item.backupName;
		}
	});
	emit('onStorageMethod', tableData.value, val);
}, 1000);

onMounted(async () => {
	// const firstItem = tableData.value[0] as SingleDataItem | undefined;
	tableData.value = JSON.parse(JSON.stringify(props.singleData));
	accountData.value = JSON.parse(JSON.stringify(props.accountDatas));
	accountList.value = JSON.parse(JSON.stringify(props.accountLists));
	tableData.value.forEach((item: any) => {
		item.verify = false;
	});

	await getBankList();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 100%;
}
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
