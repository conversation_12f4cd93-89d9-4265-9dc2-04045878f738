<template>
	<!-- <div class="login-scan-container">
		<div ref="qrcodeRef"></div>
		<div class="font12 mt20 login-msg">
			<i class="iconfont icon-saoyisao mr5"></i>
			<span>{{ $t('message.scan.text') }}</span>
		</div>
	</div> -->
	<div class="login-scan-container">
		<iframe
			id="fram_box"
			:src="`${'/dingding/ddlogin.html'}`"
			style=""
			frameborder="no"
			border="0"
			marginwidth="0"
			marginheight="0"
			scrolling="no"
			allowtransparency="true"
			width="350px"
			height="350px"
		>
		</iframe>
		<div class="font12 mt20 login-msg">
			<i class="iconfont icon-saoyisao mr5"></i>
			<!-- <span>{{ $t('message.scan.text') }}</span> -->
		</div>
	</div>
</template>

<script setup lang="ts" name="loginScan">
import { ref, onMounted, nextTick, reactive } from 'vue';
import QRCode from 'qrcodejs2-fixes';
import { ddurl } from '/@/api/admin/auth';
// 定义变量内容
const qrcodeRef = ref<HTMLElement | null>(null);

// 初始化生成二维码
const initQrcode = () => {
	nextTick(() => {
		(<HTMLElement>qrcodeRef.value).innerHTML = '';
		state.pic = new QRCode(qrcodeRef.value, {
			// text: state.ddurl,
			text: 'http://**************:8000,dingprc0uy2bi2cyyfye',
			width: 200,
			height: 200,
			colorDark: '#000000',
			colorLight: '#ffffff',
		});
	});
};
const state = reactive({
	ddurl: '',
	pic: null,
	// publicPath: process.env.BASE_URL,
});
const getusrl = async () => {
	const { data } = await ddurl();
	state.ddurl = data;
};
// 页面加载时
onMounted(() => {
	// initQrcode();
	getusrl();
});
</script>

<style scoped lang="scss">
.login-scan-animation {
	opacity: 0;
	animation-name: error-num;
	animation-duration: 0.5s;
	animation-fill-mode: forwards;
}
.login-scan-container {
	padding: 0 20px 20px;
	display: flex;
	flex-direction: column;
	text-align: center;
	@extend .login-scan-animation;
	animation-delay: 0.1s;
	:deep(img) {
		margin: auto;
	}
	.login-msg {
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--el-text-color-placeholder);
		@extend .login-scan-animation;
		animation-delay: 0.2s;
	}
}
</style>
