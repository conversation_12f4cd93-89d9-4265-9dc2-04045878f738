<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.code" placeholder="辅助核算编码" class="publicCss" clearable maxlength="18" />
				<el-input v-model.trim="query.name" placeholder="辅助核算名称" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.typeList" placeholder="辅助核算类型" class="publicCss" clearable filterable multiple
					collapse-tags style="width: 180px;">
					<el-option v-for="item in accountingList" :key="item" :label="item" :value="item" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" keyField="id" :pageSize="50" id="auxiliaryAccounting202412141909"
				:pageSizes="[50, 100, 200, 300]" :tableCols="tableCols" :query="query" :isAsc="false"
				:query-api="QueryAccountsAuxiliary">
				<template #toolbar_buttons>
					<el-button @click="onAddMethod({})" type="primary">新增</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" :title="dynamicHeading ? '编辑' : '新增'" width="400" draggable overflow
		style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
		<div style="padding-top: 10px" v-loading="listLoading">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="辅助核算编码" :label-width="'100px'" prop="code">
					<el-input v-model.trim="singleform.code" placeholder="辅助核算编码" class="btnGroup" clearable
						maxlength="50" />
				</el-form-item>
				<el-form-item label="辅助核算名称" :label-width="'100px'" prop="name">
					<el-input v-model.trim="singleform.name" placeholder="辅助核算名称" class="btnGroup" clearable
						maxlength="50" />
				</el-form-item>
				<el-form-item label="辅助核算类型" :label-width="'100px'" prop="type">
					<el-select v-model="singleform.type" placeholder="辅助核算类型" class="btnGroup" clearable filterable
						multiple collapse-tags>
						<el-option v-for="item in accountingList" :key="item" :label="item" :value="item" />
					</el-select>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryAccountsAuxiliary, ExportAccountsChartiary, InsertAccountsAuxiliary, UpdateAccountsAuxiliary, DeleteAccountsAuxiliary } from '/@/api/cwManager/accountsChart';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const dynamicHeading = ref(false);
const listLoading = ref(false);
const accountingList = ref(['组织结构', '供应商（发票）', '快递站点', '我的供应商', '店铺名称', '店铺名称2', '区域地址']);
const ruleFormRef = ref<FormInstance>();
const table = ref();
const singleform = ref<{
	code: number | undefined;
	name: string;
	type: string | Array<string>;
}>({
	code: undefined,
	name: '',
	type: [],
});

const singlerules = {
	code: [{ required: true, message: '请输入辅助核算编码', trigger: 'blur' }],
	name: [{ required: true, message: '请输入辅助核算名称', trigger: 'blur' }],
	type: [{ required: true, message: '请输入辅助核算类型', trigger: 'blur' }],
};
const query = ref({
	code: '',
	name: '',
	typeList: [],
	id: '',
	iGrade: undefined,
});

function validateNumericCode(code: any, fieldName: string): boolean {
	if (code && !/^\d+$/.test(code.toString())) {
		ElMessage.error(`${fieldName}只能输入数字`);
		return false;
	}
	return true;
}

const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			// if (!validateNumericCode(singleform.value.code, '辅助核算编码')) {
			// 	return;
			// }
			let singleformValue = singleform.value.type;
			let type = Array.isArray(singleformValue) ? singleformValue.join(',') : singleformValue;
			listLoading.value = true;
			if (dynamicHeading.value) {
				const { success } = await UpdateAccountsAuxiliary({ ...singleform.value, type });
				if (success) {
					ElMessage.success('编辑成功');
					editVisible.value = false;
					getList();
				}
			} else {
				const { success } = await InsertAccountsAuxiliary({ ...singleform.value, type });
				if (success) {
					ElMessage.success('新增成功');
					editVisible.value = false;
					getList();
				}
			}
			listLoading.value = false;
		}
	});
}, 1000); // 防抖时间1秒

const exportProps = async () => {
	await ExportAccountsChartiary({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onDelete = (row: any) => {
	ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const params = { id: row.id };
			const { success } = await DeleteAccountsAuxiliary(params);
			if (success) {
				ElMessage.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({ type: 'info', message: '已取消' });
		});
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	singleform.value.type = row.type ? row.type.split(',') : [];
	dynamicHeading.value = true;
	editVisible.value = true;
};

const onAddMethod = (row: any) => {
	singleform.value = {
		code: undefined,
		name: '',
		type: [],
	};
	dynamicHeading.value = false;
	editVisible.value = true;
};

const getList = () => {
	// if (!validateNumericCode(query.value.code, '辅助核算编码')) {
	// 	return;
	// }
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'code', title: '辅助核算编码', width: '180' },
	{ sortable: true, field: 'name', title: '辅助核算名称', width: 'auto' },
	{ sortable: true, field: 'type', title: '辅助核算类型', width: '180' },
	{ sortable: true, field: 'createdUserName', title: '创建人', width: '160' },
	{ sortable: true, field: 'createdTime', title: '创建时间', width: '195' },
	{ sortable: true, field: 'modifiedUserName', title: '修改人', width: '160' },
	{ sortable: true, field: 'modifiedTime', title: '修改时间', width: '195' },
	// { sortable: true, field: 'batchNumber', title: '批次号', width: '150' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '120',
		field:'20250608093806',
		btnList: [
			{ title: '编辑', handle: onEdit },
			{ title: '删除', handle: onDelete },
		],
	},
]);

const getAllDept = async () => { };
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
