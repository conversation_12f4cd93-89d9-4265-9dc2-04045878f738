<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="拼多多" name="first" style="height: 100%">
					<platformFundPdd />
				</el-tab-pane>
				<el-tab-pane label="天猫" name="second" style="height: 100%" lazy>
					<platformFundTm />
				</el-tab-pane>
				<el-tab-pane label="淘宝" name="third" style="height: 100%" lazy>
					<platformFundTb />
				</el-tab-pane>
				<el-tab-pane label="淘工厂" name="ninth" style="height: 100%" lazy>
					<platformFundTgc />
				</el-tab-pane>
				<el-tab-pane label="抖音" name="fourth" style="height: 100%" lazy>
					<platformFundDy />
				</el-tab-pane>
				<el-tab-pane label="京东" name="sixth" style="height: 100%" lazy>
					<platformFundJd />
				</el-tab-pane>
				<el-tab-pane label="快手" name="seventh" style="height: 100%" lazy>
					<platformFundKs />
				</el-tab-pane>
				<el-tab-pane label="视频号" name="eighth" style="height: 100%" lazy>
					<platformFundSph />
				</el-tab-pane>
				<el-tab-pane label="阿里巴巴" name="tenth" style="height: 100%" lazy>
					<platformFundAlbb />
				</el-tab-pane>
				<el-tab-pane label="小红书" name="eleventh" style="height: 100%" lazy>
					<platformFundXhs />
				</el-tab-pane>
				<el-tab-pane label="分销" name="twelfth" style="height: 100%" lazy>
					<platformFundFx />
				</el-tab-pane>
				<!-- <el-tab-pane label="扣点设置" name="fifth" style="height: 100%" lazy>
					<deductionPointSetting />
				</el-tab-pane> -->
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const platformFundPdd = defineAsyncComponent(() => import('./components/platformFundPdd.vue'));
const platformFundTm = defineAsyncComponent(() => import('./components/platformFundTm.vue'));
const platformFundTb = defineAsyncComponent(() => import('./components/platformFundTb.vue'));
const platformFundDy = defineAsyncComponent(() => import('./components/platformFundDy.vue'));
const platformFundJd = defineAsyncComponent(() => import('./components/platformFundJd.vue'));
const platformFundKs = defineAsyncComponent(() => import('./components/platformFundKs.vue'));
const platformFundSph = defineAsyncComponent(() => import('./components/platformFundSph.vue'));
const platformFundTgc = defineAsyncComponent(() => import('./components/platformFundTgc.vue'));
const platformFundAlbb = defineAsyncComponent(() => import('./components/platformFundAlbb.vue'));
const platformFundXhs = defineAsyncComponent(() => import('./components/platformFundXhs.vue'));
const platformFundFx = defineAsyncComponent(() => import('./components/platformFundFx.vue'));
const deductionPointSetting = defineAsyncComponent(() => import('./components/deductionPointSetting.vue'));
const activeName = ref('first');
</script>
