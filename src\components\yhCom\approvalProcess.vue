<template>
	<div class="timeCss" v-loading="loading" :style="{ paddingBottom: matchStatus(flowListInfo.approveStatus) ? '0px' : '50px' }">
		<el-scrollbar style="height: 100%">
			<div class="timeCss_Top" style="display: flex; background-color: #fff; justify-content: space-between">
				<div class="ProcessTop">
					<div>
						<div>
							<span>审批编号：</span>
							<span>{{ flowListInfo.businessId }}</span>
						</div>
						<div class="ProcessTop_title">
							{{ flowListInfo.title }}
						</div>
						<div class="ProcessTop_company">
							<div class="ProcessTop_company_left">
								<img src="/favicon.ico" alt="图标" class="ProcessTop_company_left_img" />
								<span style="color: #a8a9aa">义乌市昀晗贸易有限公司</span>
							</div>
						</div>
					</div>
				</div>
				<img v-if="flowListInfo.approveStatus?.includes('通过')" class="AuditImg" src="./images/agree.png" alt="审核通过" />
				<img v-if="flowListInfo.approveStatus?.includes('驳回')" class="AuditImg" src="./images/overrule.png" alt="驳回" />
				<img v-if="flowListInfo.approveStatus?.includes('撤销')" class="AuditImg" src="./images/quash.png" alt="撤销" />
				<img v-if="flowListInfo.approveStatus?.includes('拒绝')" class="AuditImg" src="./images/refuse.png" alt="拒绝" />
			</div>

			<div class="ProcessContent">
				<div v-for="(item, i) in flowListInfo.formComponentValues" :key="i">
					<div v-if="item.value && item.name" class="ProcessContent_item">
						<span class="ProcessContent_item_name">{{ item.name ? item.name : '' }}</span>
						<div v-if="item.componentType == 'DDPhotoField'" style="display: flex;flex-wrap: wrap;">
							<el-image v-for="img in item.value" style="width: 50px; height: 50px;margin-right: 10px;" :src="img" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[img]" :initial-index="0" fit="cover" />
						</div>
						<!-- 工作交接人 -->
						<span v-else-if="item.componentType == 'DynamicField'">
							{{
								item.value
									? JSON.parse(item.value)
											.filter((item: any) => item.value != '')
											.map((item1: any) => item1.value)
											.join(',')
									: ''
							}}
						</span>
						<div v-else style="display: flex; align-items: center">
							<div class="mr5 description">{{ item.value ? item.value : '' }}</div>
							<el-button link type="primary" v-if="matchingStr(item.name)" @click="copy(item.value)">复制</el-button>
						</div>
					</div>
				</div>
			</div>
			<el-timeline class="timeline">
				<el-timeline-item v-for="(activity, index) in flowListInfo.approvalList" :key="index" :timestamp="activity.timestamp">
					<template #dot>
						<el-avatar v-if="activity.type !== 'PROCESS_CC'" :size="30" :src="activity.avatar" />
						<div v-if="activity.type === 'PROCESS_CC'" class="PROCESS_CC_word">抄送</div>
					</template>
					<template #default>
						<div class="content" v-if="applyType.includes(activity.type)">
							<div class="timeline_item_content">
								<div class="timeline_item_content_left">
									<span>{{ activity.displayStatus == '申请人' ? '发起申请' : activity.displayStatus == '已同意' ? '审批人' : '' }}</span>
									<span>{{ activity.finishTime }}</span>
								</div>
								<div>
									<span class="timeline_item_content_userName">{{ activity.userName }}</span>
									<span class="timeline_item_content_displayStatus">（{{ activity.displayStatus }}）</span>
								</div>
							</div>
						</div>
						<!-- 抄送 -->
						<div v-else-if="activity.type == 'PROCESS_CC'" class="publicPadding">
							<div class="publicDisplay">
								<div>抄送人</div>
								<div>{{ activity.finishTime }}</div>
							</div>
							<div class="PROCESS_CC_Css" style="display: flex; flex-wrap: wrap">
								<div v-for="item in activity.ccUsers" class="PROCESS_CC_Css_item">
									<div><el-avatar :size="30" :src="item.ccUserAvatar" /></div>
									<div>{{ item.ccUserName }}</div>
								</div>
							</div>
						</div>
						<!-- 评论,备注 -->
						<div v-else-if="remarkType.includes(activity.type)" class="publicPadding">
							<div class="publicDisplay">
								<div>
									<span style="color: #1890ff">{{ activity.userName }} &nbsp;</span>
									<span>添加了评论</span>
								</div>
								<div>{{ activity.finishTime }}</div>
							</div>
							<div>
								<el-input v-model="activity.remark" style="width: 100%" disabled :rows="8" type="textarea" placeholder="评论" />
								<div v-if="activity.remarkImages && activity.remarkImages.length > 0" style="display: flex; flex-wrap: wrap; margin-top: 10px">
									<el-image
										class="mr10"
										v-for="item in activity.remarkImages"
										style="width: 50px; height: 50px"
										:src="item"
										:zoom-rate="1.2"
										:max-scale="7"
										:min-scale="0.2"
										:preview-src-list="activity.remarkImages"
										:initial-index="0"
										fit="cover"
									/>
								</div>
							</div>
						</div>
						<!-- 没有type只显示名字和状态 -->
						<div v-else-if="!activity.type" class="publicPadding">
							<div class="publicDisplay">
								<span>审批人</span>
								<span>{{ activity.finishTime }} &nbsp;</span>
							</div>
							<div>
								<span class="timeline_item_content_userName">{{ activity.userName }}</span>
								<span class="timeline_item_content_displayStatus">（{{ activity.displayStatus }}）</span>
							</div>
						</div>
						<!-- 小助手,已转交 -->
						<div v-else-if="activity.type === 'REDIRECT_TASK'" class="publicPadding">
							<div class="publicDisplay">
								<div>
									<span class="timeline_item_content_userName">{{ activity.userName }}</span>
									<span class="timeline_item_content_displayStatus">（{{ activity.displayStatus }}）</span>
								</div>
								<div>{{ activity.finishTime }}</div>
							</div>
							<div>{{ activity.remark }}</div>
						</div>
					</template>
				</el-timeline-item>
			</el-timeline>
		</el-scrollbar>
		<div class="timeCss_bottom" v-if="!matchStatus(flowListInfo.approveStatus)">
			<el-button type="primary" :icon="SuccessFilled" @click="ApprovedOrRefuse('agree')">审批通过</el-button>
			<el-button type="danger" :icon="CircleCloseFilled" @click="ApprovedOrRefuse('refuse')">审批驳回</el-button>
			<!-- <el-button type="primary" :icon="Promotion" @click="dialogVisible = true">去付款</el-button> -->
		</div>
		<el-dialog v-model="chooseBankVisible" append-to-body title="选择银行" width="15%" draggable :close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<el-form ref="formRef" style="max-width: 600px" :model="bankForm" label-width="auto" class="demo-ruleForm">
				<el-form-item label="银行:" prop="onlineBankId">
					<el-select v-model="bankForm.onlineBankId" filterable remote reserve-keyword placeholder="卡号" :remote-method="remoteMethod" :loading="remoteLoading" style="width: 240px">
						<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="图片">
					<uploadMf v-model:imagesStr="bankForm.images" :upstyle="{ height: 40, width: 40 }" :limit="9" ref="refuploadMf" />
				</el-form-item>
			</el-form>
			<div class="btnGruop">
				<el-button @click="chooseBankVisible = false" type="primary">取消</el-button>
				<el-button @click="handleAgree" type="primary" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="refuseVisible" append-to-body title="驳回理由" width="25%" draggable :close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<el-button @click="remark = button.text" v-for="button in buttons" :key="button.text" :type="button.type" text bg>
				{{ button.text }}
			</el-button>
			<div style="display: flex; justify-content: end; margin-top: 10px">
				<el-input v-model="remark" style="width: 100%" :rows="2" type="textarea" placeholder="驳回理由" />
			</div>
			<div class="btnGruop">
				<el-button @click="refuseVisible = false" type="primary">取消</el-button>
				<el-button @click="handleRefuse" type="primary" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="dialogVisible" append-to-body title="合并付款-明细" width="60%" draggable :close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<div v-if="dialogVisible">
				<orderPay :instanceIds="[props.viewInfo.instanceId]" @close="close" @getList="getList" />
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { dateTimeShortcuts, dateRangeShortcuts, dateShortcuts } from '/@/utils/shortcuts';
// import { ChatDotRound } from '@element-plus/icons';
import { GetWaitPayPurchaseOrderList, PassWaitPayPurchaseOrdersProcess, GetWaitPayPurchaseOrdersByCashier, ExportWaitPayPurchaseOrderList } from '/@/api/cwManager/processPayOrApproved';
import { SuccessFilled, CircleCloseFilled, Promotion } from '@element-plus/icons-vue';
import { QueryOnlineBankSet, QueryOnlineBankSetSelect } from '/@/api/cwManager/cashierSet';
import { GetCwDingProcessInfoByInstanceId } from '/@/api/cwManager/processPayOrApproved';
import { ElMessageBox, ElLoading } from 'element-plus';
import { ref, onMounted, defineEmits, defineAsyncComponent } from 'vue';
const orderPay = defineAsyncComponent(() => import('/@/views/financial/purchaseOrdersPay/components/orderPay.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
import { dayjs } from 'element-plus';
const loading = ref(false);
const props = defineProps({
	viewInfo: { type: Object, default: () => {} },
});
const emits = defineEmits(['close', 'getList']);
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);

const onlineBankId = ref();
const chooseBankVisible = ref(false);
const bankList = ref<Public.options[]>([]);
const refuseVisible = ref(false);
const remoteLoading = ref(false);
const dialogVisible = ref(false);
const remark = ref('');
const buttons = ref([
	{
		text: '金额不一致',
		type: 'primary',
	},
	{
		text: '重复订单',
		type: 'primary',
	},
	{
		text: '交易关闭',
		type: 'primary',
	},
	{
		text: '账号填写错误',
		type: 'primary',
	},
]);
const flowListInfo = ref<{
	title: string | null;
	finishTime: string | null;
	originatorDeptId: string | null;
	originatorUserId: string | null;
	originatorDeptName: string | null;
	status: string | null;
	approverUserIds: string[] | null;
	ccUserIds: string[] | null;
	result: string | null;
	businessId: string | null;
	tasks: any[] | null;
	createTime: string | null;
	formComponentValues: any[] | null;
	bizAction: string | null;
	operationRecords: any[] | null;
	approvalList: any[] | null;
	approveStatus: string | null;
}>({
	title: '',
	finishTime: '',
	approveStatus: '',
	originatorUserId: '',
	originatorDeptId: '',
	originatorDeptName: '',
	status: '',
	approverUserIds: null,
	ccUserIds: [],
	result: '',
	businessId: '',
	tasks: [],
	createTime: '',
	formComponentValues: [], // 这是一个数组
	bizAction: '',
	operationRecords: [],
	approvalList: [],
});
const bankForm = ref<{
	onlineBankId: string | number;
	images: string[];
}>({
	onlineBankId: '',
	images: [],
});
const applyType = ref(['START_PROCESS_INSTANCE', 'EXECUTE_TASK_NORMAL', 'EXECUTE_TASK_AGENT', 'EXECUTE_TASK_AUTO']);
const remarkType = ref(['ADD_REMARK']);
const copyType = ref(['账户名', '账号', '开户行', '金额']);
const auditStatus = ref(['通过', '拒绝', '驳回', '撤销']);
const close = () => {
	dialogVisible.value = false;
	emits('close');
};
const getList = () => {
	emits('getList');
};
const handleMethod = async () => {
	loading.value = true;
	const { success, data } = await GetCwDingProcessInfoByInstanceId({ instanceId: props.viewInfo.instanceId });
	if (success) {
		console.log(data, 'data');
		flowListInfo.value = data;
		let startDate = '';
		let endDate = '';
		flowListInfo.value.formComponentValues = flowListInfo.value.formComponentValues!.filter((item: any) => {
			if (item.name === '附件') {
				return false; // 过滤掉这些对象
			}
			if (item.componentType === 'DDDateRangeField') {
				// 处理日期范围字段
				if (item.value) {
					item.value = JSON.parse(item.value);
					startDate = item.value[0];
					endDate = item.value[1];
				}
			} else if (item.componentType === 'DDMultiSelectField') {
                if(item.value){
                    const parsedValue = JSON.parse(item.value);
				    item.value = parsedValue.join(',')
                }
				
			} else if (item.componentType === 'DDPhotoField') {
				// 处理照片字段
				if (item.value) {
					item.value = JSON.parse(item.value);
				}
			} else if (item.value && item.value === '["银行卡"]') {
				// 处理特定的 JSON 字符串
				item.value = JSON.parse(item.value);
			}
			return true; // 保留其他对象
		});

		// 过滤掉日期范围字段
		flowListInfo.value.formComponentValues = flowListInfo.value.formComponentValues.filter((item: any) => item.componentType !== 'DDDateRangeField');
		// 插入开始时间和结束时间
		flowListInfo.value.formComponentValues.unshift({ name: '结束时间', value: endDate });
		flowListInfo.value.formComponentValues.unshift({ name: '开始时间', value: startDate });
		flowListInfo.value.formComponentValues.unshift({ name: '所在部门', value: flowListInfo.value.originatorDeptName });
		if (flowListInfo.value.approvalList) {
			flowListInfo.value.approvalList.forEach((item: any) => {
				item.finishTime = item.finishTime ? dayjs(item.finishTime).format('MM-DD HH:mm') : '';
			});
		}
		// 格式化操作记录的日期
		if (flowListInfo.value.operationRecords) {
			flowListInfo.value.operationRecords.forEach((item: any) => {
				item.date = dayjs(item.date).format('MM-DD HH:mm');
			});
		}
		console.log(flowListInfo.value, 'flowListInfo.value');
	}
	loading.value = false;
};
const copy = (value: string) => {
	const input = document.createElement('input');
	document.body.appendChild(input);
	input.setAttribute('value', value);
	input.select();
	if (document.execCommand('copy')) {
		document.execCommand('copy');
	}
	document.body.removeChild(input);
	window.$message.success('复制成功');
};
const ApprovedOrRefuse = (type: string) => {
	console.log(flowListInfo.value, 'flowListInfo.value');
	if (flowListInfo.value.approveStatus != '审批中') return window.$message.error('该条数据已审批');
	ElMessageBox.confirm(`确认${type == 'agree' ? '审核' : '驳回'}该条数据吗?`)
		.then(async () => {
			if (type == 'agree') {
				bankForm.value.onlineBankId = '';
				refuploadMf.value?.clearMethod()
				await remoteMethod('');
				const obj = bankList.value.filter((item: any) => item.name == props.viewInfo.accountName)[0];
				bankForm.value.onlineBankId = obj ? obj.value : '';
				chooseBankVisible.value = true;
			} else {
				remark.value = '';
				refuseVisible.value = true;
			}
		})
		.catch((error) => {
			console.log(error);
			window.$message.info('已取消');
		});
};

const matchingStr = (str: string) => {
	return copyType.value.some((item) => str.includes(item));
};

const matchStatus = (str: string | null) => {
	return auditStatus.value.some((item) => str?.includes(item));
};
const remoteMethod = async (account: string) => {
	remoteLoading.value = true;
	const { data, success } = await QueryOnlineBankSetSelect({ account, accountName: props.viewInfo.accountName });
	if (success) {
		bankList.value = data.map((item: any) => {
			return {
				label: item.accountName + '-' + item.bankType + '-' + item.account,
				value: item.id,
				name: item.busAccountName,
			};
		});
		remoteLoading.value = false;
	} else {
		bankList.value = [];
		remoteLoading.value = false;
	}
};

const handleAgree = async () => {
	if (!bankForm.value.onlineBankId) return window.$message.error('请选择银行');
	const { success } = await PassWaitPayPurchaseOrdersProcess({ instanceIds: [props.viewInfo.instanceId], type: 'agree', onlineBankId: bankForm.value.onlineBankId, images: bankForm.value.images });
	if (success) {
		window.$message.success('审核成功');
		chooseBankVisible.value = false;
		emits('close');
		emits('getList');
	}
};
const handleRefuse = async () => {
	if (!remark.value) return window.$message.error('请输入驳回理由');
	const { success } = await PassWaitPayPurchaseOrdersProcess({ instanceIds: [props.viewInfo.instanceId], type: 'refuse', remark: remark.value });
	if (success) {
		window.$message.success('驳回成功');
		refuseVisible.value = false;
		emits('close');
		emits('getList');
	}
};
onMounted(() => {
	console.log(props.viewInfo, 'props.viewInfo');
	handleMethod();
});
</script>

<style scoped lang="scss">
.timeCss {
	display: flex;
	justify-content: space-between;
	width: 100%;
	height: 100%;
	background-color: #f2f4f6;
	position: relative;
	.timeCss_Top {
		padding-right: 10px;
		.AuditImg {
			width: 100px;
			height: 100px;
		}
	}

	.timeline {
		position: relative;
		margin: 0 auto;
		background-color: white;
		padding: 3% 5%;
		.content {
			padding-left: 20px;
			position: relative;
			display: flex;
			align-items: center;
			.avatar {
				width: 40px;
				height: 40px;
				border-radius: 20%;
				margin-right: 20px;
			}
			.timeline_item_content {
				flex-grow: 1;
				display: flex;
				flex-direction: column;
				.timeline_item_content_left {
					display: flex;
					justify-content: space-between;
					margin-bottom: 3px;
				}
			}
		}
		.timeline_item_content_userName {
			margin-right: 5px;
			color: #a8a9aa;
		}
		.timeline_item_content_displayStatus {
			color: #a8a9aa;
		}
		.PROCESS_CC_word {
			width: 30px;
			height: 30px;
			line-height: 30px;
			text-align: center;
			border-radius: 50%;
			background-color: #0080ff;
			font-size: 12px;
			color: #fff;
		}
		.PROCESS_CC_Css {
			display: flex;
			flex-wrap: wrap;
			.PROCESS_CC_Css_item {
				margin: 0 15px 15px 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
			}
		}
		.publicPadding {
			padding-left: 20px;
		}
		.publicDisplay {
			display: flex;
			justify-content: space-between;
			margin-bottom: 10px;
		}
	}
	.timeCss_bottom {
		position: fixed;
		bottom: 0;
		padding-left: 30px;
		width: 100%;
		height: 50px;
		background-color: #fff;
		display: flex;
		align-items: center;
	}

	.ProcessTop {
		height: 12%;
		display: flex;
		flex-direction: column;
		padding: 10px;
		background-color: white;

		.ProcessTop_title {
			font-size: 16px;
			font-weight: bold;
			margin: 2% 0;
		}

		.ProcessTop_company {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.ProcessTop_company_left {
				display: flex;
				align-items: center;

				.ProcessTop_company_left_img {
					width: 20px;
					height: 20px;
					margin-right: 5px;
					border-radius: 50%;
				}
			}
		}
	}
	.ProcessContent {
		margin: 3% 0;
		background-color: white;
		padding: 3%;

		.ProcessContent_item {
			display: flex;
			flex-direction: column;
			margin-bottom: 25px;

			.ProcessContent_item_name {
				margin-bottom: 5px;
				color: #858e99;
			}
		}
	}
}

::v-deep .el-timeline-item__dot {
	top: -4px;
	left: -10px;
}

.btnGruop {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

.description {
	width: 100%;
	word-break: break-all;
}
</style>
