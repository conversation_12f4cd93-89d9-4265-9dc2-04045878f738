import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Inventory}/Purchase/`;

//采购单管理-退款-查询采购单退款明细
export const GetPurchaseOrderRefundDetails = (params: any, config = {}) => request.post(apiPrefix + 'GetPurchaseOrderRefundDetails', params, config);

//采购单管理-退款-生成采购单退款明细
export const AddPurchaseOrderRefundDetails = (params: any, config = {}) => request.post(apiPrefix + 'AddPurchaseOrderRefundDetails', params, config);

//采购单管理-退款-添加采购单退款明细
export const GeneratePurchaseOrderRefundDetails = (params: any, config = {}) => request.post(apiPrefix + 'GeneratePurchaseOrderRefundDetails', params, config);

//审批
export const approvedPurchaseOrderRefundToCw = (params: any, config = {}) => request.post(apiPrefix + 'ApprovedPurchaseOrderRefundToCw', params, config);
//采购单管理-退款-编辑
export const editPurchaseOrderRefundDetailsERP = (params: any, config = {}) => request.post(apiPrefix + 'EditPurchaseOrderRefundDetailsERP', params, config);
//采购单管理-退款
export const editAddPurchaseOrderRefundDetails = (params: any, config = {}) => request.post(apiPrefix + 'EditAddPurchaseOrderRefundDetails', params, config);

//采购单管理-退款-导出
export const ExportPurchaseOrderRefundDetails = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPurchaseOrderRefundDetails', params, config);

//采购单管理-退款新查询
export const getPurchaseOrderRefundDetailsERP = (params: any, config = {}) => request.post(apiPrefix + 'GetPurchaseOrderRefundDetailsERP', params, config);

//采购单管理-编辑出库金额
export const editPurchaseOrderRefundDetails = (params: any, config = {}) => request.post(apiPrefix + 'EditPurchaseOrderRefundDetails', params, config);

//采购单管理-编辑退款原因
export const editPurchaseOrderRefundReason = (params: any, config = {}) => request.post(apiPrefix + 'EditPurchaseOrderRefundReason', params, config);
