<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="提现登记" name="first" style="height: 100%">
					<withdrawalRegistration />
				</el-tab-pane>
				<el-tab-pane label="提现信息" name="second" style="height: 100%" lazy>
					<payoutInformation />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const withdrawalRegistration = defineAsyncComponent(() => import('./withdrawalRegistration.vue'));
const payoutInformation = defineAsyncComponent(() => import('./payoutInformation.vue'));
const activeName = ref('first');
</script>
