/* tslint:disable */
/* eslint-disable */
/**
 * GoView
 * GoView 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 2.2.8
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { GoViewProCreateOutput } from './go-view-pro-create-output';
 /**
 * GoView 返回结果
 *
 * @export
 * @interface GoViewResultGoViewProCreateOutput
 */
export interface GoViewResultGoViewProCreateOutput {

    /**
     * 状态码
     *
     * @type {number}
     * @memberof GoViewResultGoViewProCreateOutput
     */
    code?: number;

    /**
     * 信息
     *
     * @type {string}
     * @memberof GoViewResultGoViewProCreateOutput
     */
    msg?: string | null;

    /**
     * @type {GoViewProCreateOutput}
     * @memberof GoViewResultGoViewProCreateOutput
     */
    data?: GoViewProCreateOutput;

    /**
     * 总数
     *
     * @type {number}
     * @memberof GoViewResultGoViewProCreateOutput
     */
    count?: number | null;
}
