import request from '/@/utils/yhrequest';
//凭证明细
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/AccountsVouchers/`;

const apiPrefixa = `${import.meta.env.VITE_APP_BASE_API_CwManage}/CashOutlayChart/`;

const apiPrefixb = `${import.meta.env.VITE_APP_BASE_API_CwManage}/AccountsVoucherInfo/`;

//凭证明细-查询
export const QueryAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'QueryAccountsVouchers', params, config);

//凭证明细-查询
export const UpdateAccountsVouchersDetail = (params: any, config = {}) => request.post(apiPrefix + 'UpdateAccountsVouchersDetail', params, config);

//凭证明细-生成凭证
export const AgreeAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'AgreeAccountsVouchers', params, config);

//凭证明细-一键生成凭证
export const BatchAgreeAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'BatchAgreeAccountsVouchers', params, config);

//凭证明细-一键确认无误
export const BatchConfirmAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'BatchConfirmAccountsVouchers', params, config);

//凭证明细-确认无误
export const ConfirmAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'ConfirmAccountsVouchers', params, config);

//凭证明细-批量审批现金支出科目
export const BatchApproverAccountsVouchersCashChart = (params: any, config = {}) => request.post(apiPrefix + 'BatchApproverAccountsVouchersCashChart', params, config);

//凭证明细-审批现金支出科目
export const ApproverAccountsVouchersCashChart = (params: any, config = {}) => request.post(apiPrefix + 'ApproverAccountsVouchersCashChart', params, config);

//凭证明细-编辑现金支出科目
export const UpdateAccountsVouchersCashChart = (params: any, config = {}) => request.post(apiPrefix + 'UpdateAccountsVouchersCashChart', params, config);

//凭证明细-保存并确认凭证
export const UpdateAndConfirmAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'UpdateAndConfirmAccountsVouchers', params, config);

//凭证明细-保存并生成凭证
export const UpdateAndAgreeAccountsVouchers = (params: any, config = {}) => request.post(apiPrefix + 'UpdateAndAgreeAccountsVouchers', params, config);

//凭证明细-导出凭证主列表
export const ExportAccountsVouchers = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAccountsVouchers', params, config);

//凭证明细-导出支出科目主列表
export const ExportAccountsVouchersCashChart = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAccountsVouchersCashChart', params, config);

//凭证明细-导出明细
export const ExportAccountsVouchersDetail = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAccountsVouchersDetail', params, config);

//支出科目汇总-一级科目-查询
export const QueryCashOutlayChartFirstChart = (params: any, config = {}) => request.post(apiPrefixa + 'QueryCashOutlayChartFirstChart', params, config);

//支出科目汇总-一级科目-导出
export const ExportCashOutlayChartFirstChart = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixa + 'ExportCashOutlayChartFirstChart', params, config);

//支出科目汇总-二级科目-查询
export const QueryCashOutlayChartSecondChart = (params: any, config = {}) => request.post(apiPrefixa + 'QueryCashOutlayChartSecondChart', params, config);

//支出科目汇总-二级科目-导出
export const ExportCashOutlayChartSecondChart = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixa + 'ExportCashOutlayChartSecondChart', params, config);

//凭证信息-查询
export const QueryAccountsVoucherInfo = (params: any, config = {}) => request.post(apiPrefixb + 'QueryAccountsVoucherInfo', params, config);

//凭证信息-导出
export const ExportAccountsVoucherInfo = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixb + 'ExportAccountsVoucherInfo', params, config);
