import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/FeeData/`;

//查询费用数据
export const QueryFeeData = (params: any) => request.post(apiPrefix + 'QueryFeeData', params);

//查询所有部门 QueryDept
export const QueryDept = (params: any) => request.post(apiPrefix + `QueryDept?query=${params}`);

//查询关联流水 QueryBankFlowMatch
export const QueryBankFlowMatch = (params: any) => request.post(apiPrefix + 'QueryBankFlowMatch', params);

//解除费用关联 RelieveFeeData
export const RelieveFeeData = (params: any) => request.post(apiPrefix + 'RelieveFeeData', params);

//忽略费用IgnoreFeeData
export const IgnoreFeeData = (params: any) => request.post(apiPrefix + 'IgnoreFeeData', params);

//解除忽略费用IgnoreFeeData
export const UnIgnoreFeeData = (params: any) => request.post(apiPrefix + 'UnIgnoreFeeData', params);

//编辑费用数据UpDateFeeData
export const UpDateFeeData = (params: any) => request.post(apiPrefix + 'UpDateFeeData', params);

//查询费用数据
export const QueryPurchaseOrderRefundRecordList = (params: any) => request.post(apiPrefix + 'QueryPurchaseOrderRefundRecordList', params);

//审批退款数据
export const ApproveReturnAmount = (params: any) => request.post(apiPrefix + 'ApproveReturnAmount', params);

//审批费用数据
export const ApproveFeeData = (params: any) => request.post(apiPrefix + 'ApproveFeeData', params);

//获取费用分类
export const QueryFeeDataApplyFeeType = (params: any) => request.post(apiPrefix + 'QueryFeeDataApplyFeeType', params);

//获取发起人
export const QueryFeeDataDeptList = (params: any) => request.post(apiPrefix + 'QueryFeeDataDeptList', params);

//导出
export const ExportPurchaseOrderRefundRecordList = (params: any) => request.post(apiPrefix + 'ExportPurchaseOrderRefundRecordList', params);

// export const ExportPurchaseOrderRefundRecordList = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPurchaseOrderRefundRecordList', params, config);

//解除费用关联 RelieveFeeData
export const RelieveReturnAmount = (params: any) => request.post(apiPrefix + 'RelieveReturnAmount', params);

//解除费用关联 RelieveFeeData
export const IgnoreReturnAmount = (params: any) => request.post(apiPrefix + 'IgnoreReturnAmount', params);

//解除费用忽略 RelieveFeeData
export const UnIgnoreReturnAmount = (params: any) => request.post(apiPrefix + 'UnIgnoreReturnAmount', params);

//解除费用关联 RelieveFeeData
export const UpdateReturnAmount = (params: any) => request.post(apiPrefix + 'UpdateReturnAmount', params);

export const ExportFeeData = (params: any) => request.post(apiPrefix + 'ExportFeeData', params);

//采购单退款-审批(通过)
export const approvedPurchaseOrderRefundToCw = (params: any, config = {}) => request.post(apiPrefix + 'ApprovedPurchaseOrderRefundToCw', params, config);

//采购单退款-审批(驳回)
export const RefusePurchaseOrderRefund = (params: any, config = {}) => request.post(apiPrefix + 'RefusePurchaseOrderRefund', params, config);

export const UpDateFeeDataAmount = (params: any, config = {}) => request.post(apiPrefix + 'UpDateFeeDataAmount', params, config);
