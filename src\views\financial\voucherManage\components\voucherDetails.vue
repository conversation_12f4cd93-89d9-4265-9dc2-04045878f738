<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="日期类型" :clearable="false" style="width: 85px; margin: 0 0 5px 0">
					<el-option label="交易日期" value="交易日期" />
					<el-option label="支付日期" value="支付日期" />
					<el-option label="审批日期" value="审批日期" />
					<el-option label="生成日期" value="生成日期" />
					<el-option label="创建日期" value="创建日期" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始日期" endPlaceholder="结束日期" style="width: 225px" />
				<el-input v-model.trim="query.businessId" placeholder="钉钉流程号" class="publicCss" clearable maxlength="40" />
				<el-input v-model.trim="query.title" class="publicCss" placeholder="流程名称" clearable maxlength="50" />
				<el-input v-model.trim="query.expressCom" placeholder="快递公司" class="publicCss" clearable maxlength="50" />
				<bankSelect v-model:value="query.account" placeholder="姓名/账户" class="publicCss" clearable filterable />
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
					<el-option key="未生成草稿" label="未生成草稿" value="未生成草稿" />
					<el-option key="已生成草稿" label="已生成草稿" value="已生成草稿" />
					<el-option key="凭证已确认" label="凭证已确认" value="凭证已确认" />
					<el-option key="已生成正式" label="已生成正式" value="已生成正式" />
				</el-select>
				<el-input v-model.trim="query.agreeUserName" placeholder="生成人" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.usePlatform" placeholder="平台" class="publicCss" clearable filterable>
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.label" />
					<el-option key="线下" label="线下" value="线下" />
					<el-option key="跨境-TEMU" label="跨境-TEMU" value="跨境-TEMU" />
					<el-option key="跨境-SHEIN" label="跨境-SHEIN" value="跨境-SHEIN" />
				</el-select>
				<el-select v-model="query.approverStatus" placeholder="审核状态" class="publicCss" clearable>
					<el-option key="待审核" label="待审核" value="待审核" />
					<el-option key="已审核" label="已审核" value="已审核" />
				</el-select>
				<el-select v-model="query.sumType" placeholder="费用大类" class="publicCss" filterable clearable>
					<el-option v-for="item in expenseCategoryList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.firstTitle" placeholder="一级科目" class="publicCss" clearable filterable>
					<el-option v-for="item in subjectList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.secondTitle" placeholder="二级科目" class="publicCss" clearable filterable>
					<el-option v-for="item in subjectListTwo" :key="item.title" :label="item.title" :value="item.title" />
				</el-select>
				<el-select v-model="query.feeType" placeholder="费用类型" class="publicCss" clearable filterable>
					<el-option v-for="item in expenseTypeList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.feeArea" placeholder="费用区域" class="publicCss" clearable filterable>
					<el-option v-for="item in costAreaList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.value2" placeholder="是否月结" class="publicCss" clearable>
					<el-option key="是" label="是" value="是" />
					<el-option key="否" label="否" value="否" />
				</el-select>
				<el-input v-model.trim="query.value1" class="publicCss" placeholder="供应商名称" clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList()" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="voucherDetails202412271407"
				:tableCols="tableCols"
				:pageSize="30"
				:pageSizes="[30, 50, 100, 200, 300]"
				:query="query"
				orderBy="createdTime"
				:isAsc="false"
				:scrollY="{ enabled: false, gt: 0 }"
				:scrollX="{ enabled: false, gt: 0 }"
				isNeedCheckBox
				@select="checkboxChange"
				:isNeedExpand="subtable"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:queryApi="QueryAccountsVouchers"
			>
				<template #expandCols="{ row }">
					<div v-if="row.labels && row.labels.length > 0" style="margin-left: 160px" class="expanded-container">
						<div v-for="(item, index) in row.labels" :key="index" class="label-item">
							<div>{{ item.key }}:</div>
							<div>{{ item.value }}</div>
						</div>
					</div>
					<div style="padding: 0 320px; display: block;">
						<vxe-table
							class="mytable-style"
							border
							show-overflow
							keep-source
							:height="getTableHeight(row.childs)"
							ref="tableRef"
							:loading="loading"
							:data="row.childs"
							:cell-style="cellStyle"
							:header-cell-style="{ 'text-align': 'center', background: '#D3D3D3' }"
						>
							<vxe-column field="chartRemark" title="摘要" width="235"></vxe-column>
							<vxe-column field="chartTitle" title="科目名称"></vxe-column>
							<vxe-column field="chartCode" title="科目代码" width="120"></vxe-column>
							<vxe-column field="qty" title="数量" width="120"></vxe-column>
							<vxe-column field="price" title="单价" width="120"></vxe-column>
							<vxe-column field="inAmount" title="借方金额" width="120" align="right" :formatter="formatAmount"></vxe-column>
							<vxe-column field="outAmount" title="贷方金额" width="120" align="right" :formatter="formatAmount"></vxe-column>
							<vxe-column field="auxiliaryType" title="辅助核算类型" width="150"></vxe-column>
							<vxe-column field="auxiliaryName" title="辅助核算名称" width="200"></vxe-column>
							<vxe-column field="auxiliaryCode" title="辅助核算编码" width="130"></vxe-column>
						</vxe-table>
					</div>
				</template>
				<template #toolbar_buttons>
					<div class="toolbar-container">
						<div>
							<el-button @click="onOneClickGenerate(1)" type="primary" :disabled="officialDocument">一键生成正式凭证</el-button>
							<el-button @click="onOneClickGenerate(2)" type="primary" :disabled="confirmVerify">一键确认无误</el-button>
							<el-button @click="onOneClickGenerate(3)" type="primary" :disabled="confirmVerify">一键审批现金支出科目</el-button>
							<el-dropdown @command="computedProps" style="margin: 0 12px" size="large">
								<el-button type="primary">
									导出<i class="el-dropdown-link"></i>
									<el-icon class="el-icon--right">
										<arrow-down />
									</el-icon>
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="true">主列表</el-dropdown-item>
										<el-dropdown-item command="false">导出明细</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
						<el-radio-group v-model="columnToggle" size="small" style="margin-left: auto" @change="columnToggleChange">
							<el-radio-button label="凭证" value="凭证" />
							<el-radio-button label="支出科目" value="支出科目" />
						</el-radio-group>
					</div>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="编辑" width="1600" draggable overflow style="margin-top: -18vh !important" :close-on-click-modal="false">
		<div style="padding-top: 10px" v-loading="selectloading">
			<voucherEditComponent
				:singleData="singleData"
				:accountLists="accountList"
				:accountDatas="accountData"
				:abstract="abstract"
				ref="ruleFormRef"
				@close="editVisible = false"
				@onStorageMethod="onSingleSave"
				v-if="editVisible"
			/>
		</div>
	</el-dialog>

	<el-dialog v-model="accountVisible" title="编辑科目" width="500" draggable overflow style="margin-top: -18vh !important" :close-on-click-modal="false" @close="handleClose">
		<div style="height: 100px; padding-top: 10px; display: flex; justify-content: center" v-loading="accountloading">
			<el-form ref="accountFormRef" style="max-width: 600px" :model="accountForm" :rules="accountrules" label-width="auto" class="demo-ruleForm" status-icon>
				<el-form-item label="一级科目" prop="firstTitle">
					<el-select v-model="accountForm.firstTitle" placeholder="一级科目" style="width: 250px" clearable filterable @change="firstChange">
						<el-option v-for="item in subjectList" :key="item" :label="item" :value="item" />
					</el-select>
				</el-form-item>
				<el-form-item label="二级科目" prop="secondTitle">
					<el-select v-model="accountForm.secondTitle" placeholder="二级科目" style="width: 250px" clearable filterable>
						<el-option v-for="item in subjectListTwo" :key="item.title" :label="item.title" :value="item.title" />
					</el-select>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="accountVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit">保存</el-button>
			</div>
		</template>
	</el-dialog>

	<el-drawer v-model="ProcessApprovalsDrawer" title="查看流程">
		<approvalProcess :viewInfo="viewInfo" v-if="ProcessApprovalsDrawer" @close="close" @getList="getList" />
	</el-drawer>
</template>

<script setup lang="ts" name="">
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const approvalProcess = defineAsyncComponent(() => import('/@/components/yhCom/approvalProcess.vue'));
import { dateTimeShortcuts } from '/@/utils/shortcuts';
import { platformlist } from '/@/utils/tools';
const pageLoading = ref(false);
const table = ref();
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, computed, nextTick } from 'vue';
import { VxeTableInstance } from 'vxe-table';
import {
	QueryAccountsVouchers,
	UpdateAccountsVouchersDetail,
	AgreeAccountsVouchers,
	ConfirmAccountsVouchers,
	BatchAgreeAccountsVouchers,
	BatchConfirmAccountsVouchers,
	ApproverAccountsVouchersCashChart,
	BatchApproverAccountsVouchersCashChart,
	UpdateAccountsVouchersCashChart,
	ExportAccountsVouchers,
	ExportAccountsVouchersCashChart,
	ExportAccountsVouchersDetail,
	UpdateAndConfirmAccountsVouchers,
	UpdateAndAgreeAccountsVouchers,
	QueryCashOutlayChartSecondChart,
} from '/@/api/cwManager/accountsVouchers';
import { QueryCashOutlayChart } from '/@/api/cwManager/accountsChart';
const bankSelect = defineAsyncComponent(() => import('/@/components/yhCom/bankSelect.vue'));
const voucherEditComponent = defineAsyncComponent(() => import('./voucherEditComponent.vue'));
import { QueryFeeDataApplyFeeType } from '/@/api/cwManager/feeData';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { QueryAccountsAuxiliary, QueryAccountsChart } from '/@/api/cwManager/accountsChart';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
const confirmVerify = ref(false);
const officialDocument = ref(false);
const listLoading = ref(false);
const loading = ref(false);
const selectloading = ref(false);
const accountloading = ref(false);
const expensesList = ref<Public.options[]>([]);
interface AccountingCode {
	value: string | number;
	label: string;
}
const accountingCodeList = ref<AccountingCode[]>([]);
const accountList = ref<AccountingCode[]>([]);
const accountData = ref([]);
const tableRef = ref<VxeTableInstance>();
interface CheckboxItem {
	id: string | number;
	status: string;
}
const checkboxList = ref<CheckboxItem[]>([]);
const tableData = ref<CheckboxItem[]>([]);
interface CheckboxItem {
	verify: boolean;
}
const editVisible = ref(false);
const accountVisible = ref(false);
const ProcessApprovalsDrawer = ref(false);
const subjectList = ref([]);
const subjectListTwo = ref<any>([]);
const expenseTypeList = ref<any>([]);
const expenseCategoryList = ref<any>([]);
const costAreaList = ref<any>([]);
const subjectListTwoCopy = ref<any>([]);
const singleData = ref([]);
const abstract = ref('');
const columnToggle = ref('凭证');
const subtable = ref(true);
const ruleFormRef = ref<FormInstance | null>(null);
const accountFormRef = ref<FormInstance | null>(null);
type SingleForm = {
	auxiliaryType: string;
	auxiliaryCode: number | undefined | string;
	auxiliaryName: string;
	childs: any[];
};

const singleform = ref<SingleForm>({
	auxiliaryType: '',
	auxiliaryCode: '',
	auxiliaryName: '',
	childs: [],
});

const accountForm = ref({
	firstTitle: '',
	secondTitle: '',
	sumType: '',
	feeArea: '',
	feeType: '',
});

const viewInfo = ref({
	instanceId: '',
	accountName: '',
	status: '',
});

const accountrules = ref<FormRules>({
	firstTitle: [{ required: true, message: '请选择一级科目', trigger: 'blur' }],
	secondTitle: [{ required: true, message: '请选择二级科目', trigger: 'blur' }],
});

const query = ref({
	businessId: '',
	sumType: '',
	firstTitle: '',
	secondTitle: '',
	feeType: '',
	feeArea: '',
	approverStatus: '',
	expressCom: '',
	title: '',
	account: '',
	status: '',
	agreeUserName: '',
	usePlatform: '',
	timeType: '交易日期',
	startTime: '',
	endTime: '',
	value1: '',
	value2: '',
});

const viewProcess = (row: any) => {
	viewInfo.value = {
		instanceId: row.instanceId,
		accountName: row.payAccount,
		status: row.status,
	};
	ProcessApprovalsDrawer.value = true;
};

const close = () => {
	ProcessApprovalsDrawer.value = false;
};

const changeTime = (val: any) => {
	query.value.startTime = val ? val[0] : '';
	query.value.endTime = val ? val[1] : '';
};

const cellStyle = () => {
	return { 'background-color': '#D3D3D3' };
};

const getTableHeight = (data: any) => {
	const rowHeight = 36; // 每行的高度
	const headerHeight = 36; // 表头高度
	const extraSpace = 5; // 额外的间距
	let height = data.length * rowHeight + headerHeight + extraSpace;
	const minHeight = 10; // 最小高度
	if (height < minHeight) {
		height = minHeight;
	}
	return String(height);
};

// 千分位格式化函数
const formatAmount = ({ cellValue }: { cellValue: number }) => {
	if (!cellValue && cellValue != 0) return;
	return cellValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const columnToggleChange = (e: any) => {
	nextTick(() => {
		if (e == '凭证') {
			table.value?.showHidenColums(
				[
					'expressCom',
					'occurenceDate',
					'indexNo',
					'useDepartment',
					'usePlatform',
					'warehouseClass',
					'rawAmount',
					'payTime',
					'sumType',
					'firstTitle',
					'secondTitle',
					'feeType',
					'feeArea',
					'approverStatus',
					'approverUserName',
					'approverTime',
					'failReason',
				],
				false
			);
			table.value?.showHidenColums(['accountName', 'status', 'failReason', 'agreeUserName', 'agreeTime', 'createdTime'], true);
			subtable.value = true;
		} else {
			subtable.value = false;
			table.value?.showHidenColums(['accountName', 'status', 'failReason', 'agreeUserName', 'agreeTime', 'createdTime'], false);
			table.value?.showHidenColums(
				[
					'expressCom',
					'occurenceDate',
					'indexNo',
					'useDepartment',
					'usePlatform',
					'warehouseClass',
					'rawAmount',
					'payTime',
					'sumType',
					'firstTitle',
					'secondTitle',
					'feeType',
					'feeArea',
					'approverStatus',
					'approverUserName',
					'approverTime',
					'failReason',
				],
				true
			);
		}
	});
	tableData.value.forEach((item: any) => {
		item.verify = !item.verify;
	});
	table.value.onAssignedData(tableData.value);
};

const computedProps = async (val: any) => {
	try {
		let response;
		listLoading.value = true;
		if (val == 'true' && columnToggle.value == '凭证') {
			response = await ExportAccountsVouchers({ ...query.value });
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
		} else if (val == 'true' && columnToggle.value == '支出科目') {
			response = await ExportAccountsVouchersCashChart({ ...query.value });
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
		} else if (val == 'false') {
			response = await ExportAccountsVouchersDetail({ ...query.value });
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
		}
		getList();
		listLoading.value = false;
	} catch (error) {
		window.$message.error('计算失败');
	}
};

const clearDataMethod = () => {
	singleform.value = {
		auxiliaryType: '',
		auxiliaryCode: '',
		auxiliaryName: '',
		childs: [],
	};
	// accountingCodeList.value = [];
};

const onGenerate = (row: any, val: any) => {
	const confirmText = val === 3 ? '此操作将审核科目, 是否继续?' : val === 2 ? '此操作将确认无误, 是否继续?' : '此操作将生成正式凭证, 是否继续?';

	const apiCall = val === 3 ? ApproverAccountsVouchersCashChart : val === 2 ? ConfirmAccountsVouchers : AgreeAccountsVouchers;

	ElMessageBox.confirm(confirmText, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await apiCall(row);
			if (success) {
				const successMessage = val === 3 ? '审核科目成功' : val === 2 ? '确认无误成功' : '生成正式凭证成功';
				ElMessage.success(successMessage);
				getList();
			}
		})
		.catch(() => {});
};

const onSingleSave = debounce(async (data: any, val: any) => {
	selectloading.value = true;
	singleform.value.childs = data;
	let apiCall;
	let successMessage;
	switch (val) {
		case 2:
			apiCall = UpdateAndConfirmAccountsVouchers;
			successMessage = '保存并确认成功';
			break;
		case 3:
			apiCall = UpdateAndAgreeAccountsVouchers;
			successMessage = '保存并确认生成正式凭证成功';
			break;
		case 4:
			apiCall = UpdateAndAgreeAccountsVouchers;
			successMessage = '生成凭证成功';
			break;
		default:
			apiCall = UpdateAccountsVouchersDetail;
			successMessage = '编辑保存成功';
	}
	const { success } = await apiCall({ ...singleform.value });
	selectloading.value = false;
	if (success) {
		ElMessage.success(successMessage);
		editVisible.value = false;
		getList();
	}
});

const onEditAccount = (row: any) => {
	accountForm.value = JSON.parse(JSON.stringify(row));
	accountVisible.value = true;
};

const handleSubmit = () => {
	if (accountFormRef.value) {
		submitForm(accountFormRef.value);
	}
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (!valid) return;
		accountloading.value = true;
		const { data, success } = await UpdateAccountsVouchersCashChart({ ...accountForm.value });
		accountloading.value = false;
		if (success) {
			ElMessage.success(data ? data : '保存成功');
			accountVisible.value = false;
			getList();
		}
	});
};

const handleClose = () => {
	if (accountFormRef.value) {
		accountFormRef.value.resetFields();
		accountFormRef.value.clearValidate();
		subjectListTwo.value = subjectListTwoCopy.value;
	}
};

const firstChange = (e: any) => {
	accountForm.value.secondTitle = '';
	if (e) {
		const filteredList = subjectListTwoCopy.value.filter((item: any) => item.firstTitle == e);
		accountForm.value.sumType = filteredList[0]?.sumType;
		accountForm.value.feeArea = filteredList[0]?.feeArea;
		accountForm.value.feeType = filteredList[0]?.feeType;
		subjectListTwo.value = filteredList;
	} else {
		accountForm.value.sumType = '';
		accountForm.value.feeArea = '';
		accountForm.value.feeType = '';
		subjectListTwo.value = subjectListTwoCopy.value;
	}
};

const onEditProof = async (row: any) => {
	listLoading.value = true;
	await remoteMethod(row.auxiliaryType);
	clearDataMethod();
	setTimeout(() => {
		singleform.value = JSON.parse(JSON.stringify(row));
		listLoading.value = false;
		singleData.value = JSON.parse(JSON.stringify(row.childs || []));
		abstract.value = row?.businessId ? row.businessId : '';
		editVisible.value = true;
	}, 1000);
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
	const valArrList = new Set(val.map((item: any) => item.status));
	officialDocument.value = valArrList.has('已生成正式');
	confirmVerify.value = valArrList.has('凭证已确认') || valArrList.has('已生成正式');
};

const onOneClickGenerate = (val: number) => {
	if (checkboxList.value.length === 0) {
		const warningText = val === 3 ? '请选择需审批现金支出科目的数据' : val === 2 ? '请选择需确认无误的数据' : '请选择需生成正式凭证的数据';
		ElMessage.warning(warningText);
		return;
	}
	const confirmText = val === 3 ? '此操作将一键审批现金支出科目, 是否继续?' : val === 2 ? '此操作将一键确认无误, 是否继续?' : '此操作将一键生成正式凭证, 是否继续?';
	const apiCall = val === 3 ? BatchApproverAccountsVouchersCashChart : val === 2 ? BatchConfirmAccountsVouchers : BatchAgreeAccountsVouchers;
	ElMessageBox.confirm(confirmText, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { data, success } = await apiCall(checkboxList.value);
			if (success) {
				ElMessage.success(data ? data : '操作成功');
				table.value?.clearCheckboxRow();
				checkboxList.value = [];
				getList();
			}
		})
		.catch(() => {});
};

const getList = () => {
	table.value.query.currentPage = 1;
	if (query.value.firstTitle) {
		columnToggle.value = '支出科目';
		columnToggleChange('支出科目');
	}
	table.value.getList();
	table.value.clearSelection();
	checkboxList.value = [];
	checkboxChange([]);
};

const remoteMethod = async (query: any) => {
	singleform.value.auxiliaryCode = '';
	singleform.value.auxiliaryName = '';
	if (query) {
		selectloading.value = true;
		setTimeout(async () => {
			let type = query;
			const { data, success } = await QueryAccountsAuxiliary({ currentPage: 1, pageSize: 9999999, typeList: [type] });
			selectloading.value = false;
			if (!success) return;
			accountingCodeList.value = data.list.map((item: any) => {
				return { value: item.code, label: item.name };
			});
		}, 300);
	} else {
		accountingCodeList.value = [];
	}
};

const init = async () => {
	const res1 = await QueryAccountsChart({ currentPage: 1, pageSize: 9999999 });
	accountList.value = res1.data.list.map((item: any) => {
		return {
			value: item.id,
			label: item.title,
			hasQuantity: item.hasQuantity,
			supplementary: item.supplementary,
			fullTitle: item.fullTitle,
		};
	});
	accountData.value = res1.data.list;
	const { data: data1, success: success1 } = await QueryFeeDataApplyFeeType({});
	if (!success1) return;
	expensesList.value = data1;

	const { data, success } = await QueryCashOutlayChart({ currentPage: 1, pageSize: 100000 });
	if (!success) return;
	subjectList.value = Array.from(new Set(data.list.filter((item: any) => item.pId == null).map((item: any) => item.title)));
	expenseTypeList.value = Array.from(new Set(data.list.filter((item: any) => item.feeType != null && item.feeType != undefined).map((item: any) => item.feeType)));
	expenseCategoryList.value = Array.from(new Set(data.list.filter((item: any) => item.feeType != null && item.feeType != undefined).map((item: any) => item.sumType)));
	costAreaList.value = Array.from(new Set(data.list.filter((item: any) => item.feeArea != null && item.feeArea != undefined).map((item: any) => item.feeArea)));
	interface Item {
		id: string;
		pId: string | null;
		title: string;
		firstTitle?: string;
	}
	const parentTitleMap: { [key: string]: string } = {};
	const storageList: Item[] = data.list;
	storageList.forEach((item) => {
		if (item.pId === null) {
			parentTitleMap[item.id] = item.title;
		}
	});
	storageList.forEach((item) => {
		if (item.pId !== null) {
			item.firstTitle = parentTitleMap[item.pId] ?? undefined;
		}
	});
	subjectListTwo.value = Array.from(
		new Set(
			storageList
				.filter((item: any) => item.pId != null)
				.map((item: any) => {
					return { title: item.title, firstTitle: item.firstTitle, sumType: item.sumType, feeArea: item.feeArea, feeType: item.feeType };
				})
		)
	);
	subjectListTwoCopy.value = JSON.parse(JSON.stringify(subjectListTwo.value));
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'businessId', title: '钉钉流程号', width: '160', type: 'click', handle: (row: any) => viewProcess(row), copy: true },
	{ sortable: true, field: 'applyFeeType', title: '费用分类', width: '100' },
	{ sortable: true, field: 'expressCom', title: '快递公司', width: '90', visible: false },
	{ sortable: true, field: 'title', title: '流程名称', width: '170' },
	{ sortable: true, field: 'occurenceDate', title: '交易日期', width: '90', formatter: 'formatDate', visible: false },
	{ sortable: true, field: 'indexNo', title: 'erp编号', width: '90', visible: false },
	{ sortable: true, field: 'accountingUnit', title: '核算单位', width: '90' },
	{ sortable: true, field: 'processCreateUserName', title: '发起人姓名', width: '90' },
	{ sortable: true, field: 'deptName', title: '发起人部门', width: '90' },
	{ sortable: true, field: 'amount', title: '金额(元)', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'useDepartment', title: '使用部门', width: '90', visible: false },
	{ sortable: true, field: 'usePlatform', title: '使用平台', width: '90', visible: false },
	{ sortable: true, field: 'applyReason', title: '申请事由', width: '169' },
	{ sortable: true, field: 'warehouseClass', title: '仓库分类', width: '90', visible: false },
	{ sortable: true, field: 'accountName', title: '姓名/账户名', width: '150' },
	{ sortable: true, field: 'status', title: '状态', width: '85' },
	{ sortable: true, field: 'qty', title: '数量', width: '85' },
	{ sortable: true, field: 'unitPrice', title: '单价', width: '85' },
	{ sortable: true, field: 'rawAmount', title: '原始金额', width: '90', visible: false, formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'payTime', title: '支付日期', width: '135', visible: false },
	{ sortable: true, field: 'sumType', title: '费用大类', width: '90', visible: false },
	{ sortable: true, field: 'firstTitle', title: '一级科目', width: '90', visible: false },
	{ sortable: true, field: 'secondTitle', title: '二级科目', width: '90', visible: false },
	{ sortable: true, field: 'feeType', title: '费用类型', width: '90', visible: false },
	{ sortable: true, field: 'feeArea', title: '费用区域', width: '90', visible: false },
	{ sortable: true, field: 'approverStatus', title: '审核状态', width: '90', visible: false },
	{ sortable: true, field: 'approverUserName', title: '审批人', width: '90', visible: false },
	{ sortable: true, field: 'approverTime', title: '审批日期', width: '135', visible: false },
	{ sortable: true, field: 'failReason', title: '原因', width: '90', visible: false },
	{ sortable: true, field: 'agreeUserName', title: '生成人', width: '90' },
	{ sortable: true, field: 'agreeTime', title: '生成日期', width: '135' },
	{ sortable: true, field: 'createdTime', title: '创建日期', width: '135' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		field:'20250608093910',
		width: '300',
		fixed: 'right',
		btnList: [
			{ title: '确认无误', handle: (row: any) => onGenerate(row, 2), isDisabled: (row) => row.verify || row.status == '已生成正式' || row.status == '凭证已确认' },
			{ title: '生成正式凭证', handle: (row: any) => onGenerate(row, 1), isDisabled: (row) => row.verify || row.status == '已生成正式' },
			{ title: '审核科目', handle: (row: any) => onGenerate(row, 3), isDisabled: (row) => !row.verify },
			{ title: '编辑凭证', handle: onEditProof, isDisabled: (row) => row.verify || row.status == '已生成正式' || row.status == '凭证已确认' },
			{ title: '编辑科目', handle: onEditAccount, isDisabled: (row) => !row.verify },
		],
	},
]);

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		if (columnToggle.value == '凭证') {
			item.verify = false;
		} else {
			item.verify = true;
		}
	});
	tableData.value = data.data.list;
	callback(data);
};

onMounted(async () => {
	query.value.startTime = dayjs().format('YYYY-MM-DD');
	query.value.endTime = dayjs().format('YYYY-MM-DD');
	await init();
});
</script>

<style scoped lang="scss">
.listStyle {
	display: flex;
	gap: 5px;
	width: 100%;
	margin-top: 10px;
	z-index: 999;

	.listStyle_item {
		width: 9%;
	}
}

.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

.expand-wrapper {
	padding: 16px;
}

.vxetoolbar20241219 {
	position: absolute;
	top: 0px;
	left: 1650px;
	width: auto;
	padding: 0;
	z-index: 2000 !important;
	background-color: rgb(255 255 255 / 0%);
}

.vxetoolbar20241219 ::v-deep .vxe-custom--wrapper {
	margin-left: 0px !important;
}

.tabButtonCss.el-button--small {
	padding: 5px 0 !important;
}

.vxe-pager {
	margin: 0;
	padding: 0;
}

::v-deep .vxe-table--body-wrapper .body--wrapper {
	height: auto !important;
	min-height: 10px !important;
}

::v-deep .vxetableCss ::-webkit-scrollbar {
	width: 10px !important;
	height: 12px !important;
}

.toolbar-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding-right: 20px;
}

.expanded-container {
	margin: 3px 0;
	display: flex;
	gap: 5px;
	flex-wrap: wrap;
	padding: 0 155px;
}

.label-item {
	display: flex;
	align-items: center;
	padding: 5px;
	gap: 3px;
	font-size: 15px;
	font-weight: bold;
}
</style>
