/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

/**
 * HTTP请求方法枚举<br />&nbsp;HTTP \"GET\" method. Get = 0<br />&nbsp;HTTP \"POST\" method. Post = 1<br />&nbsp; HTTP \"PUT\" method. Put = 2<br />&nbsp;HTTP \"DELETE\" method. Delete = 3<br />&nbsp;HTTP \"PATCH\" method.  Patch = 4<br />&nbsp;HTTP \"HEAD\" method. Head = 5<br />&nbsp;HTTP \"OPTIONS\" method. Options = 6<br />&nbsp; HTTP \"TRACE\" method. Trace = 7<br />&nbsp;HTTP \"CONNECT\" method. Connect = 8<br />
 * @export
 * @enum {string}
 */
export enum HttpMethodEnum {
    NUMBER_0 = 0,
    NUMBER_1 = 1,
    NUMBER_2 = 2,
    NUMBER_3 = 3,
    NUMBER_4 = 4,
    NUMBER_5 = 5,
    NUMBER_6 = 6,
    NUMBER_7 = 7,
    NUMBER_8 = 8
}

