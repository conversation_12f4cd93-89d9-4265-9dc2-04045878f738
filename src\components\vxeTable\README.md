#### 配置式表格基本用法,详细用法看组件源码

#### 引入组件 import vxeGridTable from '/@/components/vxeGridTable/index.vue'

<vxeGridTable :columns="columns" :queryFunc="pageApply" :isNeedDisposeProps="true" @disposeProps="getList">
<template #toolbar_buttons>
<el-button type="primary" @click="handleClick">Primary</el-button>
</template>
</vxeGridTable>

# 传入查询接口至queryFunc 表格列columns 如果前端需要处理数据isNeedDisposeProps至为true,并给组件传入disposeProps

# disposeProps该方法是一个回调函数

# data中包含了查询接口的data中所有数据(list,total,summary),目前只支持修改list

const getList = async (data: any, callback: Function) => {
data.data.list.forEach((item: any) => {
item.hd = '回调1111'
item.url = 'https://vxeui.com/resource/img/fj577.jpg,https://vxeui.com/resource/img/fj843.jpg'
item.click = 'click'
})
callback(data.data.list)
}

# 如若遇见调用接口想回调查询

# <el-button @click="getList" type="primary">查询</el-button>

# 需要在table上写一个ref="table",下面再定义 const table = ref()

const getList = () => {
table.value.query.currentPage = 1;
table.value.getList();
};

# 函数接收两个参数,第一个参数是查询接口返回的list数据,第二个参数是一个回调函数,将处理好的数据callback回去就可以

# 如若需要展示表尾合计行传入showsummary,后端返回数据对应columns每个数据的field_sum(小写sum,目前没匹配大小写)即可

# #toolbar_buttons这个插槽是表格左上角的按钮,按钮写在这个插槽里
