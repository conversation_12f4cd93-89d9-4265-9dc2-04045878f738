/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StatusEnum } from './status-enum';
 /**
 * 
 *
 * @export
 * @interface AddSysLdapInput
 */
export interface AddSysLdapInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddSysLdapInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddSysLdapInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddSysLdapInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    tenantId?: number | null;

    /**
     * 主机
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    host: string;

    /**
     * 端口
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    port?: number;

    /**
     * 用户搜索基准
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    baseDn: string;

    /**
     * 绑定DN(有管理权限制的用户)
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    bindDn: string;

    /**
     * 绑定密码(有管理权限制的用户密码)
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    bindPass: string;

    /**
     * 用户过滤规则
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    authFilter: string;

    /**
     * Ldap版本
     *
     * @type {number}
     * @memberof AddSysLdapInput
     */
    version?: number;

    /**
     * 绑定域账号字段属性值
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    bindAttrAccount: string;

    /**
     * 绑定用户EmployeeId属性值
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    bindAttrEmployeeId: string;

    /**
     * 绑定Code属性值
     *
     * @type {string}
     * @memberof AddSysLdapInput
     */
    bindAttrCode: string;

    /**
     * @type {StatusEnum}
     * @memberof AddSysLdapInput
     */
    status?: StatusEnum;
}
