<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.shopType" class="publicCss" placeholder="店铺类别" clearable maxlength="50" />
				<el-input v-model.trim="query.shopName" class="publicCss" placeholder="店铺名称" clearable maxlength="50" />
				<div class="publicCss">
					<manyInput v-model:inputt="query.stationCode" :title="'网点编码'" :placeholder="'网点编码'" :maxRows="300" :maxlength="3000" />
				</div>
				<el-input v-model.trim="query.stationName" class="publicCss" placeholder="网点名称" clearable maxlength="50" />
				<faceSheetSettings v-model:value="query.cityNameList" title="区域" multiple class="publicCss" />
				<faceSheetSettings v-model:value="query.accountBankList" title="行别" multiple class="publicCss" />
				<el-select v-model="query.platformList" placeholder="平台" class="publicCss" clearable multiple collapse-tags>
					<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="sheetManagement20240915093955"
				order-by="modifiedTime"
				isNeedCheckBox
				@select="checkboxChange"
				:isAsc="false"
				:tableCols="tableCols"
				:query="query"
				:queryApi="GetElectronicWaybillMngPageList"
			>
				<template #toolbar_buttons>
					<el-button @click="onAddMethod" type="primary">新增面单</el-button>
					<el-button @click="onBatchSetColor" type="primary">批量设置导出背景色</el-button>
					<el-button @click="exportProps" type="primary" :disabled="isExport">导出</el-button>
					<el-button @click="importProps" type="primary">导入</el-button>
					<el-button @click="importRecords" type="primary">导入记录</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="dialogVisible" :title="singleform.formTitle" width="700" draggable overflow>
		<div style="height: 280px" v-if="dialogVisible">
			<el-form :model="singleform" :rules="singlerules">
				<div style="display: flex; justify-content: space-between" v-loading="singleform.loading">
					<div>
						<el-form-item label="网点编码" :label-width="'90px'" prop="stationCode">
							<el-input v-model.trim="singleform.stationCode" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="网点名称" :label-width="'90px'" prop="stationName">
							<el-input v-model.trim="singleform.stationName" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="平台" :label-width="'90px'" prop="platform">
							<el-select v-model.trim="singleform.platform" placeholder="请选择平台" filterable clearable @change="handlePlatformChange">
								<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="店铺名称" :label-width="'90px'" prop="shopCode">
							<el-select v-model.trim="singleform.shopCode" placeholder="请选择店铺" filterable clearable remote :remote-method="handleRemoteShopSearch">
								<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="区域" :label-width="'90px'" prop="cityName">
							<!-- <el-select v-model.trim="singleform.cityName" placeholder="请选择区域" filterable clearable>
								<el-option v-for="item in regionlist" :key="item.value" :label="item.label" :value="item.value" />
							</el-select> -->
							<faceSheetSettings v-model:value="singleform.cityName" title="区域" style="width: 200px" />
						</el-form-item>
						<el-form-item label="快递公司" :label-width="'90px'" prop="expressCom">
							<!-- <el-select v-model.trim="singleform.expressCom" placeholder="请选择快递公司" filterable clearable style="width: 200px">
								<el-option v-for="item in expressCompany" :key="item.value" :label="item.label" :value="item.value" />
							</el-select> -->
							<faceSheetSettings v-model:value="singleform.expressCom" title="快递公司" style="width: 200px" />
						</el-form-item>
						<el-form-item label="导出背景色" :label-width="'90px'">
							<el-color-picker v-if="dialogVisible" v-model="singleform.hexColor"></el-color-picker>
						</el-form-item>
					</div>
					<div>
						<el-form-item label="店铺类别" :label-width="'90px'" prop="shopType">
							<el-input v-model.trim="singleform.shopType" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="收款账户" :label-width="'90px'" prop="accountName">
							<el-input v-model.trim="singleform.accountName" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="开户行" :label-width="'90px'" prop="accountBankName">
							<el-input v-model.trim="singleform.accountBankName" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="行别" :label-width="'90px'" prop="accountBank">
							<!-- <el-select v-model.trim="singleform.accountBank" placeholder="请选择" filterable clearable>
								<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select> -->
							<faceSheetSettings v-model:value="singleform.accountBank" title="行别" style="width: 200px" />
						</el-form-item>
						<el-form-item label="收款账号" :label-width="'90px'" prop="accountNo">
							<el-input v-model.trim="singleform.accountNo" placeholder="请输入" autocomplete="off" clearable style="width: 200px" maxlength="20" />
						</el-form-item>
						<el-form-item label="单价" :label-width="'90px'" prop="price">
							<el-input-number v-model.trim="singleform.price" :min="0" :max="100" :controls="false" :precision="2" placeholder="请输入" style="width: 200px" />
						</el-form-item>
					</div>
				</div>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog v-model="batchColorVisible" title="批量设置导出颜色" width="300" draggable overflow>
		<div style="height: 150px; padding-top: 25%">
			<el-form :model="batchform" :rules="batchrules">
				<el-form-item label="导出背景色" :label-width="'90px'" prop="hexColor">
					<el-color-picker v-if="batchColorVisible" v-model="batchform.hexColor"></el-color-picker>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="batchColorVisible = false">取消</el-button>
				<el-button type="primary" @click="onbatchColorSave"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog title="导入" v-model="importVisible" width="35%" draggable overflow v-loading="importLoading" style="margin-top: -38vh !important">
		<div style="display: flex; justify-content: space-between">
			<el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1" :on-remove="removeFile" :file-list="fileList" accept=".xlsx,.xls,.csv" :http-request="uploadFile">
				<el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
					<el-button size="small" type="primary">点击上传</el-button>
				</el-tooltip>
			</el-upload>
			<el-button @click="downLoadImportFile">下载导入模版</el-button>
		</div>
		<div class="btnGroup">
			<el-button @click="importVisible = false">取消</el-button>
			<el-button type="primary" @click="submit" v-reclick>确定</el-button>
		</div>
	</el-dialog>

	<el-dialog title="导入记录" v-model="importRecordsVisible" width="50%" draggable overflow v-loading="importLoading">
		<importRecordsCom v-if="importRecordsVisible" style="height: 600px" />
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, reactive, onMounted, computed } from 'vue';
import { ElMessageBox, ElMessage, FormRules } from 'element-plus';
import dayjs from 'dayjs';
import { bankList, platformlist, expressCompany } from '/@/utils/tools';
import {
	GetElectronicWaybillMngPageList,
	ImportElectronicWaybillMngList,
	SaveElectronicWaybillMng,
	GetElectronicWaybillShop,
	DelOrEnabledElectronicWaybillMngById,
	BulkSetElectronicWaybillMngColor,
	ExportElectronicWaybillMng,
	ImportElectronicWaybillMng,
} from '/@/api/cwManager/electronicWaybill';

const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const faceSheetSettings = defineAsyncComponent(() => import('/@/components/yhCom/faceSheetSettings.vue'));
const importRecordsCom = defineAsyncComponent(() => import('./importRecords.vue'));
const options = ref<Public.options[]>([]);
const shopNamelist = ref<Public.options[]>([]);
const checkboxList = ref<any[]>([]);
const batchColorVisible = ref(false);
const importRecordsVisible = ref(false);
const importLoading = ref(false);
const dialogVisible = ref(false);
const importVisible = ref(false);
const fileList = ref([]); //上传文件列表
const file = ref(); //上传文件
const isExport = ref(false);
const table = ref();
const query = ref({
	shopType: '',
	shopName: '',
	stationCode: '',
	stationName: '',
	cityNameList: [],
	platformList: [],
	accountBankList: [],
});

interface SingleForm {
	cityName: string;
	expressCom: string;
	shopType: string;
	accountName: string;
	accountBankName: string;
	accountBank: string;
	accountNo: string;
	price: string | undefined;
	platform: string;
	shopCode: string;
	stationCode: string | undefined;
	stationName: string;
	id: number;
	hexColor: string;
}

const batchform = ref({
	hexColor: '',
});

const batchrules = reactive<FormRules<SingleForm>>({
	hexColor: [{ required: true, message: '请选择导出背景色', trigger: 'change' }],
});

const singlerules = reactive<FormRules<SingleForm>>({
	cityName: [{ required: true, message: '请选择区域', trigger: 'change' }],
	expressCom: [{ required: true, message: '请输入快递公司', trigger: 'blur' }],
	shopType: [{ required: true, message: '请输入店铺类别', trigger: 'blur' }],
	accountName: [{ required: true, message: '请输入收款账户', trigger: 'blur' }],
	accountBankName: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
	accountBank: [{ required: true, message: '请选择行别', trigger: 'change' }],
	accountNo: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
	price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
	platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
	shopCode: [{ required: true, message: '请选择店铺', trigger: 'change' }],
	stationCode: [{ required: true, message: '请输入网点编码', trigger: 'blur' }],
	stationName: [{ required: true, message: '请输入网点名称', trigger: 'blur' }],
	hexColor: [{ required: true, message: '请选择导出背景色', trigger: 'change' }],
});

const singleform = ref({
	accountName: '',
	platform: '',
	stationCode: undefined,
	stationName: '',
	shopCode: '',
	shopType: '',
	cityName: '',
	accountBankName: '',
	expressCom: '',
	accountBank: '',
	accountNo: '',
	price: undefined,
	id: 0,
	formTitle: '',
	loading: false,
	hexColor: '',
});
const regionlist = ref<Public.options[]>([
	{ label: '南昌', value: '南昌' },
	{ label: '义乌', value: '义乌' },
	{ label: '广东', value: '广东' },
	{ label: '上海', value: '上海' },
	{ label: '湖北', value: '湖北' },
	{ label: '湖南', value: '湖南' },
	{ label: '安徽', value: '安徽' },
	{ label: '苏州', value: '苏州' },
]);

const filteredPlatformList = computed(() => {
	return platformlist.filter((item) => item.label !== '未知');
});

onMounted(() => {});

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const onAddMethod = () => {
	onCleardataMethod();
	singleform.value.formTitle = '新增面单';
	singleform.value.id = 0;
	dialogVisible.value = true;
};
// 平台选择改变
const handlePlatformChange = async (platform: any) => {
	singleform.value.platform = platform;
	singleform.value.shopCode = '';
	await fetchShopList('');
};

// 远程搜索店铺
const handleRemoteShopSearch = async (shopName: any) => {
	await fetchShopList(shopName);
};

// 获取店铺
const fetchShopList = async (shopName = '') => {
	const params = {
		platform: singleform.value.platform,
		shopCode: '',
		shopName: shopName,
	};
	const { data } = await GetElectronicWaybillShop(params);
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopCode,
	}));
};

const onSingleSave = async () => {
	if (
		!singleform.value.cityName ||
		!singleform.value.expressCom ||
		!singleform.value.shopType ||
		!singleform.value.accountName ||
		!singleform.value.accountBankName ||
		!singleform.value.accountBank ||
		!singleform.value.accountNo ||
		singleform.value.platform === null ||
		singleform.value.platform === undefined ||
		!singleform.value.shopCode ||
		!singleform.value.price ||
		!singleform.value.stationCode ||
		!singleform.value.stationName
	) {
		ElMessage.error('请填写必填项');
		return;
	}
	singleform.value.loading = true;
	let hexColorHash = singleform.value.hexColor;
	let hexColorWithoutHash;
	if (hexColorHash) {
		if (!hexColorHash.startsWith('#') || hexColorHash.length !== 7) {
			ElMessage.error('请输入正确的颜色编码');
			return;
		}
		hexColorWithoutHash = hexColorHash.substring(1);
		if (hexColorWithoutHash.length !== 6) {
			ElMessage.error('请输入正确的颜色编码');
			return;
		}
	}
	try {
		const { success } = await SaveElectronicWaybillMng({ ...singleform.value, hexColor: hexColorWithoutHash });
		singleform.value.loading = false;
		if (success) {
			ElMessage.success(singleform.value.id ? '编辑成功' : '新增成功');
			getList();
			dialogVisible.value = false;
		}
	} catch (error) {
		singleform.value.loading = false;
	}
};

const importRecords = async () => {
	importRecordsVisible.value = true;
};
const downLoadImportFile = async () => {
	window.open('/excel/cwManage/面单导入模版.xlsx', '_self');
};
const importProps = () => {
	fileList.value = [];
	file.value = null;
	importVisible.value = true;
};
const uploadFile = async (data: any) => {
	file.value = data.file;
};
const removeFile = (file: any, fileList: any) => {
	file.value = null;
};
const submit = async () => {
	let form = new FormData();
	form.append('upfile', file.value);
	if (!file.value) return window.$message.error('请上传文件');
	window.$message.info('正在导入中,请稍后...');
	importLoading.value = true;
	try {
		const { success } = await ImportElectronicWaybillMng(form);
		if (success) {
			window.$message.success('导入成功');
			importVisible.value = false;
			getList();
		}
	} catch (error) {
		window.$message.error('导入失败');
	} finally {
		importLoading.value = false;
	}
};
const exportProps = async () => {
	isExport.value = true;
	await ExportElectronicWaybillMng({ ...query.value })
		.then((data: any) => {
			if (!data.success) {
				return;
			}
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
			isExport.value = false;
		})
		.catch(() => {
			isExport.value = false;
		});
};

const handleEdit = (row: any) => {
	onCleardataMethod();
	singleform.value = JSON.parse(JSON.stringify(row));
	singleform.value.formTitle = '编辑面单';
	if (row.shopCode && row.shopName) {
		shopNamelist.value = [{ label: row.shopName, value: row.shopCode }];
	}
	singleform.value.hexColor = row.hexColor ? (row.hexColor.startsWith('#') ? row.hexColor : `#${row.hexColor}`) : '';
	dialogVisible.value = true;
};

const handleForbidden = async (row: any) => {
	let title = row.enabled == 1 ? '启用' : '禁用';
	ElMessageBox.confirm('此操作将' + title + '此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const params = {
				id: row.id,
				enabled: row.enabled == 1 ? 0 : 1,
			};
			const { success } = await DelOrEnabledElectronicWaybillMngById(params);
			if (success) {
				ElMessage.success(title + '成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '已取消',
			});
		});
};

const onbatchColorSave = async () => {
	if (!batchform.value.hexColor) {
		ElMessage.error('请选择导出背景色');
		return;
	}
	let hexColorHash = batchform.value.hexColor;
	if (!hexColorHash.startsWith('#') || hexColorHash.length !== 7) {
		ElMessage.error('请输入正确的编码');
		return;
	}
	let hexColorWithoutHash = hexColorHash.substring(1);
	if (hexColorWithoutHash.length !== 6) {
		ElMessage.error('请输入正确的编码');
		return;
	}
	let ids = checkboxList.value.map((item: any) => item.id);
	const { success } = await BulkSetElectronicWaybillMngColor({ ids, hexColor: hexColorWithoutHash });
	if (success) {
		ElMessage.success('设置成功');
		checkboxList.value = [];
		batchform.value.hexColor = '';
		batchColorVisible.value = false;
		getList();
	}
};

const onBatchSetColor = () => {
	if (checkboxList.value.length == 0) {
		ElMessage.error('请选择所需设置的行数据');
		return;
	}
	batchform.value.hexColor = '';
	batchform.value.hexColor = checkboxList.value[0].hexColor ? (batchform.value.hexColor = `#${checkboxList.value[0].hexColor}`) : '';
	batchColorVisible.value = true;
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const onCleardataMethod = () => {
	singleform.value.stationCode = undefined;
	singleform.value.stationName = '';
	singleform.value.shopCode = '';
	singleform.value.shopType = '';
	singleform.value.cityName = '';
	singleform.value.accountName = '';
	singleform.value.accountBankName = '';
	singleform.value.expressCom = '';
	singleform.value.accountBank = '';
	singleform.value.accountNo = '';
	singleform.value.platform = '';
	singleform.value.hexColor = '';
	singleform.value.price = undefined;
};

const bgRow = (row: any) => {
	let cdccc = row.hexColor ? row.hexColor : 'fff';
	return `<div style="background-color: #${cdccc};width:30px;height:20px"></div>`;
};
const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'shopType', title: '店铺类别', width: '120' },
	{ sortable: false, field: 'beijing', title: '背景', width: '50', type: 'html', formatter: bgRow },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '120' },
	{ sortable: true, field: 'stationCode', title: '网点编码', width: '90' },
	{ sortable: true, field: 'stationName', title: '网点名称', width: '140' },
	{ sortable: true, field: 'price', title: '单价', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'wareType', title: '仓库分类', width: '120' },
	{ sortable: true, field: 'accountName', title: '收款账户', width: '120' },
	{ sortable: true, field: 'accountNo', title: '收款账号', width: '120' },
	{ sortable: true, field: 'accountBankName', title: '开户行', width: '120' },
	{ sortable: true, field: 'accountBank', title: '行别', width: '120' },
	{ sortable: true, field: 'modifiedUserName', title: '操作人', width: '120' },
	{ sortable: true, field: 'cityName', title: '区域', width: '70' },
	{ sortable: true, field: 'platform', title: '平台', formatter: 'formatPlatform', width: '70' },
	{ sortable: true, field: 'expressCom', title: '快递公司', width: '120' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '150',
		fixed: 'right',
		field:'**************',
		btnList: [
			{ title: '编辑', handle: handleEdit },
			{ title: '启用', handle: handleForbidden, isDisabled: (row) => row.enabled == 0 },
			{ title: '禁用', handle: handleForbidden, isDisabled: (row) => row.enabled == 1 },
		],
	},
]);
</script>

<style scoped lang="scss">
.button-container {
	margin-left: auto;
	display: flex;
}
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

.btnGroup {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
