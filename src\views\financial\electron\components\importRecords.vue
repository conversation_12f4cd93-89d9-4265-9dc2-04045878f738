<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dateRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-input v-model.trim="query.createdUserName" class="publicCss" style="width: 120px" placeholder="操作人" clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="sheetManagement20250205111944" :isAsc="false" :tableCols="tableCols" :query="query" :queryApi="GetElectronicWaybillImportLogPage"> </vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { GetElectronicWaybillImportLogPage } from '/@/api/cwManager/electronicWaybill';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dateRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const table = ref();
const query = ref({
	startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
	endDate: dayjs().endOf('month').format('YYYY-MM-DD'),
	createdUserName: null,
});
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'createdUserName', title: '操作人' },
	{ sortable: true, field: 'createdTime', title: '操作时间' },
]);

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
</script>

<style scoped lang="scss"></style>
