<template>
    <div>
        <el-select v-model="account" :placeholder="props.placeholder" :size="props.size" :style="cststyle" filterable
            :clearable="props.clearable" :multiple="props.multiple" :collapseTags="props.multiple" @change="changeBank">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
    </div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, } from 'vue';
import { QueryOnlineBankSet, } from '/@/api/cwManager/cashierSet';
const query = ref({
    currentPage: 1,
    pageSize: 100000,
})
const props = defineProps({
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '请选择银行卡',
    },
    cststyle: {
        type: String,
        default: 'width: 100%;',
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    collapseTags: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: 'small',
    },
    filterFn: {
        type: [Function, Boolean],
        default: false,
    },
    labelOption: {
        type: Array,
        default: () => ['accountName']
    },
    valueOption: {
        type: String,
        default: 'account'
    }
});

const emit = defineEmits(['bankChange']);
const options = ref<Public.options[]>([])
const account = defineModel('value');
const accountName = defineModel('label');
const id = defineModel('id');

const getBankList = async () => {
    let { data, success } = await QueryOnlineBankSet(query.value);
    if (!success) options.value = [];
    if (typeof props.filterFn === 'function') {
        data.list = props.filterFn(data.list);
    }
    options.value = data.list.map((item: any) => {
        return {
            label: props.labelOption.map((label: any) => item[label]).join('-'),
            value: item[props.valueOption],
            id: item.id,
        }
    });
}

const changeBank = (value: any) => {
    let res;
    if (!props.multiple) {
        const res = options.value.find((item: any) => item.value === value);
        id.value = value ? res?.id : '';
        accountName.value = value ? res?.label : '';
    } else {
        res = options.value.filter((item: any) => value.includes(item.value));
        id.value = value ? res.map((item: any) => item.id) : '';
        accountName.value = value ? res.map((item: any) => item.label) : '';
    }
    emit('bankChange', res);
};

onMounted(async () => {
    await getBankList();
});

</script>

<style scoped lang="scss"></style>
