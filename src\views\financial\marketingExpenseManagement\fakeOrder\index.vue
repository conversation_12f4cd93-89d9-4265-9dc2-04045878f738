<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-date-picker v-model="query.dateTime" type="date" placeholder="出生日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w100" />
				<el-input v-model.trim="query.processNo" placeholder="流程号" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.presenter" placeholder="提交人" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.groupId" placeholder="运营组" class="publicCss" clearable filterable>
					<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content v-loading="loading">
			<vxetable ref="table" id="fakeOrder202508030828" :tableCols="tableCols" :query="query" :query-api="GetSummaryVariousPlatformsList" showsummary />
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetSummaryVariousPlatformsList, ExportSummaryVariousPlatforms, EditSummaryVariousPlatforms } from '/@/api/financial/newOperation';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
import { GetDirectorGroupList } from '/@/api/operatemanage/shop';
const faceSheetSettings = defineAsyncComponent(() => import('/@/components/yhCom/faceSheetSettings.vue'));
const groupList = ref<any[]>([]);
const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const query = ref({
	dateTime: time, //日期
	processNo: '', //流程号
	presenter: '', //提交人
	groupId: '', //运营组
});

const statusList = ref<Public.options[]>([
	{ label: '未发起', value: 0 },
	{ label: '待发起', value: 1 },
	{ label: '已发起', value: 2 },
	{ label: '已拒绝', value: 5 },
	{ label: '已撤销', value: 6 },
]);
const reviewList = ref<Public.options[]>([
	{ label: '待审核', value: 1 },
	{ label: '已审核', value: 2 },
	{ label: '已拒绝', value: 3 },
]);
const table = ref();
const loading = ref(false);
const checkBoxList = ref<any[]>([]);

const exportProps = async () => {
	loading.value = true;
	await ExportSummaryVariousPlatforms({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			window.$message[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功,稍后请到下载管理查看' : '导出失败'));
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
};

const handleReview = (row: any, val: Number) => {
	ElMessageBox.prompt(`是否审核${val == 2 ? '通过' : '拒绝'}？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		inputPlaceholder: '可输入审核备注（最多200字）',
		inputValidator: (value) => {
			if (value && value.length > 200) return '输入内容不能超过200字符';
			return true;
		},
		inputErrorMessage: '输入内容不符合要求',
		type: 'warning',
	})
		.then(async ({ value }) => {
			// 确保截取前200字符
			const trimmedValue = value ? value.substring(0, 200) : '';
			const { success } = await EditSummaryVariousPlatforms({
				checkMarketCostIds: [row.id],
				reviewRemark: trimmedValue,
				reviewStatus: val,
			});
			if (success) {
				ElMessage({ type: 'success', message: `审核${val == 2 ? '通过' : '拒绝'}成功` });
				getList();
			}
		})
		.catch(() => {});
};

const handleEdit = (row: any) => {
	console.log(row);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ width: '100', sortable: true, field: 'pullDate', title: '日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'region', title: '提交人', align: 'center' },
	{ width: '230', sortable: true, field: 'businessId', title: '流程号', align: 'center' },
	{ width: '90', sortable: true, field: 'shopName', title: '小组', align: 'center' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'payPrice', title: '申请金额', align: 'right', formatter: 'fmtAmt2' },

	{ width: '132', sortable: true, field: 'payTime', title: '支付时间', align: 'center' },
	{ width: '220', sortable: true, field: 'shopName', title: '店铺名称', align: 'center' },
	{ width: '120', sortable: true, field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '120', sortable: true, field: 'shopId', title: '店铺ID', align: 'center' },
	{ width: '120', sortable: true, field: 'payPrice', title: '支付金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '120', sortable: true, field: 'tranRemark', title: '交易摘要', align: 'center' },
	{ width: '120', sortable: true, field: 'initiateUserName', title: '发起人', align: 'center' },
	{ width: '132', sortable: true, field: 'initiateTime', title: '发起时间', align: 'center' },
	{ width: '90', sortable: true, field: 'reviewStatus', title: '审核状态', align: 'center', formatter: (row: any) => reviewList.value.find((item) => item.value === row.reviewStatus)?.label },
	{ width: '120', sortable: true, field: 'reviewUserName', title: '审核人', align: 'center' },
	{ width: '132', sortable: true, field: 'reviewTime', title: '审核时间', align: 'center' },
	{ width: '200', sortable: false, field: 'reviewRemark', title: '初审备注', align: 'center' },
	{
		title: '操作',
		align: 'center',
		width: '110',
		type: 'btnList',
		minWidth: '110',
		field: 'operationAcc',
		btnList: [{ title: '编辑', handle: (row) => handleEdit(row) }],
		fixed: 'right',
	},
]);

onMounted(async () => {
	let { data, success } = await GetDirectorGroupList();
	if (data && success) {
		groupList.value = data.map((item: any) => {
			return { value: item.key, label: item.value };
		});
	}
});
</script>

<style scoped lang="scss"></style>
