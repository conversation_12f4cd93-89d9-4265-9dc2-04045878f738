import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/BankFlow/`;

export const GetAllOnlineBank = () => request.get(apiPrefix + 'GetAllOnlineBank');

export const ImportBankFlow = (params: any, config = {}) => request.post(apiPrefix + 'ImportBankFlow', params, config);

//通用导入
export const ImportBankFlowCommon = (params: any, config = {}) => request.post(apiPrefix + 'ImportBankFlowCommon', params, config);

//查询银行流水 QueryBankFlow
export const QueryBankFlow = (params: any) => request.post(apiPrefix + 'QueryBankFlow', params);

//忽略银行流水 IgnoreBankFlow
export const IgnoreBankFlow = (params: any) => request.post(apiPrefix + 'IgnoreBankFlow', params);

//对账冲正BankFlowMatch
export const BankFlowMatch = (params: any) => request.post(apiPrefix + 'BankFlowMatch', params);

//编辑银行流水 UpdateBankFlow
export const UpdateBankFlow = (params: any) => request.post(apiPrefix + 'UpdateBankFlow', params);

//查询对账冲正 QueryBankFlowMatch
export const QueryBankFlowMatch = (params: any) => request.post(apiPrefix + 'QueryBankFlowMatch', params);

//编辑匹配费用 QueryFeeDataMatch
export const QueryFeeDataMatch = (params: any) => request.post(apiPrefix + 'QueryFeeDataMatch', params);

//解除流水匹配 RelieveBankFlow
export const RelieveBankFlow = (params: any) => request.post(apiPrefix + 'RelieveBankFlow', params);

//批量忽略  BulkIgnoreBankFlow
export const BulkIgnoreBankFlow = (params: any) => request.post(apiPrefix + 'BulkIgnoreBankFlow', params);

//解除忽略  UnIgnoreBankFlow
export const UnIgnoreBankFlow = (params: any) => request.post(apiPrefix + 'UnIgnoreBankFlow', params);

//批量修改费用类型 BulkUpDateFeeType
export const BulkUpDateFeeType = (params: any) => request.post(apiPrefix + 'BulkUpDateFeeType', params);


export const QueryBusDataMatch = (params: any) => request.post(apiPrefix + 'QueryBusDataMatch', params);

//导出
export const ExportBankFlow = (params: any) => request.post(apiPrefix + 'ExportBankFlow', params);


export const UpdateBankFlowToReturnAmount = (params: any) => request.post(apiPrefix + 'UpdateBankFlowToReturnAmount', params);

//流水数据审批 ApproveBankFlow
export const ApproveBankFlow = (params: any) => request.post(apiPrefix + 'ApproveBankFlow', params);