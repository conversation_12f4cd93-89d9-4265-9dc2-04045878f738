import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/BankFlowMonth/`;

// 计算匹配费用
export const ComputeFee = (params: any) => request.post(apiPrefix + 'ComputeFee', params);

// 月账单流水列表
export const GetBankFlowMonthRecordList = (params: any) => request.post(apiPrefix + 'GetBankFlowMonthRecordList', params);

// 导出月账单流水列表
export const ExportBankFlowMonthRecord = (params: any) => request.post(apiPrefix + 'ExportBankFlowMonthRecord', params);

// 导入月账单流水
export const ImportBankFlowMonth = (params: any) => request.post(apiPrefix + 'ImportBankFlowMonth', params);

// 月账单报表列表
export const GetBankFlowMonthReportList = (params: any) => request.post(apiPrefix + 'GetBankFlowMonthReportList', params);

// 导出月账单流水报表
export const ExportBankFlowMonthReport = (params: any) => request.post(apiPrefix + 'ExportBankFlowMonthReport', params);
