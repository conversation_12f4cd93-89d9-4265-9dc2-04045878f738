<template>
	<div style="width: 100%; height: 100%">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:startDate="query.processCreateTimeStart"
						v-model:endDate="query.processCreateTimeEnd" startPlaceholder="发起时间" endPlaceholder="发起时间"
						class="publicCss" style="width: 200px" />
					<dataRange v-model:startDate="query.payTimeStart" v-model:endDate="query.payTimeEnd"
						startPlaceholder="支付时间" endPlaceholder="支付时间" class="publicCss" style="width: 200px" />
					<dataRange v-model:startDate="query.operateTimeStart" v-model:endDate="query.operateTimeEnd"
						startPlaceholder="操作时间" endPlaceholder="操作时间" class="publicCss" style="width: 200px" />
					<el-select v-model="query.payStatus" placeholder="支付状态" class="publicCss" clearable
						style="width: 90px">
						<el-option v-for="item in PayStatus" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable style="width: 90px">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.isOrderAmountSame" placeholder="匹配状态" class="publicCss" clearable
						style="width: 90px">
						<el-option v-for="item in isOrderAmountSameOption" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-input v-model.trim="query.title" class="publicCss" placeholder="请输入流程名称" clearable
						maxlength="50" />
					<el-input v-model.trim="query.businessId" class="publicCss" placeholder="请输入审批编码" clearable
						maxlength="50" />
					<el-input v-model.trim="query.indexNo" class="publicCss" placeholder="请输入费用单号" clearable
						maxlength="50" />
					<el-input v-model.trim="query.orderNo" class="publicCss" placeholder="请输入订单编号" clearable
						maxlength="50" />
					<el-input v-model.trim="query.operateUserName" class="publicCss" placeholder="操作人" clearable
						style="width: 90px" maxlength="50" />
					<el-input v-model.trim="query.payType" class="publicCss" placeholder="付款方式" clearable
						style="width: 100px" maxlength="50" />
					<el-select v-model="query.ofPublicPayType" placeholder="对公/对私" class="publicCss" clearable
						style="width: 90px">
						<el-option label="对公" value="对公" />
						<el-option label="对私" value="对私" />
					</el-select>
					<el-select v-model="query.payAccount" placeholder="采购账号" class="publicCss" clearable>
						<el-option v-for="item in payAccount" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.isAdapt" placeholder="流程是否适配" class="publicCss" clearable>
						<el-option label="已适配" :value="true" />
						<el-option label="未适配" :value="false" />
					</el-select>
					<el-input v-model.trim="query.skBankType" class="publicCss" placeholder="请输入开户行" clearable
						maxlength="50" />
					<el-input v-model.trim="query.applyFeeType" class="publicCss" placeholder="请输入费用分类" clearable
						maxlength="50" />
					<div style="height: 35px; margin: 7px 10px 0 0">
						<el-cascader v-model="query.feeTypeList" :options="optionsExpense" :props="propsExpense"
							placeholder="请选择收支类型" class="publicCss custom-cascader" style="width: 190px" collapse-tags
							collapse-tags-tooltip clearable />
					</div>
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-button @click="onExportTableAll" type="primary">导出</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" :pageSize="30" id="**************" :pageSizes="[30, 50, 100, 200, 300]"
					:tableCols="tableCols" @select="select" showsummary isIndexFixed isCheckBoxFixed :query="query"
					isNeedCheckBox :query-api="GetWaitPayPurchaseOrderList">
					<template #payStatus="{ row }">
						<el-icon v-if="row.payStatus == '已付款'" color="green">
							<SuccessFilled />
						</el-icon>
						{{ row.payStatus }}
					</template>
					<template #status="{ row }">
						<el-icon v-if="row.status == '审批通过'" color="green">
							<SuccessFilled />
						</el-icon>
						<el-icon v-if="row.status == '拒绝'" color="red">
							<CircleCloseFilled />
						</el-icon>
						{{ row.status }}
					</template>
					<template #isOrderAmountSame="{ row }">
						<el-icon v-if="row.isOrderAmountSame == 1" color="green">
							<SuccessFilled />
						</el-icon>
						<el-icon v-if="row.isOrderAmountSame == 0" color="red">
							<CircleCloseFilled />
						</el-icon>
						{{ row.isOrderAmountSame == 1 ? '正常' : row.isOrderAmountSame == 0 ? '异常' : '' }}
					</template>
					<template #toolbar_buttons>
						<el-button @click="pullProps" type="primary">拉取数据</el-button>
						<el-button @click="ApprovedOrRefuse('agree')" type="primary">审批通过</el-button>
						<el-button @click="ApprovedOrRefuse('refuse')" type="primary">驳回</el-button>
						<el-button @click="consolidatedPayments" type="primary">合并付款</el-button>
						<el-button @click="ApprovedOrRefuse('edit')" type="primary">修改付款账号</el-button>
						<el-button @click="flushedStatus" type="primary">刷新1688匹配状态</el-button>
						<span class="shop-text" :title="shopWithDraw" :style="{ color: verifyCount ? 'red' : 'black' }">{{ shopWithDraw }}</span>
					</template>
				</vxetable>
			</template>
		</Container>

		<el-dialog v-model="dialogVisible" append-to-body title="合并付款-明细" width="60%" draggable
			:close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<div v-if="dialogVisible">
				<orderPay :instanceIds="instanceIds" @close="close" @getList="getList" />
			</div>
		</el-dialog>

		<el-dialog v-model="refuseVisible" append-to-body title="驳回理由" width="25%" draggable
			:close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<el-button @click="remark = button.text" v-for="button in buttons" :key="button.text" :type="button.type"
				text bg>
				{{ button.text }}
			</el-button>
			<div style="display: flex; justify-content: end; margin-top: 10px">
				<el-input v-model="remark" style="width: 100%" :rows="2" type="textarea" placeholder="驳回理由" />
			</div>
			<div class="btnGruop">
				<el-button @click="refuseVisible = false" type="primary">取消</el-button>
				<el-button @click="handleRefuse" type="primary" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="chooseBankVisible" append-to-body title="选择银行" width="570px" draggable
			:close-on-press-escape="false" :close-on-click-modal="false" :show-close="false">
			<el-form ref="formRef" style="max-width: 1000px" :model="bankForm" label-width="auto" class="demo-ruleForm">
				<el-form-item label="银行:" prop="onlineBankId">
					<el-select v-model="bankForm.onlineBankId" filterable remote reserve-keyword placeholder="卡号"
						:remote-method="remoteMethod" :loading="remoteLoading" style="width: 550px">
						<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="图片" prop="age" v-if="selectList.length == 1 && approveType == 'agree'">
					<uploadMf v-model:imagesStr="bankForm.images" :upstyle="{ height: 40, width: 40 }" :limit="9"
						ref="refuploadMf" />
				</el-form-item>
			</el-form>
			<div class="btnGruop">
				<el-button @click="chooseBankVisible = false">取消</el-button>
				<el-button @click="handleAgree" type="primary" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-drawer v-model="ProcessApprovalsDrawer" title="查看流程">
			<approvalProcess :viewInfo="viewInfo" v-if="ProcessApprovalsDrawer" @close="close" @getList="getList" />
		</el-drawer>

	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { QueryCashierFeeType, QueryOnlineBankSetSelect, QueryCashierFeeTypeSum, QueryCashierFeeTypeCasCade } from '/@/api/cwManager/cashierSet';
import {
	GetWaitPayPurchaseOrderList,
	PassWaitPayPurchaseOrdersProcess,
	GetWaitPayPurchaseOrdersByCashier,
	ExportWaitPayPurchaseOrderList,
	ChangePayInfoWaitPayPurchaseOrdersProcess,
	RepairAliOrderAmount,
	GetPayExceptCount,
	RepairDingInfo
} from '/@/api/cwManager/processPayOrApproved';
import { GetAllCgAccount } from '/@/api/cwManager/cashierSet';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const orderPay = defineAsyncComponent(() => import('/@/views/financial/purchaseOrdersPay/components/orderPay.vue'));
const approvalProcess = defineAsyncComponent(() => import('/@/components/yhCom/approvalProcess.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
const query = ref({
	status: '',
	title: '',
	isAdapt: true,
	processCreateTimeStart: '',
	processCreateTimeEnd: '',
	payTimeStart: '',
	payTimeEnd: '',
	businessId: '',
	indexNo: '',
	orderNo: '',
	operateUserName: '',
	payAccount: '',
	isOrderAmountSame: '',
	payStatus: '',
	payType: '',
	skBankType: '',
	applyFeeType: '',
	feeTypeList: [],
	ofPublicPayType: '',
	operateTimeStart: '',
	operateTimeEnd: '',
	type: '',
	sumType: '',
	name: '',
});
const instanceIds = ref<any>([]);
const selectList = ref<any>([]);
const disburseList = ref<Public.options[]>([]);
const expenseCollect = ref<Public.options[]>([]);
const expenseTypeList = ref<Public.options[]>([]);
const dialogVisible = ref(false);
const refuseVisible = ref(false);
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const remark = ref('');
const onlineBankId = ref();
const remoteLoading = ref(false);
const ProcessApprovalsDrawer = ref(false);
const paymentVisible = ref(false);
const table = ref();
const chooseBankVisible = ref(false);
const bankList = ref<Public.options[]>([]);
const propsExpense = ref({
	multiple: true,
	emitPath: false,
});
const optionsExpense = ref([]);
const approveType = ref<string | null>('');
const bankForm = ref<{
	onlineBankId: string | number;
	images: string[];
}>({
	onlineBankId: '',
	images: [],
});
const imgUrl = ref<String[]>([]);
const instanceId = ref('');
const shopWithDraw = ref('');
const verifyCount = ref(false);
const viewInfo = ref({
	instanceId: '',
	accountName: '',
	status: '',
});
const buttons = ref([
	{
		text: '金额不一致',
		type: 'primary',
	},
	{
		text: '重复订单',
		type: 'primary',
	},
	{
		text: '交易关闭',
		type: 'primary',
	},
	{
		text: '账号填写错误',
		type: 'primary',
	},
]);
const options = ref<Public.options[]>([
	{ value: '待审核', label: '待审核' },
	{ value: '审批操作中', label: '审批操作中' },
	{ value: '审批通过', label: '审批通过' },
	{ value: '拒绝', label: '拒绝' },
	{ value: '作废', label: '作废' },
]);
const isOrderAmountSameOption = ref<Public.options[]>([
	{ value: 1, label: '正常' },
	{ value: 0, label: '异常' },
]);
const PayStatus = ref<Public.options[]>([
	{ value: '待支付', label: '待支付' },
	{ value: '已付款待确认', label: '已付款待确认' },
	{ value: '已付款', label: '已付款' },
]);
const payAccount = ref<Public.options[]>([]);

const select = (val: any) => {
	selectList.value = val;
};

const onExportTableAll = async () => {
	const res: any = await ExportWaitPayPurchaseOrderList({ ...query.value, ...table.value.query });
	if (res) {
		const aLink = document.createElement('a');
		let blob = new Blob([res], { type: 'application/vnd.ms-excel' });
		aLink.href = URL.createObjectURL(blob);
		aLink.setAttribute('download', '出纳付款中台.xlsx');
		aLink.click();
	} else {
		ElMessage.error('导出没数据');
	}
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
	identification();
};

const identification = async () => {
	const { success, data } = await GetPayExceptCount({});
	if (success && data) {
		const { startDate, endDate, dfCount, fkzCount, shzCount } = data;
		let startDates = dayjs(startDate).format('YYYY-MM-DD');
		let endDates = dayjs(endDate).format('YYYY-MM-DD');
		if(dfCount === 0 && fkzCount === 0 && shzCount === 0){
			verifyCount.value = false;
		} else {
			verifyCount.value = true;
		}
		shopWithDraw.value = `${startDates} - ${endDates}，其中待付款流程 ${dfCount} 条，付款中流程 ${fkzCount} 条，审核中流程 ${shzCount} 条，请关注！`;
	}
};

const close = () => {
	dialogVisible.value = false;
	refuseVisible.value = false;
	ProcessApprovalsDrawer.value = false;
};

//打开弹窗
const consolidatedPayments = () => {
	if (selectList.value.length == 0) return window.$message.error('请选择数据');
	if (selectList.value.length > 30) return window.$message.error('最多只能选择30条数据');
	const flag = selectList.value.every((item: any) => item.payAccount == selectList.value[0].payAccount && item.payStatus == '待支付' && item.payType == '阿里巴巴' && item.isOrderAmountSame == 1);
	if (flag) {
		ElMessageBox.confirm('确认选择这些数据吗?')
			.then(() => {
				instanceIds.value = selectList.value.map((item: any) => item.instanceId);
				dialogVisible.value = true;
			})
			.catch(() => {
				window.$message.info('已取消');
			});
	} else {
		window.$message.error('请选择相同的支付账号相同、支付方式为阿里巴巴、状态为待支付、匹配状态为正常的数据');
	}
};

type ActionType = 'agree' | 'refuse' | 'edit';
const ApprovedOrRefuse = (type: ActionType) => {
	approveType.value = type;
	const map: Record<ActionType, string> = {
		agree: '审核',
		refuse: '驳回',
		edit: '修改',
	};
	if (selectList.value.length == 0) return window.$message.error('请选择数据');
	if (type == 'edit') {
		if (selectList.value.length != 1) return window.$message.error('只能选择一条数据');
		if (selectList.value[0].status != '审批通过') return window.$message.error('只能选择审批通过的数据');
	} else {
		const flag = selectList.value.every((item: any) => item.status == '待审核');
		if (!flag) return window.$message.error('只能选择待审核的数据');
	}
	ElMessageBox.confirm(`确认${map[type]}这些数据吗?`)
		.then(async () => {
			if (type == 'agree' || type == 'edit') {
				bankForm.value.onlineBankId = '';
				refuploadMf.value?.clearMethod();
				if (type == 'agree') {
					const flag = selectList.value.every((item: any) => item.payAccount == selectList.value[0].payAccount);
					if (flag) {
						await remoteMethod('');
						const obj = bankList.value.filter((item: any) => item.name == selectList.value[0].payAccount)[0];
						bankForm.value.onlineBankId = obj ? obj.value : '';
					}
				}
				chooseBankVisible.value = true;
			} else if (type == 'refuse') {
				remark.value = '';
				refuseVisible.value = true;
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const pullProps = async () => {
	const { success, data } = await GetWaitPayPurchaseOrdersByCashier();
	if (success) {
		window.$message.success(data ? data : '拉取成功');
		getList();
	}
};
const flushedStatus = async () => {
	if (selectList.value.length == 0) return window.$message.error('请选择数据');
	if (selectList.value.length > 10) return window.$message.error('最多只能选择10条数据');
	const flag = selectList.value.every((item: any) => item.payType == '阿里巴巴' && item.isOrderAmountSame == 0 && item.orderNo);
	if (!flag) return window.$message.error('请选择付款方式为阿里巴巴、匹配状态为异常、订单编号不为空的数据');
	const { success, data } = await RepairAliOrderAmount({ instanceIds: selectList.value.map((item: any) => item.instanceId) });
	if (success) {
		window.$message.success(data ? data : '刷新成功');
		selectList.value = [];
		getList();
	}
};

const handleRefuse = async () => {
	if (!remark.value) return window.$message.error('请输入驳回理由');
	const { success } = await PassWaitPayPurchaseOrdersProcess({ instanceIds: selectList.value.map((item: any) => item.instanceId), type: 'refuse', remark: remark.value });
	if (success) {
		window.$message.success('驳回成功');
		refuseVisible.value = false;
		getList();
	}
};

const remoteMethod = async (account: string) => {
	// if (!account) return;
	remoteLoading.value = true;
	const { data, success } = await QueryOnlineBankSetSelect({ account, accountName: selectList.value[0].payAccount });
	if (success) {
		bankList.value = data.map((item: any) => {
			return {
				label: item.accountName + '-' + item.bankType + '-' + item.account,
				value: item.id,
				name: item.busAccountName,
			};
		});
		remoteLoading.value = false;
	} else {
		bankList.value = [];
		remoteLoading.value = false;
	}
};

const handleAgree = async () => {
	if (!bankForm.value.onlineBankId) return window.$message.error('请选择银行');
	if (approveType.value == 'agree') {
		const { success } = await PassWaitPayPurchaseOrdersProcess({
			instanceIds: selectList.value.map((item: any) => item.instanceId),
			type: 'agree',
			onlineBankId: bankForm.value.onlineBankId,
			images: bankForm.value.images,
		});
		if (success) {
			window.$message.success('审核成功');
			chooseBankVisible.value = false;
			getList();
		}
	} else if (approveType.value == 'edit') {
		const { success } = await ChangePayInfoWaitPayPurchaseOrdersProcess({
			instanceIds: [selectList.value[0].instanceId],
			onlineBankId: bankForm.value.onlineBankId,
			// images: bankForm.value.images,
		});
		if (success) {
			window.$message.success('修改成功');
			chooseBankVisible.value = false;
			getList();
		}
	}
};

const getAllAccount = async () => {
	const { data, success } = await GetAllCgAccount();
	if (success) {
		payAccount.value = data?.map((item: any) => {
			return {
				label: item,
				value: item,
			};
		});
	}

	const { data: data2, success: success2 } = await QueryCashierFeeTypeSum({ currentPage: 1, pageSize: ******** });
	if (!success2) return;
	disburseList.value = data2 || [];

	const { data: data3, success: success3 } = await QueryCashierFeeTypeCasCade({});
	if (!success3) return;
	optionsExpense.value = data3 || [];
};

//查看流程
const viewProcess = (row: any) => {
	viewInfo.value = {
		instanceId: row.instanceId,
		accountName: row.payAccount,
		status: row.status,
	};
	ProcessApprovalsDrawer.value = true;
};

const synchronousDD = (row: any) => {
	ElMessageBox.confirm('此操作将同步钉钉,是否继续?')
		.then(async () => {
			const { success } = await RepairDingInfo({ instanceId: row.instanceId });
			if (success) {
				window.$message.success('同步成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const tableCols = ref<VxeTable.Columns[]>([
	{
		title: '状态',
		align: 'center',
		field:'**************',
		children: [
			{ sortable: true, field: 'payStatus', title: '支付', width: '80', align: 'right' },
			{ sortable: true, field: 'status', title: '审核', width: '80', align: 'right' },
			{ sortable: true, field: 'isOrderAmountSame', title: '1688匹配', width: '95', align: 'right' },
		],
	},
	{ sortable: true, field: 'title', title: '流程名称', width: '200' },
	{ sortable: true, field: 'feeType', title: '收支类型', width: '90' },
	{ sortable: true, field: 'businessId', title: '审批编码', width: '160' },
	{ sortable: true, field: 'processCreateTime', title: '发起时间', formatter: 'formatTime', width: '140' },
	{ sortable: true, field: 'applyReason', title: '申请事由', width: '90' },
	{ sortable: true, field: 'applyFeeType', title: '费用分类', width: '90' },
	{ sortable: true, field: 'indexNo', title: '费用单号', width: '90' },
	{ sortable: true, field: 'amount', title: '金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'payType', title: '付款方式', width: '90' },
	{
		sortable: true,
		field: 'orderNo',
		title: '订单编码',
		type: 'html',
		width: '160',
		formatter: (row: any) => {
			if (row.orderNo != null && row.orderNo.length > 10 && row.payType === '阿里巴巴') {
				return `<a href="https://trade.1688.com/order/new_step_order_detail.htm?orderId=${row.orderNo}" target="_blank" style="color: #1000ff;">${row.orderNo}</a>`;
			} else {
				return row.orderNo;
			}
		},
	},
	{ sortable: true, field: 'ofPublicPayType', title: '对公/对私', width: '100' },
	{ sortable: true, field: 'payAccount', title: '采购账号', width: '90' },
	{ sortable: true, field: 'skBankUserName', title: '收款账户名', width: '100' },
	{ sortable: true, field: 'skBankAccount', title: '账号', width: '90' },
	{ sortable: true, field: 'skBankType', title: '开户行', width: '100' },
	{ sortable: true, field: 'processRemark', title: '备注', width: '90' },
	{ field: 'picListJson', title: '图片', width: '90', type: 'image' },
	{ sortable: true, field: 'payBankAccountName', title: '付款账号信息', width: '120' },
	{ sortable: true, field: 'operateUserId', width: '80', title: '操作人', formatter: (row: any) => row.operateUserName },
	{ sortable: true, field: 'operateTime', title: '操作时间', formatter: 'formatTime', width: '140' },
	{ sortable: true, field: 'payTime', title: '付款时间', formatter: 'formatTime', width: '140' },
	{ sortable: true, field: 'remark', title: '操作备注', width: '100' },
	{
		title: '查看流程', width: '100', fixed: 'right',field:'**************', type: 'btnList', align: 'center', btnList: [
			{ title: '查看', handle: viewProcess },
			{
				title: '同步钉钉', handle: synchronousDD, isDisabled(row) {
					return row.status == '待审核' && row.isAdapt != 1 ? false : true;
				},
			}
		]
	},
]);
onMounted(() => {
	query.value.processCreateTimeStart = dayjs().startOf('day').format('YYYY-MM-DD');
	query.value.processCreateTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD');
	getAllAccount();
	identification();
});
</script>

<style scoped lang="scss">
.btnGruop {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.shop-text {
	font-size: 12px;
	margin-left: 5px;
	max-width: 520px;
	min-width: 100px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
}
</style>
