<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-select v-model="query.companys" class="publicCss" placeholder="公司名" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in corporationList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-input v-model.trim="query.invoiceHeader" class="publicCss" placeholder="发票抬头" clearable maxlength="50" />
				<el-input v-model.trim="query.taxID" class="publicCss" placeholder="税号" clearable maxlength="50" />
				<el-input v-model.trim="query.openingBank" class="publicCss" placeholder="开户行" clearable maxlength="50" />
				<el-input v-model.trim="query.accountNumber" class="publicCss" placeholder="账号" clearable maxlength="50" />
				<el-select v-model="query.region" class="publicCss" placeholder="税务地" clearable filterable>
					<el-option label="江西" value="江西" />
					<el-option label="浙江" value="浙江" />
					<el-option label="深圳" value="深圳" />
					<el-option label="武汉" value="武汉" />
				</el-select>
				<el-input v-model.trim="query.city" class="publicCss" placeholder="公司属地" clearable maxlength="50" />
				<el-select v-model="query.taxCredential" class="publicCss" placeholder="纳税资质" clearable filterable>
					<el-option v-for="item in ratepayingList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-input v-model.trim="query.corporation" class="publicCss" placeholder="法人" clearable maxlength="50" />
				<el-input v-model.trim="query.corporationMobile" class="publicCss" placeholder="法人电话" clearable maxlength="20" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="companyInvoicingInformation202503261527"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				:isAsc="false"
				:queryApi="GetCompanyInvoicingInfoPage"
				v-loading="pageLoading"
			>
				<template #toolbar_buttons>
					<el-button type="primary" @click="addCompanyInvoicingInfo">新增</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="dialogVisible" :title="dialogTitle" width="25%" draggable overflow style="margin-top: -18vh !important" :close-on-click-modal="false">
		<el-form ref="formRef" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm" status-icon>
			<el-form-item label="公司名" prop="company">
				<el-input v-model.trim="ruleForm.company" placeholder="请输入公司名" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="发票抬头" prop="invoiceHeader">
				<el-input v-model.trim="ruleForm.invoiceHeader" placeholder="请输入发票抬头" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="税号" prop="taxID">
				<el-input v-model.trim="ruleForm.taxID" placeholder="请输入税号" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="开户行" prop="openingBank">
				<el-input v-model.trim="ruleForm.openingBank" placeholder="请输入开户行" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="账号" prop="accountNumber">
				<el-input v-model.trim="ruleForm.accountNumber" placeholder="请输入账号" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="税务地" prop="region">
				<el-input v-model.trim="ruleForm.region" placeholder="请输入税务地" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="公司属地" prop="city">
				<el-input v-model.trim="ruleForm.city" placeholder="请输入公司属地" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label=" 纳税资质" prop="taxCredential">
				<el-select v-model="ruleForm.taxCredential" class="form_Css" placeholder="请选择纳税资质" clearable filterable>
					<el-option v-for="item in ratepayingList" :key="item" :label="item" :value="item" />
				</el-select>
			</el-form-item>
			<el-form-item label="法人">
				<el-input v-model.trim="ruleForm.corporation" placeholder="请输入法人" class="form_Css" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="法人电话">
				<el-input v-model.trim="ruleForm.corporationMobile" placeholder="请输入法人电话" class="form_Css" clearable maxlength="20" />
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button type="primary" @click="onSaveMethod">保存</el-button>
		</template>
	</el-dialog>

	<el-dialog v-model="detailDialog" width="30%" title="店铺信息" append-to-body draggable overflow style="margin-top: -15vh !important">
		<div>
			<div class="pb5">
				<el-select
					v-model="detailInfo.shopName"
					style="width: 150px; margin-right: 10px"
					placeholder="店铺"
					clearable
					filterable
					remote
					reserve-keyword
					:remote-method="remoteMethod"
					@change="changeShop"
				>
					<el-option v-for="item in shopOptionsList" :key="item.shopCode" :label="item.shopName" :value="item.shopName" />
				</el-select>
				<el-input v-model.trim="detailInfo.shopCode" placeholder="请输入店铺编码" style="width: 150px; margin-right: 10px" clearable maxlength="20" />
				<el-button type="primary" @click="onSearchShop">查询</el-button>
				<el-button type="primary" @click="addShop">添加</el-button>
			</div>
			<el-table :data="shopDataList" style="width: 100%" border height="400">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="shopName" label="店铺名称" width="auto" align="center" show-overflow-tooltip />
				<el-table-column prop="shopCode" label="店铺编码" width="130" align="center" show-overflow-tooltip />
				<el-table-column label="操作" width="70">
					<template #default="scope">
						<el-button size="small" type="danger" @click="deleteShop(scope.$index, scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="detailDialog = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import { GetCompanyInvoicingInfoPage, SaveCompanyInvoicingInfo, GetAfterSalesInvoiceManageCustomerInfo, GetShopList, SaveCompanyInvoicingShopInfo } from '/@/api/cwManager/afterSalesInvoiceManage';
import { debounce } from 'lodash-es';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const table = ref();
const formRef = ref();
const corporationList = ref([]);
const detailDialog = ref(false);
const shopDataList = ref<{ shopCode: string; shopName: string }[]>([]);
const shopDataListBackup = ref<{ shopCode: string; shopName: string }[]>([]);
const shopOptionsList = ref<any[]>([]);
const ratepayingList = ref<any[]>(['小规模纳税人', '一般纳税人13%税率', '个体工商户']);
const detailInfo = ref({
	shopCode: '',
	shopName: '',
	id: '',
});
const query = ref({
	companys: [],
	invoiceHeader: '',
	taxID: '',
	openingBank: '',
	accountNumber: '',
	region: '',
	city: '',
	taxCredential: '',
	corporation: '',
	corporationMobile: '',
});

const ruleForm = ref({
	company: '',
	invoiceHeader: '',
	taxID: '',
	openingBank: '',
	accountNumber: '',
	region: '',
	city: '',
	taxCredential: '',
	corporation: '',
	corporationMobile: '',
});

const rules = ref({
	company: [{ required: true, message: '请输入公司名', trigger: 'blur' }],
	invoiceHeader: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
	taxID: [{ required: true, message: '请输入税号', trigger: 'blur' }],
	openingBank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
	accountNumber: [{ required: true, message: '请输入账号', trigger: 'blur' }],
	region: [{ required: true, message: '请输入税务地', trigger: 'blur' }],
	city: [{ required: true, message: '请输入公司属地', trigger: 'blur' }],
	taxCredential: [{ required: true, message: '请输入 纳税资质', trigger: 'blur' }],
});

const submit = async () => {
	const { success } = await SaveCompanyInvoicingShopInfo({ shopList: shopDataList.value, id: detailInfo.value.id });
	if (success) {
		detailDialog.value = false;
		getList();
	}
};

const remoteMethod = async (account: string) => {
	const { data, success } = await GetShopList({ shopName: account });
	if (success) {
		shopOptionsList.value = data;
	} else {
		shopOptionsList.value = [];
	}
};

const assignMethod = () => {
	shopDataListBackup.value = shopDataList.value;
};

const changeShop = (value: string) => {
	detailInfo.value.shopCode = shopOptionsList.value.find((item: any) => item.shopName === value)?.shopCode || '';
};

const onSearchShop = () => {
	if (detailInfo.value.shopCode) {
		const filteredData = shopDataListBackup.value.filter((item: any) => item.shopCode.includes(detailInfo.value.shopCode));
		shopDataList.value = filteredData;
	} else {
		shopDataList.value = shopDataListBackup.value;
	}
};

const addShop = () => {
	if (!detailInfo.value.shopCode || !detailInfo.value.shopName) {
		ElMessage.error('请选择店铺');
		return;
	}
	const isExist = shopDataListBackup.value.some((item: any) => item.shopCode === detailInfo.value.shopCode);
	if (isExist) {
		ElMessage.error('店铺已存在');
		return;
	}
	shopDataListBackup.value.push({
		shopCode: detailInfo.value.shopCode,
		shopName: detailInfo.value.shopName,
	});
	shopDataList.value = [...shopDataListBackup.value];

	detailInfo.value.shopCode = '';
	detailInfo.value.shopName = '';
};

const deleteShop = (index: number, row: any) => {
	// 从备份数据中删除
	const backupIndex = shopDataListBackup.value.findIndex((item) => item.shopCode === row.shopCode);
	if (backupIndex !== -1) {
		shopDataListBackup.value.splice(backupIndex, 1);
	}

	// 更新显示数据
	if (detailInfo.value.shopCode) {
		const filteredData = shopDataListBackup.value.filter((item: any) => item.shopCode.includes(detailInfo.value.shopCode));
		shopDataList.value = filteredData;
	} else {
		shopDataList.value = [...shopDataListBackup.value];
	}
};

const openOrderDetail = (row: any) => {
	detailDialog.value = true;
	shopDataList.value = JSON.parse(JSON.stringify(row.shopList));
	assignMethod();
	detailInfo.value.id = row.id;
	detailInfo.value.shopCode = '';
	detailInfo.value.shopName = '';
};

const onEditProof = (row: any) => {
	dialogVisible.value = true;
	dialogTitle.value = '编辑公司开票信息';
	nextTick(() => {
		formRef.value.resetFields();
		formRef.value.clearValidate();
		ruleForm.value = { ...row };
	});
};

const addCompanyInvoicingInfo = () => {
	dialogVisible.value = true;
	dialogTitle.value = '新增公司开票信息';
	nextTick(() => {
		formRef.value.resetFields();
		formRef.value.clearValidate();
		ruleForm.value = {
			company: '',
			invoiceHeader: '',
			taxID: '',
			openingBank: '',
			accountNumber: '',
			region: '',
			city: '',
			taxCredential: '',
			corporation: '',
			corporationMobile: '',
		};
	});
};

const onSaveMethod = () => {
	if (ruleForm.value.corporationMobile) {
		if (!/^\d*$/.test(ruleForm.value.corporationMobile)) {
			ElMessage.error('法人电话必须为数字');
			return;
		}
	}
	formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const { success } = await SaveCompanyInvoicingInfo(ruleForm.value);
			if (success) {
				dialogVisible.value = false;
				getList();
				init();
				ElMessage.success('保存成功');
			}
		}
	});
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'company', title: '公司名', width: 'auto' },
	{ sortable: true, field: 'invoiceHeader', title: '发票抬头', width: 'auto' },
	{ sortable: true, field: 'taxID', title: '税号', width: 'auto' },
	{ sortable: true, field: 'openingBank', title: '开户行', width: 'auto' },
	{ sortable: true, field: 'accountNumber', title: '账号', width: 'auto' },
	{ field: 'shopList', title: '店铺信息', width: '75', type: 'btnList', btnList: [{ title: '查看', handle: (row: any) => openOrderDetail(row) }] },
	{ sortable: true, field: 'region', title: '税务地', width: 'auto' },
	{ sortable: true, field: 'city', title: '公司属地', width: 'auto' },
	{ sortable: true, field: 'taxCredential', title: '纳税资质', width: 'auto' },
	{ sortable: true, field: 'corporation', title: '法人', width: 'auto' },
	{ sortable: true, field: 'corporationMobile', title: '法人电话', width: 'auto' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '90',
		field:'**************',
		fixed: 'right',
		btnList: [{ title: '编辑', handle: onEditProof }],
	},
]);

const init = async () => {
	const { success, data } = await GetCompanyInvoicingInfoPage({ currentPage: 1, pageSize: ******** });
	if (success) {
		corporationList.value = Array.from(new Set(data.list.map((item: any) => item.company)));
	}
};

onMounted(async () => {
	await init();
	const { success: success2, data: data2 } = await GetShopList({});
	if (success2) {
		shopOptionsList.value = data2;
	}
});
</script>

<style scoped lang="scss">
.form_Css {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

::v-deep .el-select__tags-text {
	max-width: 35px;
}
</style>
