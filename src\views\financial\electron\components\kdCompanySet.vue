<template>
	<Container>
		<template #content>
			<vxetable ref="table" :pageSize="50" id="20240120151104" :query="query" :tableCols="tableCols" :query-api="GetElectronicWaybillSetList">
				<template #toolbar_buttons>
					<el-button @click="addSetting" type="primary">新增</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="Visible" :title="isEdit ? '编辑' : '新增'" width="400" draggable overflow>
		<div style="display: flex; justify-content: center">
			<el-input v-model.trim="addInfo.val" placeholder="请输入快递公司" clearable maxlength="20" />
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="Visible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { GetElectronicWaybillSetList, SaveElectronicWaybillSet, DelElectronicWaybillSet } from '/@/api/cwManager/electronicWaybill';
const table = ref();
const isEdit = ref(false);
const query = ref({
	title: '快递公司',
});
const addInfo = ref({
	title: '快递公司',
	val: null,
	id: null,
});
const Visible = ref(false);
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));

const addSetting = () => {
	addInfo.value = {
		title: '快递公司',
		val: null,
		id: null,
	};
	isEdit.value = false;
	Visible.value = true;
};
const getList = () => {
	table.value.currentPage = 1;
	table.value.getList();
};
const handleSubmit = async () => {
	if (addInfo.value.val === null) return window.$message.error('请输入快递公司');
	const { success } = await SaveElectronicWaybillSet(addInfo.value);
	if (!success) return;
	window.$message.success('操作成功');
	Visible.value = false;
	getList();
};
const onEdit = (row: any) => {
	addInfo.value = {
		title: '快递公司',
		val: row.val,
		id: row.id,
	};
	isEdit.value = true;
	Visible.value = true;
};
const onDelete = (row: any) => {
	ElMessageBox.confirm('此操作将删除该数据,是否继续?', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await DelElectronicWaybillSet({ id: row.id });
			if (!success) return;
			getList();
			window.$message.success('删除成功');
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '已取消删除!',
			});
		});
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'val', title: '快递公司' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '90',
		btnList: [
			{ title: '编辑', handle: onEdit },
			{ title: '删除', handle: onDelete },
		],
		fixed: 'right',
		field:'20250608092545'
	},
]);
</script>

<style scoped lang="scss"></style>
