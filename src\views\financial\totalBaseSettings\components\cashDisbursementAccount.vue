<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.sumTypeList" placeholder="费用大类" class="publicCss" clearable filterable multiple collapse-tags style="width: 180px;">
					<el-option v-for="item in sumTypeList1" :key="item" :label="item" :value="item" />
				</el-select>
				<el-input v-model.trim="query.title" placeholder="科目名称" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.feeType" placeholder="费用类型" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.feeAreaList" placeholder="费用区域" class="publicCss" clearable filterable multiple collapse-tags style="width: 180px;">
					<el-option v-for="item in feeAreaList1" :key="item" :label="item" :value="item" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				keyField="id"
				:pageSize="50"
				id="cashDisbursementAccount202412151139"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				:query="query"
				:isAsc="false"
				:isNeedPager="false"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:treeConfig="{ transform: true, rowField: 'id', parentField: 'pId' }"
				:query-api="QueryCashOutlayChart"
			>
				<template #toolbar_buttons>
					<el-button @click="onAddMethod({})" type="primary">新增</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" :title="dynamicHeading ? '编辑' : '新增'" width="400" draggable overflow style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
		<div style="padding-top: 10px" v-loading="listLoading">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="费用大类" :label-width="'100px'" prop="sumType">
					<el-select v-model="singleform.sumType" placeholder="费用大类" class="btnGroup" clearable filterable v-if="singleform.pId">
						<el-option v-for="item in sumTypeList1" :key="item" :label="item" :value="item" />
					</el-select>
					<el-input v-model.trim="singleform.sumType" placeholder="费用大类" class="btnGroup" clearable maxlength="50" v-else />
				</el-form-item>
				<el-form-item label="科目名称" :label-width="'100px'" prop="title">
					<el-input v-model.trim="singleform.title" placeholder="科目名称" class="btnGroup" clearable maxlength="50" />
				</el-form-item>
				<el-form-item label="费用类型" :label-width="'100px'" prop="feeType">
					<el-input v-model.trim="singleform.feeType" placeholder="费用类型" class="btnGroup" clearable maxlength="50" />
				</el-form-item>
				<el-form-item label="费用区域" :label-width="'100px'" prop="feeArea">
					<el-select v-model="singleform.feeArea" placeholder="费用区域" class="btnGroup" clearable filterable>
						<el-option v-for="item in feeAreaList1" :key="item" :label="item" :value="item" />
					</el-select>
				</el-form-item>
				<el-form-item label="备注" :label-width="'100px'" style="white-space: pre-wrap; word-break: break-all">
					<el-input
						v-model="singleform.remark"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						style="width: 700px"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryCashOutlayChart, ExportCashOutlayChart, InsertCashOutlayChart, UpdateCashOutlayChart, DeleteCashOutlayChart } from '/@/api/cwManager/accountsChart';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const dynamicHeading = ref(false);
const listLoading = ref(false);
const sumTypeList1 = ref(['采购款', '独立核算', '快递费', '其他应收款', '人工设置', '日常费用', '营销费用']);
const feeAreaList1 = ref(['独立核算', '公司费用', '人工设置', '深圳费用']);
const ruleFormRef = ref<FormInstance>();
const table = ref();
const singleform = ref<{
	id: string | undefined;
	pId: string | undefined;
	sumType: string;
	title: string;
	feeType: string;
	feeArea: string;
	remark: string;
}>({
	id: undefined,
	pId: undefined,
	sumType: '',
	title: '',
	feeType: '',
	feeArea: '',
	remark: '',
});

const singlerules = {
	sumType: [{ required: true, message: '请输入费用大类', trigger: 'blur' }],
	title: [{ required: true, message: '请输入科目名称', trigger: 'blur' }],
	feeType: [{ required: true, message: '请输入费用类型', trigger: 'blur' }],
	feeArea: [{ required: true, message: '请输入费用区域', trigger: 'blur' }],
	remark: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const query = ref({
	sumTypeList: [],
	title: '',
	feeType: '',
	feeAreaList: [],
});

const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			listLoading.value = true;
			if (dynamicHeading.value) {
				const { success } = await UpdateCashOutlayChart({ ...singleform.value });
				if (success) {
					ElMessage.success('编辑成功');
					editVisible.value = false;
					getList();
				}
			} else {
				const { success } = await InsertCashOutlayChart({ ...singleform.value });
				if (success) {
					ElMessage.success('新增成功');
					editVisible.value = false;
					getList();
				}
			}
			listLoading.value = false;
		}
	});
}, 1000); // 防抖时间1秒

const exportProps = async () => {
	await ExportCashOutlayChart({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onDelete = (row: any) => {
	ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const params = { id: row.id };
			const { success } = await DeleteCashOutlayChart(params);
			if (success) {
				ElMessage.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({ type: 'info', message: '已取消' });
		});
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	dynamicHeading.value = true;
	editVisible.value = true;
};

const onAddMethod = (row: any) => {
	if (row.children && row.children.length > 0) {
		singleform.value.pId = row.id;
	}
	singleform.value = {
		id: undefined,
		pId: row && row.id ? row.id : undefined, //父级科目编码
		sumType: '',
		title: '',
		feeType: '',
		feeArea: '',
		remark: '',
	};
	console.log(singleform.value, 'singleform.value');
	dynamicHeading.value = false;
	editVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const isDisabled = (row: any, val: number): boolean => {
	if (val === 1) {
		return row.children && row.children.length > 0;
	}
	return false;
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'sumType', title: '费用大类', width: '200', treeNode: true },
	{ sortable: true, field: 'title', title: '科目名称', width: '180' },
	{ sortable: true, field: 'feeType', title: '费用类型', width: '110' },
	{ sortable: true, field: 'feeArea', title: '费用区域', width: '110' },
	{ sortable: true, field: 'remark', title: '备注' },
	{ sortable: true, field: 'createdUserName', title: '创建人', width: '100' },
	{ sortable: true, field: 'createdTime', title: '创建时间', width: '140' },
	{ sortable: true, field: 'modifiedUserName', title: '修改人', width: '100' },
	{ sortable: true, field: 'modifiedTime', title: '修改时间', width: '140' },
	// { sortable: true, field: 'batchNumber', title: '批次号', width: '150' },
	{
		title: '操作',
		align: 'center',
		field:'20250608093823',
		type: 'btnList',
		width: '110',
		btnList: [
			{ title: '新增', handle: onAddMethod },
			{ title: '编辑', handle: onEdit },
			{ title: '删除', handle: onDelete, isDisabled: (row: any) => isDisabled(row, 1) },
		],
	},
]);

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		sumTypeList1.value.push(item.sumType);
	});
	sumTypeList1.value = Array.from(new Set(sumTypeList1.value));
	callback(data);
};

const getAllDept = async () => {};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
