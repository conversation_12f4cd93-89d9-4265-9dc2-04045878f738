<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-select v-model="query.enable" placeholder="状态" class="publicCss" clearable>
					<el-option label="禁用" :value="false" />
					<el-option label="启用" :value="true" />
				</el-select>
				<el-button @click="getList" type="primary">查询</el-button>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20240915093955" :tableCols="tableCols" :query="query" :queryApi="QueryCashierUserSet">
				<template #toolbar_buttons>
					<el-button @click="handleAdd" type="primary">新增</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="addVisible" title="新增" width="15%" draggable overflow :close-on-click-modal="false">
		<div style="display: flex; justify-content: center">
			<yhUserSelect style="width: 220px" v-model:value="addInfo.ddUserId" v-model:label="addInfo.userName" v-model:phone="addInfo.phone" v-model:deptName="addInfo.deptName" />
		</div>
		<div style="display: flex; justify-content: center; margin-top: 20px">
			<el-button @click="addVisible = false">取消</el-button>
			<el-button type="primary" @click="addSubmit" v-reclick="1000">确定</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { QueryCashierUserSet, InsertOrUpdateCashierUserSet, DeleteCashierUserSet } from '/@/api/cwManager/cashierSet';
import { ElMessage } from 'element-plus';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));

const options = ref<Public.options[]>([]);
const addVisible = ref(false);
const table = ref();
const query = ref({
	enable: null,
});

let addInfo = ref({
	ddUserId: '',
	userName: '',
	deptName: '',
	phone: '',
});
const changeStatus = async (row: any) => {
	let { success } = await InsertOrUpdateCashierUserSet(row);
	if (success) {
		ElMessage.success('修改成功');
		getList();
	}
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'userName', title: '姓名' },
	{ sortable: true, field: 'phone', title: '手机号' },
	{ sortable: true, field: 'deptName', title: '部门' },
	{ sortable: true, field: 'enable', title: '状态', type: 'switch', handle: changeStatus },
]);
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const clear = () => {
	addInfo.value = {
		ddUserId: '',
		userName: '',
		deptName: '',
		phone: '',
	};
};
const handleAdd = () => {
	clear();
	addVisible.value = true;
};

const addSubmit = async () => {
	if (!addInfo.value.ddUserId) return ElMessage.error('请选择出纳人员');
	let { success } = await InsertOrUpdateCashierUserSet(addInfo.value);
	if (success) {
		ElMessage.success('新增成功');
		addVisible.value = false;
		clear();
		getList();
	}
};
</script>

<style scoped lang="scss"></style>
