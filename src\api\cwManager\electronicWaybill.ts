import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/ElectronicWaybill/`;

//面单管理-导入
export const ImportElectronicWaybillMngList = (params: any, config = {}) => request.post(apiPrefix + 'ImportElectronicWaybillMngList', params, config);

//面单管理-查询
export const GetElectronicWaybillMngPageList = (params: any, config = {}) => request.post(apiPrefix + 'GetElectronicWaybillMngPageList', params, config);

//面单管理-新增
export const SaveElectronicWaybillMng = (params: any, config = {}) => request.post(apiPrefix + 'SaveElectronicWaybillMng', params, config);

//面单管理-获取店铺
export const GetElectronicWaybillShop = (params: any, config = {}) => request.get(apiPrefix + 'GetElectronicWaybillShop', { params, ...config });

//面单管理-删除或禁用
export const DelOrEnabledElectronicWaybillMngById = (params: any, config = {}) => request.get(apiPrefix + 'DelOrEnabledElectronicWaybillMngById', { params, ...config });

//面单数据-查询
export const GetElectronicWaybillWorkDatePageList = (params: any, config = {}) => request.post(apiPrefix + 'GetElectronicWaybillWorkDatePageList', params, config);

//面单数据-充值保存
export const RechargeElectronicWaybillWorkDate = (params: any, config = {}) => request.post(apiPrefix + 'RechargeElectronicWaybillWorkDate', params, config);

//面单数据-导出
export const ExportElectronicWaybillWorkDateList = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportElectronicWaybillWorkDateList', params, config);

//面单数据-充值确认
export const UpdateElectronicWaybillWorkDate_Oper = (params: any, config = {}) => request.post(apiPrefix + 'UpdateElectronicWaybillWorkDate_Oper', params, config);

//面单数据-面单数据编辑
export const EditElectronicWaybillWorkDate = (params: any, config = {}) => request.post(apiPrefix + 'EditElectronicWaybillWorkDate', params, config);

//面单数据-充值明细查询
export const QueryElectronicWaybillRecharge = (params: any, config = {}) => request.post(apiPrefix + 'QueryElectronicWaybillRecharge', params, config);

//面单管理-批量设置导出背景色
export const BulkSetElectronicWaybillMngColor = (params: any, config = {}) => request.post(apiPrefix + 'BulkSetElectronicWaybillMngColor', params, config);

//保存快递岗面单设置 SaveElectronicWaybillSet
export const SaveElectronicWaybillSet = (params: any, config = {}) => request.post(apiPrefix + 'SaveElectronicWaybillSet', params, config);

//获取快递岗面单设置  GetElectronicWaybillSetList
export const GetElectronicWaybillSetList = (params: any, config = {}) => request.post(apiPrefix + 'GetElectronicWaybillSetList', params, config);

//删除快递岗面单设置 DelElectronicWaybillSet
export const DelElectronicWaybillSet = (params: any, config = {}) => request.post(apiPrefix + 'DelElectronicWaybillSet', params, config);

//导出面单管理 ExportElectronicWaybillMng
export const ExportElectronicWaybillMng = (params: any, config = {}) => request.post(apiPrefix + 'ExportElectronicWaybillMng', params, config);

//导入面单管理 ExportElectronicWaybillMng
export const ImportElectronicWaybillMng = (params: any, config = {}) => request.post(apiPrefix + 'ImportElectronicWaybillMng', params, config);

//导入记录 GetElectronicWaybillImportLogPage
export const GetElectronicWaybillImportLogPage = (params: any, config = {}) => request.post(apiPrefix + 'GetElectronicWaybillImportLogPage', params, config);