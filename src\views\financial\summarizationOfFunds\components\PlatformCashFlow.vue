<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end"
					style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20250313094053" :tableCols="tableCols" showsummary isIndexFixed :query="query"
				:query-api="PagePlatformFund" :export-api="ExportPlatformFund"
				:asyncExport="{ title: '平台资金', isAsync: false }" @footerCellClick="onSummaryTotalMap">
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="trendChart.totalMapVisible" width="60%" draggable overflow @close="lengendObject = {}">
		<div>
			<dataRange v-model:startDate="trendChart.start" v-model:endDate="trendChart.end" :clearable="false"
				startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 260px" @change="onTrendChartMethod()" />
			<lineChart v-if="trendChart.totalMapVisible" :chartData="trendChart.analysisData" ref="sumChart" :thisStyle="{
				width: '100%',
				height: '600px',
				'box-sizing': 'border-box',
				'line-height': '600px',
			}" @onLegendMethod="onLegendMethod" :lengendObject="lengendObject" />
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, nextTick } from 'vue';
import { PagePlatformFund, ExportPlatformFund } from '/@/api/financewh/funds';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { QueryPlatformFundCharAnalysis } from '/@/api/financewh/fundsAnalysis';
const query = ref({
	start: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
	platform: null,
});
const lengendObject = ref({});
const table = ref();
const sumChart = ref(); //总资产趋势图组件
const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

const onLegendMethod = (val: any) => {
  lengendObject.value = val
};

const onSummaryTotalMap = async (row: any, column: any) => {
	trendChart.value.columns = column;
	trendChart.value.end = query.value.end;
	trendChart.value.start = dayjs(trendChart.value.end).subtract(30, 'day').format('YYYY-MM-DD');
	onTrendChartMethod();
};

const onTrendChartMethod = async () => {
	const res = await QueryPlatformFundCharAnalysis({
		start: trendChart.value.start,
		end: trendChart.value.end,
		column: trendChart.value.columns,
	});
	trendChart.value.analysisData = res;
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'receiptDate', title: '日期', width: '90', formatter: 'formatDate' },
	{
		title: '汇总',
		align: 'center',
		field: '20250608094422',
		children: [
			{ summaryEvent: true, sortable: true, field: 'z_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'z_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'z_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '淘系',
		align: 'center',
		field: '20250608094431',
		children: [
			{ summaryEvent: true, sortable: true, field: 'tx_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'tx_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '拼多多',
		field: '20250608094441',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'pdd_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '抖音',
		field: '20250608094450',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'dy_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '京东',
		field: '20250608094459',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'jd_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '快手',
		field: '20250608094509',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'ks_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'ks_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'ks_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '小红书',
		field: '20250608094522',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'xhs_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'xhs_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'xhs_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '视频号',
		field: '20250608094531',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'sph_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'sph_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'sph_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '分销',
		field: '20250608094551',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'fx_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'fx_FreezeFund', title: '冻结资金', width: '90', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '各平台核算',
		field: '20250608094553',
		align: 'center',
		tipmesg: '昨日货款余额+总收入-总支出-今日货款余额',
		children: [
			{ summaryEvent: true, sortable: true, field: 'tx_Check', title: '淘系', width: '70', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'dy_Check', title: '抖音', width: '70', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'pdd_Check', title: '拼多多', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'jd_Check', title: '京东', width: '70', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'ks_Check', title: '快手', width: '70', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'xhs_Check', title: '小红书', width: '80', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'sph_Check', title: '视频号', width: '80', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{ sortable: true, field: 'updateTime', title: '更新时间', width: '140', align: 'right', formatter: 'formatTime' },
]);
</script>

<style scoped lang="scss"></style>
