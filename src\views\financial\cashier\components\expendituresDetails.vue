<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss"
					style="width: 300px" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202410071350"
				:treeConfig="{ transform: true, rowField: 'id', parentField: 'parentId' }" :isNeedPager="false"
				showsummary :tableCols="tableCols" :pageSize="1000" :pageSizes="[1000]" :query="query"
				:query-api="QueryReportDetail" />
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { QueryTotalReport, ComputeFee, QueryReportDetail, ExportTotalReportOutAmount } from '/@/api/cwManager/totalReport';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'feeType', title: '费用类型', width: '300', treeNode: true },
	{ sortable: true, field: 'amount', title: '余额', formatter: 'fmtAmt2', align: 'right', width: '300' },
]);
const table = ref();
const query = ref({
	isPay: true,
	startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
});
const exportProps = async () => {
	await ExportTotalReportOutAmount({ ...query.value })
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
</script>

<style scoped lang="scss"></style>
