<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startOccurenceTime" v-model:endDate="query.endOccurenceTime" style="width: 200px" :clearable="false" />
				<el-select v-model="query.accountName" placeholder="别名" class="publicCss" clearable filterable>
					<el-option v-for="item in expenseList" :key="item.account" :label="item.accountName" :value="item.accountName" />
				</el-select>
				<el-select v-model="query.cardNature" placeholder="银行性质" class="publicCss" clearable filterable>
					<el-option v-for="item in natureList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.cardType" placeholder="网银类型" class="publicCss" clearable filterable>
					<el-option key="提现网银" label="提现网银" value="提现网银" />
					<el-option key="出纳账单" label="出纳账单" value="出纳账单" />
					<el-option key="其他对公网银" label="其他对公网银" value="其他对公网银" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="onlineBankingMonthly202506211739"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				:query-api="GetBankFlowMonthReportList"
			>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineAsyncComponent, PropType } from 'vue';
import { GetBankFlowMonthReportList, ExportBankFlowMonthReport } from '/@/api/cwManager/bankFlowMonth';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const props = defineProps({
	expenseTypeList: {
		type: Array as PropType<string[]>,
		default: () => [],
	},
});
const pageLoading = ref(false);
const natureList = ref<Public.options[]>([
	{ label: '个人', value: '个人' },
	{ label: '基本户', value: '基本户' },
	{ label: '一般户', value: '一般户' },
]);
const expenseList = ref<any[]>([]);
const table = ref();
const query = ref({
	startOccurenceTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endOccurenceTime: dayjs().format('YYYY-MM-DD'),
	accountName: '',
	cardNature: '',
	cardType: '',
});
const exportProps = async () => {
	const { success, msg } = await ExportBankFlowMonthReport({ ...query.value, ...table.value.query });
	if (success) {
		window.$message({ message: msg || '导出成功', type: 'success' });
	} else {
		window.$message({ message: msg, type: 'warning' });
	}
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'occurenceMonth', title: '月份', width: '90' },
	{ sortable: true, field: 'accountName', title: '提现账号(别名)' },
	{ sortable: true, field: 'cardNature', title: '银行性质', width: '90' },
	{ sortable: true, field: 'cardType', title: '网银类型', width: '110' },
	{
		title: '日报表汇总',
		align: 'center',
		field: 'DailyReportSummary',
		children: [
			{ sortable: true, field: 'dayLastMonthBalance', title: '上月余额', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'dayInAmount', title: '提现金额', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'dayOutAmount', title: '转出金额', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'dayBalance', title: '余额', width: '110', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{
		title: '月账单汇总',
		align: 'center',
		field: 'MonthlyBillSummary',
		children: [
			{ sortable: true, field: 'monthLastMonthBalance', title: '上月余额-月', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'monthInAmount', title: '到账金额-月', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'monthOutAmount', title: '转出金额-月', width: '110', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'monthBalance', title: '余额-月', width: '110', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{ sortable: true, field: 'differenceAmount', title: '差异', width: '110', formatter: 'fmtAmt2', align: 'right' },
]);

onMounted(() => {
	expenseList.value = props.expenseTypeList
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银');
		})
		.map((item: any) => ({ accountName: item.accountName, account: item.account }));
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
</style>
