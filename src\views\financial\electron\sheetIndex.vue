<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="面单数据" name="first" style="height: 100%">
					<sheetData />
				</el-tab-pane>
				<el-tab-pane label="面单管理" name="second" style="height: 100%" lazy>
					<sheetManagement />
				</el-tab-pane>
				<el-tab-pane label="设置" name="third" style="height: 100%" lazy>
					<settingIndex />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const sheetData = defineAsyncComponent(() => import('./components/sheetData.vue'));
const sheetManagement = defineAsyncComponent(() => import('./components/sheetManagement.vue'));
const settingIndex = defineAsyncComponent(() => import('./components/settingIndex.vue'));
const activeName = ref('first');
</script>
