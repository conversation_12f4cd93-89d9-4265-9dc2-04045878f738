{"name": "admin.net", "type": "module", "version": "2.4.33", "lastBuildTime": "2024.08.30", "description": "Admin.NET 站在巨人肩膀上的 .NET 通用权限开发框架", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite build", "lint-fix": "eslint --fix src/", "build-api": "cd api_build/ && build.bat", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.28", "@microsoft/signalr": "^8.0.7", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^11.0.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "async-validator": "^4.2.5", "axios": "^0.27.2", "countup.js": "^2.8.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "dom-zindex": "^1.0.6", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.8.1", "ezuikit": "^1.0.0", "ezuikit-js": "^8.0.11", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "jsplumb": "^2.15.6", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "md-editor-v3": "^4.19.2", "mitt": "^3.0.1", "monaco-editor": "^0.51.0", "mqtt": "^4.3.8", "nprogress": "^0.2.0", "pinia": "^2.2.2", "print-js": "^1.6.0", "push.js": "^1.0.12", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.13.0", "relation-graph": "^2.2.3", "screenfull": "^6.0.2", "sm-crypto-v2": "^1.9.2", "sortablejs": "^1.15.2", "splitpanes": "^3.1.5", "vcrontab-3": "^3.3.22", "vform3-builds": "^3.0.10", "vue": "^3.4.38", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.6", "vue-draggable-plus": "^0.5.3", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^9.14.0", "vue-json-pretty": "^2.4.0", "vue-plugin-hiprint": "0.0.57-beta27", "vue-router": "^4.4.3", "vue-signature-pad": "^3.0.2", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.1.17", "vxe-table": "^4.7.76", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@plugin-web-update-notification/vite": "^1.7.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.15", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/compiler-sfc": "^3.4.38", "code-inspector-plugin": "^0.16.0", "crypto-js": "^4.2.0", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "file-saver": "^2.0.5", "globals": "^15.9.0", "less": "^4.2.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "terser": "^5.31.6", "typescript": "^5.5.4", "vite": "^5.4.2", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression2": "^1.2.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["admin.net", "vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"]}