<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="流水汇总" name="first" style="height: 100%">
					<flowDetailsSummary />
				</el-tab-pane>
				<el-tab-pane label="流水明细" name="second" style="height: 100%" lazy>
					<flowDetailsTurnover />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const flowDetailsTurnover = defineAsyncComponent(() => import('./flowDetailsTurnover.vue'));
const flowDetailsSummary = defineAsyncComponent(() => import('./flowDetailsSummary.vue'));
const activeName = ref('first');
</script>
