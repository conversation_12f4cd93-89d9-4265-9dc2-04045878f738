<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="时间类型" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="发生时间" label="发生时间" value="发生时间" />
					<el-option key="复核时间" label="复核时间" value="复核时间" />
					<el-option key="创建时间" label="创建时间" value="创建时间" />
				</el-select>
				<dataRange
					v-model:startDate="query.startTime"
					v-model:endDate="query.endTime"
					:shortcuts="false"
					class="publicCss"
					startPlaceholder="开始时间"
					endPlaceholder="结束时间"
					style="width: 230px"
				/>
				<el-select v-model="query.account" placeholder="别名" clearable filterable class="publicCss">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.operatorType" placeholder="业务类型" class="publicCss" clearable filterable>
					<el-option key="提现" label="提现" value="提现" />
					<el-option key="过账" label="过账" value="过账" />
					<el-option key="申请款" label="申请款" value="申请款" />
					<el-option key="期初" label="期初" value="期初" />
					<el-option key="调整" label="调整" value="调整" />
				</el-select>
				<yhUserSelect class="publicCss" v-model:value="query.operatorUserId" :placeholder="'创建人'" :isDdUserId="false" />
				<yhUserSelect class="publicCss" v-model:value="query.verifyUserId" :placeholder="'复核人'" :isDdUserId="false" />
				<el-select v-model="query.flowType" placeholder="收支类型" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="收入" label="收入" value="收入" />
					<el-option key="支出" label="支出" value="支出" />
				</el-select>
				<el-select v-model="query.verifyStatus" placeholder="核对状态" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="复核前" label="复核前" value="复核前" />
					<el-option key="复核后" label="复核后" value="复核后" />
				</el-select>
				<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2" class="publicCss" style="width: 200px" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				<el-select v-model="query.applyStatus" placeholder="发起状态" class="publicCss" clearable filterable>
					<el-option key="已发起" label="已发起" value="已发起" />
					<el-option key="未发起" label="未发起" value="未发起" />
					<el-option key="发起失败" label="发起失败" value="发起失败" />
				</el-select>
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable filterable>
					<el-option key="待复核" label="待复核" value="待复核" />
					<el-option key="已复核" label="已复核" value="已复核" />
				</el-select>
				<el-select v-model="query.toAccount" placeholder="对方账户名" clearable filterable class="publicCss">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawFlow"
			>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { QueryWithDrawFlow } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const props = defineProps({
	reviewInfo: {
		type: Object,
		default: () => ({}),
	},
});
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const nameList = ref<Public.options[]>([]);
const table = ref();
interface CheckboxItem {
	[key: string]: any;
}
const checkboxList = ref<CheckboxItem[]>([]);
const query = ref({
	timeType: '发生时间',
	startTime: '',
	endTime: '',
	account: '',
	operatorType: '',
	operatorUserId: '',
	verifyUserId: '',
	flowType: '',
	verifyStatus: '',
	minAmount: undefined,
	maxAmount: undefined,
	status: '',
	toAccount: '',
	applyStatus: '',
	// ...props.reviewInfo,
});

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'dateTime', title: '日期', width: '135' },
	{ sortable: true, field: 'presenter', title: '提交人', width: '120' },
	{ sortable: true, field: 'processNo', title: '流程号', width: '120' },
	{ sortable: true, field: 'groupId', title: '小组id', width: '80' },
	{ sortable: true, field: 'groupName', title: '小组', width: '120' },
	{ sortable: true, field: 'amount', title: '申请金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_TX', title: '淘系', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_JD', title: '京东', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_SN', title: '苏宁', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_PDD', title: '拼多多', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_DY', title: '抖音', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_DW', title: '得物', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_Video', title: '视频号', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_MZ', title: '喵住', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_1688', title: '1688', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_KJ', title: '跨境', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_XH', title: '小号', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount_PP', title: '品牌', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'remark', title: '备注', width: '200' },
]);

const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
};
const disposeProps = async (data: any, callback: Function) => {
	callback(data);
};
onMounted(() => {
	query.value = { ...query.value, account: props.reviewInfo.account };
	query.value.operatorUserId = '';
	query.value.verifyUserId = '';
	getAllDept();
});
</script>

<style scoped lang="scss">
.table_top {
	display: flex;
	justify-content: space-between;
}
</style>
