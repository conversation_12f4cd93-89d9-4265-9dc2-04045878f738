<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="时间类型" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="发生时间" label="发生时间" value="发生时间" />
					<el-option key="复核时间" label="复核时间" value="复核时间" />
					<el-option key="创建时间" label="创建时间" value="创建时间" />
				</el-select>
				<dataRange
					v-model:startDate="query.startTime"
					v-model:endDate="query.endTime"
					:shortcuts="false"
					class="publicCss"
					startPlaceholder="开始时间"
					endPlaceholder="结束时间"
					style="width: 230px"
				/>
				<el-select v-model="query.account" placeholder="别名" clearable filterable class="publicCss">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.operatorType" placeholder="业务类型" class="publicCss" clearable filterable>
					<el-option key="提现" label="提现" value="提现" />
					<el-option key="过账" label="过账" value="过账" />
					<el-option key="申请款" label="申请款" value="申请款" />
					<el-option key="期初" label="期初" value="期初" />
					<el-option key="调整" label="调整" value="调整" />
				</el-select>
				<yhUserSelect class="publicCss" v-model:value="query.operatorUserId" :placeholder="'创建人'" :isDdUserId="false" />
				<yhUserSelect class="publicCss" v-model:value="query.verifyUserId" :placeholder="'复核人'" :isDdUserId="false" />
				<el-select v-model="query.flowType" placeholder="收支类型" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="收入" label="收入" value="收入" />
					<el-option key="支出" label="支出" value="支出" />
				</el-select>
				<el-select v-model="query.verifyStatus" placeholder="核对状态" style="width: 90px; margin: 0 0 5px 0" clearable filterable>
					<el-option key="复核前" label="复核前" value="复核前" />
					<el-option key="复核后" label="复核后" value="复核后" />
				</el-select>
				<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2" class="publicCss" style="width: 200px" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				<el-select v-model="query.applyStatus" placeholder="发起状态" class="publicCss" clearable filterable>
					<el-option key="已发起" label="已发起" value="已发起" />
					<el-option key="未发起" label="未发起" value="未发起" />
					<el-option key="发起失败" label="发起失败" value="发起失败" />
				</el-select>
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable filterable>
					<el-option key="待复核" label="待复核" value="待复核" />
					<el-option key="已复核" label="已复核" value="已复核" />
				</el-select>
				<el-select v-model="query.toAccount" placeholder="对方账户名" clearable filterable class="publicCss">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="onToReviewMethod" type="primary" :disabled="recheckBan">复核</el-button>
					<el-button @click="onApprovalMethod" type="primary" :disabled="ApprovalBan">发起审批</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedCheckBox
				@select="checkboxChange"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawFlow"
			>
				<template #verifyInAmount="{ row }">
					<el-input-number
						v-model="row.verifyInAmount"
						style="width: 80px"
						clearable
						autocomplete="off"
						:disabled="row.verifyInAmount == 0"
						:min="-9999999"
						:max="9999999"
						:precision="2"
						:controls="false"
						maxlength="50"
					/>
				</template>
				<template #verifyOutAmount="{ row }">
					<el-input-number
						v-model="row.verifyOutAmount"
						style="width: 100px"
						clearable
						autocomplete="off"
						:disabled="row.verifyOutAmount == 0"
						:min="-9999999"
						:max="9999999"
						:precision="2"
						:controls="false"
						maxlength="50"
					/>
				</template>
				<template #toolbar_buttons>
					<!-- <el-button @click="exportProps" type="primary">导出</el-button> -->
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="发起审批" width="600" draggable overflow append-to-body :close-on-click-modal="false" style="margin-top: -10vh !important">
		<applicationMoney v-if="editVisible" :applicationData="checkboxList" @close="editVisibleClose" @closingMethod="editVisible = false" />
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryWithDrawCompanyAccountBalance, UpdateWithDrawCompanyAccountBalance, ExportWithDrawCompanyAccountBalance, VerifyWithDrawFlow } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { QueryWithDrawFlow } from '/@/api/cwManager/withDrawInfo';
import { ElMessageBox, ElLoading } from 'element-plus';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
import dayjs from 'dayjs';
const props = defineProps({
	reviewInfo: {
		type: Object,
		default: () => ({}),
	},
});
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const applicationMoney = defineAsyncComponent(() => import('./applicationMoney.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const ApprovalBan = ref(true);
const recheckBan = ref(false);
const nameList = ref<Public.options[]>([]);
const table = ref();
interface CheckboxItem {
	flowType: string;
	operatorType: string;
	status: string;
	toAccountName: string;
}
const checkboxList = ref<CheckboxItem[]>([]);
const query = ref({
	timeType: '发生时间',
	startTime: '',
	endTime: '',
	account: '',
	operatorType: '',
	operatorUserId: '',
	verifyUserId: '',
	flowType: '',
	verifyStatus: '',
	minAmount: undefined,
	maxAmount: undefined,
	status: '',
	toAccount: '',
	applyStatus: '',
	// ...props.reviewInfo,
});

const onToReviewMethod = () => {
	if (!checkboxList.value.length) {
		window.$message.warning('请选择需要复核的数据');
		return;
	}
	ElMessageBox.confirm('是否确认复核？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { success } = await VerifyWithDrawFlow(checkboxList.value);
		if (success) {
			window.$message.success('复核成功');
			getList();
		}
	});
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
	checkApprovalBan();
};

const checkApprovalBan = () => {
	if (checkboxList.value.length === 0) {
		ApprovalBan.value = true;
		return;
	}
	const hasReviewed = checkboxList.value.some((item) => item.status === '已复核');
	recheckBan.value = hasReviewed;
	// const allValid = checkboxList.value.every((item) => item.toAccountName === checkboxList.value[0]?.toAccountName);
	// ApprovalBan.value = !allValid;
	ApprovalBan.value = checkboxList.value.some((item) => item.operatorType != '申请款');
};

const onApprovalMethod = () => {
	if (!checkboxList.value.length) {
		window.$message.warning('请选择需要发起审批的数据');
		return;
	}
	editVisible.value = true;
};

const editVisibleClose = () => {
	table.value.clearSelection();
	checkboxList.value = [];
	editVisible.value = false;
	getList();
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'accountName', title: '别名', width: '150' },
	{ sortable: true, field: 'operatorType', title: '业务类型', width: '90' },
	{ sortable: true, field: 'flowType', title: '收支类型', width: '90' },
	{
		title: '收入金额',
		align: 'center',
		children: [
			{ sortable: true, field: 'unVerifyInAmount', title: '复核前', width: '80', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'verifyInAmount', title: '复核后', width: '105', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{
		title: '支出金额',
		align: 'center',
		children: [
			{ sortable: true, field: 'unVerifyOutAmount', title: '复核前', width: '80', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'verifyOutAmount', title: '复核后', width: '125', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{ sortable: true, field: 'balance', title: '余额', formatter: 'fmtAmt2', align: 'right', width: '90' },
	{ sortable: true, field: 'occurrenceTime', title: '发生时间', width: '135' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '135' },
	{ sortable: true, field: 'toAccountName', title: '对方账户名(别名)', width: '150' },
	{ sortable: true, field: 'applyStatus', title: '发起状态', width: '90' },
	{ sortable: true, field: 'status', title: '状态', width: '70' },
	{ sortable: true, field: 'operatorUserName', title: '创建人', width: '75' },
	{ sortable: true, field: 'operatorTime', title: '创建时间', width: '135' },
	{ sortable: true, field: 'verifyUserName', title: '复核人', width: '75' },
	{ sortable: true, field: 'verifyTime', title: '复核时间', width: '135' },
]);

const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
};
const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.imgs = item.imgs ? item.imgs.split(',') : [];
		item.withDrawDate = dayjs(item.withDrawDate).format('YYYY-MM-DD');
	});
	callback(data);
};
onMounted(() => {
	query.value = { ...query.value, account: props.reviewInfo.account };
	query.value.operatorUserId = '';
	query.value.verifyUserId = '';
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 50%;
}
::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
.table_top {
	display: flex;
	justify-content: space-between;
}
</style>
