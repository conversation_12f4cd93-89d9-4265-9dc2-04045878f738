<template>
	<div class="timeCss">
		<el-date-picker
			style="width: 100%"
			v-model="timeRange"
			:type="props.type"
			range-separator="至"
			:start-placeholder="props.startPlaceholder"
			:end-placeholder="props.endPlaceholder"
			:placeholder="props.type == 'date' ? props.placeholder : ''"
			:shortcuts="props.shortcuts ? (props.type == 'daterange' ? dateRangeShortcuts : props.type == 'date' ? dateShortcuts : dateTimeShortcuts) : []"
			:align="props.align"
			:clearable="props.clearable"
			:value-format="props.valueFormat"
			@change="changeTime"
		/>
	</div>
</template>

<script setup lang="ts" name="">
import { dateTimeShortcuts, dateRangeShortcuts, dateShortcuts } from '/@/utils/shortcuts';
import { ref, onMounted, watch, defineEmits } from 'vue';
const emit = defineEmits(['change']);
const props = defineProps({
	startPlaceholder: {
		type: String,
		default: '开始时间',
	},
	endPlaceholder: {
		type: String,
		default: '结束时间',
	},
	type: {
		type: String,
		default: 'daterange',
	},
	align: {
		type: String,
		default: 'left',
	},
	clearable: {
		type: Boolean,
		default: true,
	},
	valueFormat: {
		type: String,
		default: 'YYYY-MM-DD',
	},
	placeholder: {
		type: String,
		default: '请选择日期',
	},
	// 是否显示快捷选项
	shortcuts: {
		type: Boolean,
		default: true,
	},
});
const startDate: any = defineModel('startDate');
const endDate: any = defineModel('endDate');
const date: any = defineModel('date');
const timeRange = ref<string[] | string>();
const changeTime = (val: string[]) => {
	if (props.type === 'daterange' || props.type === 'datetimerange') {
		startDate.value = val ? val[0] : '';
		endDate.value = val ? val[1] : '';
	} else if (props.type === 'date') {
		date.value = val;
	}
	emit('change', val);
};
watch(
	[() => startDate.value, () => endDate.value, () => date.value],
	() => {
		if (props.type === 'daterange' || props.type === 'datetimerange') {
			timeRange.value = [startDate.value, endDate.value];
		} else if (props.type === 'date') {
			timeRange.value = date.value;
		}
	},
	{ immediate: true }
);
onMounted(() => {
	if (props.type === 'daterange' || props.type === 'datetimerange') {
		timeRange.value = [startDate.value, endDate.value];
	} else if (props.type === 'date') {
		timeRange.value = date.value;
	}
});
</script>

<style scoped lang="scss"></style>
