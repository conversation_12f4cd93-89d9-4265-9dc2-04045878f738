<template>
	<div style="width: 100%; height: 100%" v-loading="loading">
		<Container>
			<template #header>
				<div class="topCss">
					<el-input v-model.trim="query.po_Ids" class="publicCss" placeholder="采购单号" clearable
						maxlength="50" />
					<el-input v-model.trim="query.sku_ids" class="publicCss" placeholder="商品编码" clearable
						maxlength="50" />
					<el-input v-model.trim="query.adder" class="publicCss" placeholder="添加人" clearable maxlength="5" />
					<el-input v-model.trim="query.remark" class="publicCss" placeholder="备注" clearable
						maxlength="150" />
					<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate"
						startPlaceholder="开始日期" endPlaceholder="结束日期" class="publicCss" style="width: 200px" />
					<dataRange v-model:startDate="query.startRefundDatetime" v-model:endDate="query.endRefundDatetime"
						startPlaceholder="退款开始日期" endPlaceholder="退款结束日期" class="publicCss" style="width: 200px" />
					<el-select v-model="query.status" placeholder="状态" class="publicCss itemCss" filterable clearable>
						<el-option label="待审核" value="待审核" />
						<el-option label="已审核" value="已审核" />
					</el-select>
					<el-select v-model="query.collectType" placeholder="收款信息" class="publicCss itemCss" filterable
						clearable>
						<el-option v-for="item in bankNameList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<numRange v-model:maxNum="query.maxTotalRefundAmount" v-model:minNum="query.minTotalRefundAmount"
						:precision="2" :min="0" style="width: 230px; margin: 0 10px 5px 0" minPlaceHolder="总退款最小值"
						maxPlaceHolder="总退款最大值" />
					<numRange v-model:maxNum="query.maxAdjustAmount" v-model:minNum="query.minAdjustAmount"
						:precision="2" style="width: 230px; margin: 0 10px 5px 0" minPlaceHolder="调整金额≥"
						maxPlaceHolder="调整金额<" />
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" :pageSize="30" id="**************" :pageSizes="[30, 50, 100, 200, 300]"
					:tableCols="tableCols" :query="query" :orderBy="'addTime'" :isAsc="false" isNeedDisposeProps
					@disposeProps="disposeProps" showsummary
					:treeConfig="{ transform: true, rowField: 'id', parentField: 'parentId' }"
					:query-api="GetPurchaseOrderRefundDetails" isNeedCheckBox @select="checkboxChange"
					@visibleMethod="visibleMethod">
					<template #toolbar_buttons>
						<!-- <el-button @click="onSignMethod" type="primary">登记</el-button> -->
						<!-- <el-button @click="onApprovalMethod" type="primary">审批</el-button> -->
						<el-button @click="onApproveMethod(1)" type="primary" :disabled="onlyCheck">审批通过</el-button>
						<el-button @click="onApproveMethod(2)" type="primary" :disabled="rejectCheck">审批驳回</el-button>
						<el-button @click="onAdjustCause" type="primary">调整原因</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
					</template>
				</vxetable>
			</template>
		</Container>
		<el-dialog v-model="dialogMapVisible" title="登记" width="1600" draggable overflow :close-on-click-modal="false"
			style="margin-top: -10vh">
			<div style="height: 720px">
				<registration ref="refregistration" v-if="dialogMapVisible" :verify="verify"
					:purchaseNumber="purchaseNumber" @onStorageMethod="onStorageMethod" />
			</div>
			<div style="display: flex; justify-content: center; gap: 20px; margin-top: 10px">
				<el-button @click="dialogMapVisible = false">取 消</el-button>
				<el-button type="primary" @click="onSortSave">保 存</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="dialogApprovalPair" title="审批" width="300" draggable overflow :close-on-click-modal="false"
			style="margin-top: -10vh">
			<div
				style="height: 150px; display: flex; align-items: center; justify-content: center; padding-bottom: 40px">
				<el-radio-group v-model="approval">
					<el-radio value="1" size="large">通过</el-radio>
					<el-radio value="2" size="large">驳回</el-radio>
				</el-radio-group>
			</div>
			<div style="display: flex; justify-content: center; gap: 20px">
				<el-button @click="dialogApprovalPair = false">取 消</el-button>
				<el-button type="primary" @click="shenpiFuc">审 批</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="reasonForAdjustment" title="调整原因" width="350" draggable overflow
			:close-on-click-modal="false" style="margin-top: -10vh">
			<div style="height: 250px; padding-top: 10px">
				<el-input v-model="justment.refundReason" placeholder="请输入调整原因" type="textarea" autocomplete="off"
					clearable maxlength="500" show-word-limit :autosize="{ minRows: 9, maxRows: 9 }" resize="none" />
			</div>
			<div style="display: flex; justify-content: center; gap: 20px">
				<el-button @click="reasonForAdjustment = false">取 消</el-button>
				<el-button type="primary" @click="justmentSave">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog v-model="reject.visible" title="驳回原因" width="450" draggable overflow
			:close-on-click-modal="false" style="margin-top: -10vh">
			<div style="height: 150px; padding-top: 10px">
				<el-form
					ref="ruleFormRef"
					style="max-width: 600px"
					:model="reject"
					:rules="rejectrules"
					label-width="auto"
				>
					<el-form-item label="驳回原因" prop="rejectReason">
						<el-input v-model="reject.rejectReason" placeholder="请输入驳回原因" type="textarea" autocomplete="off"
							clearable maxlength="50" show-word-limit :autosize="{ minRows: 4, maxRows: 4 }" resize="none" />
					</el-form-item>
				</el-form>
			</div>
			<div style="display: flex; justify-content: center; gap: 20px">
				<el-button @click="reject.visible = false">取 消</el-button>
				<el-button type="primary" @click="rejectSave(ruleFormRef)">确 定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="modificationAmount" title="编辑" width="1000" draggable overflow :close-on-click-modal="false"
			style="margin-top: -10vh">
			<div>
				<el-table :data="amountList" style="width: 100%" height="350px">
					<el-table-column prop="po_Id" label="采购单号" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="sku_id" label="商品编码" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="buy_Amount" label="采购金额" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="cost_Amount" label="已入库金额" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="pendingAmount" label="未入库金额" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="outboundAmount" label="出库金额">
						<template #default="{ row, $index }">
							<el-input-number v-model="row.outboundAmount" :min="-9999999" :max="9999999" :precision="2"
								:controls="false" style="width: 95%" placeholder="请输入" />
						</template>
					</el-table-column>
					<el-table-column prop="returnAmount" label="退货款" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="returnFreight" label="退运费" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="totalRefunsds" label="总退款" width="100" :show-overflow-tooltip="true" />
				</el-table>
			</div>
			<div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px">
				<el-button @click="modificationAmount = false">取 消</el-button>
				<el-button type="primary" @click="onSingleSave">保 存</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue';
import {
	GetPurchaseOrderRefundDetails,
	ExportPurchaseOrderRefundDetails,
	editPurchaseOrderRefundDetailsERP,
	editPurchaseOrderRefundDetails,
	editPurchaseOrderRefundReason,
} from '/@/api/inventory/purchase';
import { approvedPurchaseOrderRefundToCw, RefusePurchaseOrderRefund } from '/@/api/cwManager/feeData';
import { QueryOnlineBankSet, } from '/@/api/cwManager/cashierSet';

import dayjs from 'dayjs';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { curry } from 'lodash-es';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const registration = defineAsyncComponent(() => import('/@/views/financial/refundManagement/components/registration.vue'));
const allshenpi = defineAsyncComponent(() => import('/@/views/financial/refundManagement/components/allshenpi.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const dialogMapVisible = ref(false);
const dialogApprovalPair = ref(false);
const approval = ref('1');
const loading = ref(false);
const refregistration = ref<InstanceType<typeof registration> | null>(null);
import { decimal } from '/@/utils/decimal';
const query = ref({
	po_Ids: '',
	sku_ids: '',
	adder: '',
	status: '待审核',
	startDate: '',
	endDate: '',
	remark: '',
	collectType: '',
	maxTotalRefundAmount: undefined,
	minTotalRefundAmount: undefined,
	maxAdjustAmount: undefined,
	minAdjustAmount: undefined,
	startRefundDatetime: '',
	endRefundDatetime: '',
});

interface Justment {
	po_Ids: string[];
	refundReason: string;
}

const justment = ref<Justment>({
	po_Ids: [],
	refundReason: '',
});
const modificationAmount = ref(false);
const reasonForAdjustment = ref(false);
const purchaseNumber = ref<string[]>([]);
const verify = ref(false);
const onlyCheck = ref(false);
const rejectCheck = ref(false);
const tableData = ref([]);
const amountList = ref<any[]>([]);
const bankNameList = ref<any[]>([]);
const reject = ref({
	visible : false,
	rejectReason : null,
});
const rejectrules = reactive({
	rejectReason: [
		{ required: true, message: '请输入驳回原因', trigger: 'blur' },
		{ max: 50, message: '驳回原因不能超过50个字符', trigger: 'blur' },
	],
});
const ruleFormRef = ref<FormInstance>();
interface SelListItem {
	parentId: number | string;
	status: string;
	refundReason: string;
}
const submitobj = ref<{ selList: SelListItem[] }>({ selList: [] });

const table = ref();

const onSortSave = () => {
	refregistration.value?.onSaveMethod();
};

const onApprovalMethod = () => {
	if (submitobj.value.selList.length == 0) {
		window.$message.info('请选择选择需要审批的数据！');
		return;
	}
	dialogApprovalPair.value = true;
};

const onAdjustCause = () => {
	if (submitobj.value.selList.length == 0) {
		window.$message.warning('请选择需要调整的数据！');
		return;
	}
	justment.value.po_Ids = [];
	justment.value.refundReason = '';
	if (submitobj.value.selList.length == 1) {
		justment.value.refundReason = submitobj.value.selList[0].refundReason;
	}
	reasonForAdjustment.value = true;
};

const justmentSave = async () => {
	if (!justment.value.refundReason) {
		window.$message.error('请输入调整原因');
		return;
	}
	justment.value.po_Ids = submitobj.value.selList.filter((item1: any) => item1.po_Id).map((item: any) => item.po_Id);
	const { success } = await editPurchaseOrderRefundReason({ ...justment.value });
	if (success) {
		window.$message.success('调整原因成功');
		justment.value.refundReason = '';
		submitobj.value.selList = [];
		reasonForAdjustment.value = false;
		getList();
	}
};

const onApproveMethod = (val: any) => {
	if (submitobj.value.selList.length == 0) {
		window.$message.info('请选择选择需要审批的数据！');
		return;
	}
	if (val == 1) {
		approval.value = '1';
		shenpiFuc();
	} else {
		approval.value = '2';
		reject.value.rejectReason = null;
		reject.value.visible = true;
		if (ruleFormRef.value) {
			ruleFormRef.value.resetFields();
			ruleFormRef.value.clearValidate();
		}
	}
};

const rejectSave = async (formEl:any) => {
  if (!formEl) return
  await formEl.validate((valid:any, fields:any) => {
    if (valid) {
		shenpiFuc();
    }
  })
}

const shenpiFuc = () => {
	let senewlist: string[] = [];
	submitobj.value.selList.map((item: any) => {
		if (item.parentId == '0' || item.parentId == 0) {
			senewlist.push(item.batchNumber);
		}
	});
	if (senewlist.length == 0) {
		window.$message.info('请选择至少一条数据！');
		return;
	}
	// state.dialogVisible = true;
	ElMessageBox.confirm(`此操作将${approval.value == '2' ? '批量驳回审批' : '批量通过审批'}`, '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			if (approval.value == '1') {
				const { success } = await approvedPurchaseOrderRefundToCw({ BatchNumber: senewlist });
				if (success) {
					window.$message.success('审批通过操作成功');
					getList();
				}
			} else {
				const { success } = await RefusePurchaseOrderRefund({ BatchNumber: senewlist, rejectReason:reject.value.rejectReason });
				if (success) {
					window.$message.success('审批驳回操作成功');
					reject.value.rejectReason = null;
					reject.value.visible = false;
					getList();
				}
			}
		})
		.catch(() => {
			// window.$message.info('已取消');
		});
};

const exportProps = async () => {
	loading.value = true;
	await ExportPurchaseOrderRefundDetails({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '采购单退款导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const checkboxChange = (value: any) => {
	submitobj.value.selList = value;
	onlyCheck.value = submitobj.value.selList.some((item) => item.parentId == 0 && item.status === '已审核');
	// rejectCheck.value = submitobj.value.selList.some((item) => item.parentId == 0 && item.status === '待审核');
};
const visibleMethod = (data: any, callback: any) => {
	callback(data.row.parentId == '0' || data.row.parentId == 0 ? true : false);
};

const onStorageMethod = () => {
	dialogMapVisible.value = false;
	table.value.query.orderBy = 'addTime';
	table.value.query.isAsc = false;
	table.value.getList();
};

const onSignMethod = () => {
	verify.value = true;
	purchaseNumber.value = [];
	dialogMapVisible.value = true;
};
const handleEdit = (row: any) => {
	ElMessageBox.confirm('是否确认编辑？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			purchaseNumber.value = [];
			tableData.value.forEach((item: any) => {
				if (item.parentId == row.id) {
					purchaseNumber.value.push(item.po_Id);
					purchaseNumber.value = Array.from(new Set(purchaseNumber.value));
				}
			});
			verify.value = false;
			dialogMapVisible.value = true;
		})
		.catch(() => {
			ElMessage.info('已取消编辑');
		});
};

const modificationEdit = async (row: any) => {
	let purchaseNumber: (string | number)[] = []; // 显式声明类型
	tableData.value.forEach((item: any) => {
		if (item.parentId == row.id) {
			purchaseNumber.push(item.po_Id);
			purchaseNumber = Array.from(new Set(purchaseNumber));
		} else if (item.id == row.id) {
			purchaseNumber.push(item.po_Id);
			purchaseNumber = Array.from(new Set(purchaseNumber));
		}
	});
	loading.value = true;
	const { data, success } = await editPurchaseOrderRefundDetailsERP(purchaseNumber);
	loading.value = false;
	if (!success) return;
	amountList.value = data.list;
	modificationAmount.value = true;
};

const onSingleSave = async () => {
	ElMessageBox.confirm('是否确认编辑？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await editPurchaseOrderRefundDetails(amountList.value);
			if (success) {
				window.$message.success('编辑成功');
				modificationAmount.value = false;
				getList();
			}
		})
		.catch(() => {
			ElMessage.info('已取消编辑');
		});
};

const getList = () => {
	if (query.value.maxTotalRefundAmount !== undefined && query.value.maxTotalRefundAmount !== null && query.value.minTotalRefundAmount !== undefined && query.value.minTotalRefundAmount !== null) {
		const maxTotalRefundAmount = parseFloat(query.value.maxTotalRefundAmount);
		const minTotalRefundAmount = parseFloat(query.value.minTotalRefundAmount);
		if (maxTotalRefundAmount < minTotalRefundAmount) {
			window.$message.error('最小总退款金额不能大于最大总退款金额,请重新输入!');
			return;
		}
	}
	table.value.query.currentPage = 1;
	table.value.getList();
	submitobj.value.selList = [];
	table.value.clearSelection();
};

const tableCols = ref<VxeTable.Columns[]>([
	// { sortable: true, field: 'batchNumber', width: '150', title: '批次号', treeNode: true },
	{ sortable: true, field: 'status', title: '状态', width: '100', treeNode: true },
	{ sortable: true, field: 'po_Id', title: '采购单号', width: '100' },
	{ sortable: true, field: 'isPay', title: '是否付款', width: '90' },
	// { sortable: true, field: 'sku_id', title: '商品编码', width: '100', align: 'right' },
	// { sortable: true, field: 'price', title: '单价', width: '100', formatter: 'fmtAmt2', align: 'right' },
	// { sortable: true, field: 'buy_qty', title: '采购数量', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'buy_Amount', title: '采购金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'payAmount', title: '付款金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	// { sortable: true, field: 'qty', title: '已入库数量', width: '100', formatter: 'fmtAmt0', align: 'right' },
	// { sortable: true, field: 'pending_qty', title: '未入库数量', width: '100', formatter: 'fmtAmt0', align: 'right' },
	// { sortable: true, field: 'cost_Amount', title: '退货数量', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'cost_Amount', title: '已入库金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'pendingAmount', title: '未入库金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outboundAmount', title: '出库金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'returnAmount', title: '退货款', width: '100', formatter: 'fmtAmt2', align: 'right' },
	// { sortable: true, field: 'adjustType', title: '调整类型', width: '100' },
	{ sortable: true, field: 'returnFreight', title: '退运费', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'adjustAmount', title: '调整金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'totalRefunsds', title: '总退款', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'refundDatetime', title: '退款时间', width: '100' },
	{ sortable: true, field: 'collectType', title: '收款方式', width: '100' },
	{ sortable: true, field: 'collectUser', title: '收款人', width: '100' },
	{ sortable: true, field: 'collectAccount', title: '收款账号', width: '100' },
	{ field: 'annexUrl', title: '附件', type: 'image', width: '100' },
	{ sortable: true, field: 'adder', title: '添加人', width: '100' },
	{ sortable: true, field: 'addTime', title: '添加时间', width: '100' },
	{ sortable: true, field: 'remark', title: '备注', width: '100' },

	{ sortable: true, field: 'cwAduitTime', title: '审核通过时间', width: '100' },
	{ sortable: true, field: 'cwAduitUserName', title: '审核人', width: '100' },
	{ sortable: true, field: 'refundReason', title: '调整原因', width: '100' },
	// { sortable: true, field: 'cwAduitStatus', title: '审批状态', width: '100', formatter: (row: any) => (row.cwAduitStatus == 0 ? '待审核' : row.cwAduitStatus == 2 ? '已审核' : '-') },
	// {
	// 	title: '操作',
	// 	align: 'center',
	// 	width: '90',
	// 	type: 'btnList',
	// 	minWidth: '90',
	// 	direction: 'column',
	// 	btnList: [{ title: '编辑', handle: handleEdit, isDisabled: (row) => row.parentId != 0 }],
	// 	fixed: 'right',
	// },
	{
		title: '操作',
		align: 'center',
		width: '70',
		type: 'btnList',
		minWidth: '70',
		field:'20250608092822',
		direction: 'column',
		btnList: [{ title: '编辑', handle: modificationEdit, isDisabled: (row) => row.parentId != 0 || row.status == '已审核' }],
		fixed: 'right',
	},
]);
const disposeProps = async (data: any, callback: Function) => {
	let a = 0
	data.data.list.forEach((item: any, index: any) => {
		item.addTime = item.addTime ? dayjs(item.addTime).format('YYYY-MM-DD') : '';
		item.refundDatetime = item.refundDatetime ? dayjs(item.refundDatetime).format('YYYY-MM-DD') : '';
		a = Number(decimal(a, item.payAmount, 2, '+'))
	});
	tableData.value = data.data.list;
	if (Array.isArray(data.data.summary)) {
		const summary = data.data.summary;
		if (summary[0] && !('payAmount' in summary[0])) {
			summary[0].payAmount = a;
		}
		if (summary[1] && !('payAmount' in summary[1])) {
			summary[1].payAmount = '';
		}
	}
	callback(data);
};
const getBankName = async () => {
	const { data, success } = await QueryOnlineBankSet({ currentPage: 1, pageSize: 100000 });
	if (success) {
		bankNameList.value = data.list.filter((item: any) => item.busAccountName).map((item1: any) => {
			return {
				label: item1.account + '-' + item1.userName + '-' + item1.bankType,
				value: item1.account
			}
		})
	}
}
onMounted(() => {
	getBankName()
});
</script>

<style scoped lang="scss">
.btnGruop {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

.publicCss {
	width: 150px;
	margin-right: 10px;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
</style>
