<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="税费明细" name="first" style="height: 100%">
					<detailTax />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const detailTax = defineAsyncComponent(() => import('./components/detailTax.vue'));
const activeName = ref('first');
</script>
