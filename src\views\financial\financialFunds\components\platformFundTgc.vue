<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange
					v-model:startDate="query.startDate"
					v-model:endDate="query.endDate"
					class="publicCss"
					:clearable="false"
					startPlaceholder="开始时间"
					endPlaceholder="结束时间"
					style="width: 230px"
				/>
				<el-input v-model.trim="query.platformShopId" placeholder="平台店铺ID" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.shopIdList" placeholder="店铺" class="publicCss" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" style="width: 200px">
					<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.status" placeholder="请选择状态" class="publicCss" style="width: 170px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in statusList" :key="item" :label="item" :value="item"></el-option>
				</el-select>
				<el-select v-model="query.type" placeholder="类型" clearable class="publicCss">
					<el-option label="微信" value="微信" />
					<el-option label="支付宝" value="支付宝" />
				</el-select>
				<el-select v-model="query.checkStatus" placeholder="验算" clearable class="publicCss">
					<el-option label="验算正确" value="验算正确" />
					<el-option label="验算错误" value="验算错误" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="pullProps" type="primary">拉取</el-button>
					<el-button @click="Payouts" type="primary">提现</el-button>
					<el-button @click="check" type="primary">验算</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<div class="container-relative">
				<div class="radio-group-absolute">
					<el-radio-group v-model="radioValue" size="small" @change="getList">
						<el-radio-button label="货款余额" value="货款余额" />
						<el-radio-button label="推广账户" value="推广账户" />
						<el-radio-button label="保证金账户" value="保证金账户" />
					</el-radio-group>
					<el-button @click="exportProps(radioValue)" type="primary" class="ml5" :disabled="rootDisabled">导出</el-button>
					<el-button @click="onLockUnlock('lock')" type="primary" class="ml5" :disabled="rootDisabled">锁定</el-button>
					<el-button @click="onLockUnlock('unlock')" type="primary" class="ml5" :disabled="rootDisabled">解锁</el-button>
					<span class="shop-text" :title="shopWithDraw">{{ shopWithDraw }}</span>
				</div>
				<vxetable
					showsummary
					ref="table"
					v-if="radioValue === '货款余额'"
					id="platformFundTgc2025030109510"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'totalFundBalance' &&
								col.field !== 'couponBalance' &&
								col.field !== 'totalAmount' &&
								col.field !== 'oldDepositAmount' &&
								col.field !== 'extendAmountBalance' &&
								col.field !== 'availableBalance' &&
								col.field !== 'totalBalance' &&
								col.field !== 'frozenAmount' &&
								col.field !== 'depositAmount' &&
								col.field !== 'depositOutcome' &&
								col.field !== 'depositIncome' &&
								col.field !== 'depositAmountDifference' &&
								col.field !== 'netIncomeOutcome'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetTGCDailyAccount"
					@footerCellClick="onSummaryTotalMap"
					isNeedDisposeProps
					@disposeProps="disposeProps"
				>
					<template #endBalance="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="endBalance" />
					</template>
					<template #goodsIncome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="goodsIncome" />
					</template>
					<template #fee="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="fee" />
					</template>
					<template #totalIncome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalIncome" :min="0" :max="********" />
					</template>
					<template #totalOutcome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalOutcome" :min="-********" :max="0" />
					</template>
				</vxetable>
				<vxetable
					showsummary
					ref="table1"
					v-if="radioValue === '推广账户'"
					id="platformFundTgc2025030109511"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'openingBalance' &&
								col.field !== 'endBalance' &&
								col.field !== 'realTimeBalance' &&
								col.field !== 'withDrawAmount' &&
								col.field !== 'fee' &&
								col.field !== 'totalAmount' &&
								col.field !== 'type' &&
								col.field !== 'couponBalance' &&
								col.field !== 'oldDepositAmount' &&
								col.field !== 'depositAmount' &&
								col.field !== 'depositOutcome' &&
								col.field !== 'depositIncome' &&
								col.field !== 'depositAmountDifference' &&
								col.field !== 'promotionalFeeRecharge' &&
								col.field !== 'goodsIncome' &&
								col.field !== 'checkStatus' &&
								col.field !== 'netCashFlow'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetTGCPromotionAccount"
					@footerCellClick="onSummaryTotalMap"
				>
				</vxetable>
				<vxetable
					showsummary
					ref="table2"
					v-if="radioValue === '保证金账户'"
					id="platformFundTgc2025030109512"
					:tableCols="
						tableCols.filter(
							(col) =>
								col.field !== 'openingBalance' &&
								col.field !== 'endBalance' &&
								col.field !== 'withDrawAmount' &&
								col.field !== 'fee' &&
								col.field !== 'realTimeBalance' &&
								col.field !== 'totalFundBalance' &&
								col.field !== 'couponBalance' &&
								col.field !== 'type' &&
								col.field !== 'totalOutcome' &&
								col.field !== 'totalIncome' &&
								col.field !== 'totalAmount' &&
								col.field !== 'availableBalance' &&
								col.field !== 'totalBalance' &&
								col.field !== 'frozenAmount' &&
								col.field !== 'promotionalFeeRecharge' &&
								col.field !== 'goodsIncome' &&
								col.field !== 'checkStatus' &&
								col.field !== 'netCashFlow'
						)
					"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetTGCDepositAccount"
					@footerCellClick="onSummaryTotalMap"
				>
				</vxetable>
			</div>
		</template>
	</Container>

	<el-dialog v-model="totalMapVisible" width="60%" draggable overflow>
		<div>
			<dataRange
				v-model:startDate="trendChart.startDate"
				v-model:endDate="trendChart.endDate"
				:clearable="false"
				startPlaceholder="开始时间"
				endPlaceholder="结束时间"
				style="width: 260px"
				@change="onTrendChartMethod(2)"
			/>
			<lineChart
				v-if="totalMapVisible"
				:chartData="analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '600px',
					'box-sizing': 'border-box',
					'line-height': '600px',
				}"
			/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import {
	GetTGCDailyAccount,
	GetTGCPromotionAccount,
	GetTGCDepositAccount,
	ExportTGCDailyAccount,
	ExportTGCDepositAccount,
	ExportTGCPromotionAccount,
	LockDailyBalanceData,
	UnLockDailyBalanceData,
	GetTGCDailyAccountTotalMap,
	GetTGCPromotionAccountTotalMap,
	GetTGCDepositAccountTotalMap,
	EditTGCGoodsBalance,
	InitShopData,
	SyncShopDataWithDrawAmount,
	CheckShopDataDailyBalance,
} from '/@/api/cwManager/cwFundsDailyBalance';
import { GetWithDrawShopList } from '/@/api/cwManager/withDrawInfo';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
import { ElMessageBox, ElMessage } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const editableNumber = defineAsyncComponent(() => import('/@/components/yhCom/editableNumber.vue'));
const pageLoading = ref(false);
const timeRange = ref('');
const radioValue = ref('货款余额');
const statusList = ref(['待导入', '已导入', '核算正确', '核算错误', '已锁定']);
const shopNamelist = ref<Public.options[]>([]);
const checkboxList = ref<any>([]);
const table = ref();
const rootDisabled = ref(false);
const table1 = ref();
const table2 = ref();
const detailName = ref('');
const totalMapName = ref('');
const totalMapVisible = ref(false);
const analysisData = ref<any>({});
const sumChart = ref();
const shopWithDraw = ref('');
const trendChart = ref({
	startDate: '',
	endDate: '',
	shopIdList: [] as Array<string>,
	status: [] as Array<string>,
	platformShopId: '',
	type: '',
});
const query = ref({
	startDate: '',
	endDate: '',
	shopName: '',
	status: [],
	financialType: '',
	shopIdList: [],
	type: '',
	platformShopId: '',
	checkStatus: '',
});
const tableData = ref<any[]>([]);
const backupTableData = ref<any[]>([]);

const formatFixedAmt = (value: number) => (value ? value.toFixed(2) : 0);

const findRowIndex = (row: any) => tableData.value.findIndex((item: any) => item.mesgID === row.mesgID);

// 获取当前选中的表格
const getCurrentTable = () => {
	const tableRefs = {
		货款余额: table,
		推广账户: table1,
		保证金账户: table2,
	};
	return tableRefs[radioValue.value as keyof typeof tableRefs];
};

const pullProps = async () => {
	ElMessageBox.confirm(`${query.value.startDate}-${query.value.endDate}的已导入数据会丢失，是否确认拉取？`, '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await InitShopData({ startDate: query.value.startDate, endDate: query.value.endDate, platform: 8 });
			if (success) {
				ElMessage.success('拉取成功');
				getList();
			} else {
				ElMessage.error('拉取失败，请稍后再试！');
			}
		})
		.catch(() => {});
};

const Payouts = async () => {
	const { success, data } = await SyncShopDataWithDrawAmount({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('提现成功');
	} else {
		ElMessage.error('提现失败，请稍后再试！');
	}
};

const check = async () => {
	const { success, data } = await CheckShopDataDailyBalance({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('验算成功');
	} else {
		ElMessage.error('验算失败，请稍后再试！');
	}
};

// 更新表格数据并同步 UI
const updateTableData = (row: any, status: boolean) => {
	if (status && row.status === '已锁定') {
		ElMessage.warning('已锁定数据不能编辑，请解锁后编辑');
		return;
	}
	const index = findRowIndex(row);
	if (index !== -1) {
		tableData.value[index].statusVerify = status;
		getCurrentTable()?.value.onAssignedData(tableData.value);
	}
};

// 编辑
const handleEdit = (row: any) => updateTableData(row, true);

// 取消
const handleCancel = (row: any) => {
	const index = findRowIndex(row);
	if (index !== -1) {
		// 恢复备份数据
		BACKUP_FIELDS.forEach((field) => {
			tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
		});
		updateTableData(row, false);
	}
};

// 保存
const handleSave = async (row: any) => {
	let a = BACKUP_FIELDS;
	const index = findRowIndex(row);
	if (index === -1) return;
	updateTableData(row, false);
	// 删除备份字段
	BACKUP_FIELDS.forEach((field) => {
		delete row[`${field}_Backup`];
	});
	try {
		const response = await EditTGCGoodsBalance({ ...row });
		// 处理 API 返回的数据
		if (response.success) {
			backupTableData.value = [...tableData.value]; // 深拷贝，避免数据引用问题
			ElMessage.success('保存成功');
			// 创建新的备份
			BACKUP_FIELDS.forEach((field) => {
				tableData.value[index][`${field}_Backup`] = tableData.value[index][field];
			});
			getCurrentTable()?.value.getList();
		} else {
			// ElMessage.error(response.msg || '保存失败');
			a.forEach((field) => {
				tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
			});
		}
	} catch (error) {
		console.error('API 请求失败:', error);
		ElMessage.error('保存请求失败，请稍后再试！');
	}
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const onLockUnlock = async (type: 'lock' | 'unlock') => {
	if (checkboxList.value.length === 0) {
		ElMessage.warning('请选择需要锁定/解锁的数据');
		return;
	}
	ElMessageBox.confirm(`确定要${type === 'lock' ? '锁定' : '解锁'}这些数据吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			let radiotype = '';
			if (radioValue.value === '货款余额') {
				radiotype = '货款';
			} else if (radioValue.value === '推广账户') {
				radiotype = '营销';
			} else if (radioValue.value === '保证金账户') {
				radiotype = '保证金';
			}
			const params = {
				platform: 8,
				tableType: radiotype,
				mesgIdList: checkboxList.value.map((item: any) => item.mesgID),
			};
			const { success } = type === 'lock' ? await LockDailyBalanceData(params) : await UnLockDailyBalanceData(params);
			if (success) {
				ElMessage.success('操作成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage.info(`已取消${type === 'lock' ? '锁定' : '解锁'}`);
		});
};

const exportProps = async (val: string) => {
	const handleExport = async (exportFunc: any, fileNamePrefix: string, platform: string) => {
		try {
			const data = await exportFunc({
				...query.value,
				...(val === '货款余额' ? table.value.query : val === '推广账户' ? table1.value.query : table2.value.query),
			});
			if (data) {
				const aLink = document.createElement('a');
				const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.download = `${fileNamePrefix}-${platform}${new Date().toLocaleString()}.xlsx`;
				document.body.appendChild(aLink); // 兼容Firefox
				aLink.click();
				document.body.removeChild(aLink);
			}
		} finally {
			rootDisabled.value = false;
		}
	};
	switch (val) {
		case '货款余额':
			await handleExport(ExportTGCDailyAccount, '货款余额', '淘工厂');
			break;
		case '保证金账户':
			await handleExport(ExportTGCDepositAccount, '保证金余额', '淘工厂');
			break;
		case '推广账户':
			await handleExport(ExportTGCPromotionAccount, '推广账户', '淘工厂');
			break;
	}
};

const onSummaryTotalMap = async (row: any, column: any, shopId: string) => {
	trendChart.value.shopIdList = query.value.shopIdList ? query.value.shopIdList : [];
	trendChart.value.type = query.value.type ? query.value.type : '';
	trendChart.value.status = query.value.status ? query.value.status : [];
	trendChart.value.platformShopId = query.value.platformShopId ? query.value.platformShopId : '';
	onTrendChartMethod(1);
};

const onTrendChartMethod = async (val: number) => {
	const apiMap = {
		货款余额: {
			name: '货款余额趋势图',
			api: GetTGCDailyAccountTotalMap,
		},
		推广账户: {
			name: '推广账户趋势图',
			api: GetTGCPromotionAccountTotalMap,
		},
		保证金账户: {
			name: '保证金账户趋势图',
			api: GetTGCDepositAccountTotalMap,
		},
	};
	const config = apiMap[radioValue.value as keyof typeof apiMap];
	if (!config) return;
	totalMapName.value = config.name;
	if (val === 1) {
		trendChart.value.startDate = dayjs(query.value.endDate).subtract(1, 'month').format('YYYY-MM-DD');
		trendChart.value.endDate = query.value.endDate;
	} else {
		trendChart.value.startDate = dayjs(trendChart.value.startDate).format('YYYY-MM-DD');
		trendChart.value.endDate = dayjs(trendChart.value.endDate).format('YYYY-MM-DD');
	}
	const params = {
		...query.value,
		startDate: trendChart.value.startDate,
		endDate: trendChart.value.endDate,
		shopIdList: trendChart.value.shopIdList,
		status: trendChart.value.status,
		platformShopId: trendChart.value.platformShopId,
		type: trendChart.value.type,
	};
	const res = await config.api(params);
	analysisData.value = res;
	sumChart.value?.reSetChart(analysisData.value);
	totalMapVisible.value = true;
};

const onTotalMap = async (row: any) => {
	trendChart.value.shopIdList = [row.shopId];
	trendChart.value.type = row.type ? row.type : '';
	trendChart.value.platformShopId = row.platformShopId;
	onTrendChartMethod(1);
};

const getList = () => {
	const tableRefs = {
		货款余额: table,
		推广账户: table1,
		保证金账户: table2,
	};
	const currentTable = tableRefs[radioValue.value as keyof typeof tableRefs];
	currentTable.value.clearSelection();
	currentTable.value.query.currentPage = 1;
	currentTable.value.getList();
	checkboxList.value = [];
};
const BACKUP_FIELDS = ['endBalance', 'goodsIncome', 'fee', 'totalIncome', 'totalOutcome'] as const;

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.statusVerify = false;
		// 创建备份
		BACKUP_FIELDS.forEach((field) => {
			item[`${field}_Backup`] = item[field];
		});
	});
	tableData.value = data.data.list;
	shopWithDraw.value = data.data.extData.shopWithDraw ? data.data.extData.shopWithDraw : '';
	backupTableData.value = JSON.parse(JSON.stringify(data.data.list));
	callback(data);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'date', title: '日期', width: '85', formatter: 'formatDate' },
	{ sortable: true, field: 'rpaDate', title: '抓取时间', width: '135' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID', width: '126' },
	{ sortable: true, field: 'shopId', title: '店铺ID', width: '76' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '180' },
	{ sortable: true, field: 'type', title: '类型', width: '80' },
	{ summaryEvent: true, sortable: true, field: 'totalAmount', title: '总金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'openingBalance', title: '期初余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'endBalance', title: '期末余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'goodsIncome', title: '货款收入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	// { summaryEvent: true,sortable: true, field: 'realTimeBalance', title: '实时余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'promotionalFeeRecharge', title: '推广费充值', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'withDrawAmount', title: '提现', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'fee', title: '扣点', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalBalance', title: '总余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'availableBalance', title: '可用余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'frozenAmount', title: '冻结金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	// { summaryEvent: true,sortable: true, field: 'totalFundBalance', title: '总资金金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	// { summaryEvent: true,sortable: true, field: 'couponBalance', title: '优惠券金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalIncome', title: '总收入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalOutcome', title: '总支出', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'oldDepositAmount', title: '昨日保证金余额', width: '150', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'depositAmount', title: '保证金余额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'depositOutcome', title: '保证金支出', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'depositIncome', title: '保证金收入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'depositAmountDifference', title: '差额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'netIncomeOutcome', title: '净收支', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'netCashFlow', title: '净现金流入', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'checkStatus', title: '验算', width: '70' },
	{ sortable: true, field: 'status', title: '状态', width: '70' },
	{
		align: 'center',
		type: 'btnList',
		width: '130',
		fixed: 'right',
		field:'20250608095212',
		btnList: [
			{ title: '趋势图', handle: onTotalMap },
			{ title: '编辑', handle: handleEdit, isDisabled: (row) => (radioValue.value === '货款余额' ? row.statusVerify : true), permissions: 'FinancialFundsEditor' },
			{ title: '保存', handle: handleSave, isDisabled: (row) => (radioValue.value === '货款余额' ? !row.statusVerify : true), permissions: 'FinancialFundsEditor' },
			{ title: '取消', handle: handleCancel, isDisabled: (row) => (radioValue.value === '货款余额' ? !row.statusVerify : true), permissions: 'FinancialFundsEditor' },
		],
	},
]);

const fetchShopList = async () => {
	const params = {
		currentPage: 1,
		pageSize: 10000000,
		platform: 8,
	};
	query.value.shopIdList = [];
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		query.value.startDate = timeRange.value;
		query.value.endDate = timeRange.value;
	}
	fetchShopList();
});
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 65px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.container-relative {
	position: relative;
	width: 100%;
	height: 100%;
}

.radio-group-absolute {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	display: flex;
	align-items: center;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
.shop-text {
	color: red;
	font-size: 12px;
	margin-left: 5px;
	max-width: 900px;
	min-width: 100px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
}
</style>
