<template>
	<div style="height: 100%; width: 100%">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:date="query.queryDate" class="publicCss" style="width: 200px" type="date" :clearable="false" />
					<el-select v-model="query.accountList" placeholder="别名" style="width: 200px" class="publicCss" filterable clearable multiple collapse-tags>
						<el-option v-for="item in nameData" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.cardNatureList" placeholder="卡号性质" style="width: 200px" class="publicCss" filterable clearable multiple collapse-tags>
						<el-option label="个人" value="个人" />
						<el-option label="基本户" value="基本户" />
						<el-option label="一般户" value="一般户" />
					</el-select>
					<div class="pb5" style="display: flex">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-dropdown @command="computedProps" style="margin: 0 12px">
							<el-button type="primary"
								>计算<i class="el-dropdown-link"></i> <el-icon class="el-icon--right"> <arrow-down /> </el-icon
							></el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item command="true">全量</el-dropdown-item>
									<el-dropdown-item command="false">部分</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<el-button @click="forcedCalculations" type="primary">强制计算</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
						<div style="margin-left: 15px; display: flex; align-items: center; color: red; font-size: 15px; font-weight: bold">
							<span>计算完成时间：</span>
							<span v-if="matchTime ? true : false">{{ matchTime }}</span>
							<span v-else>暂未计算</span>
						</div>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable
					ref="table"
					id="2024091509395555"
					showsummary
					:tableCols="tableCols"
					isNeedDisposeProps
					@disposeProps="disposeProps"
					:pageSize="1000"
					:pageSizes="[1000]"
					:query="query"
					:query-api="QueryTotalReport"
					:isNeedPager="false"
				>
				</vxetable>
			</template>
		</Container>

		<el-dialog v-model="state.dialogVisible" draggable width="400px" title="编辑">
			<!-- <div class="w-e-text-container" style="display: flex; flex-direction: row;">
                上日余额: 
                <el-input-number v-model="submitObj.oldBalance" :min="-********" :max="*********" :controls="false" :precision="1" placeholder="上日余额" />
            </div> -->

			<el-form ref="formRef" style="max-width: 600px" :model="submitObj" label-width="auto" class="demo-ruleForm">
				<el-form-item label="上日余额:" prop="onlineBankId">
					<el-input-number v-model="submitObj.oldBalance" :min="-********" :max="*********" :controls="false" :precision="2" placeholder="上日余额" />
				</el-form-item>
				<el-form-item label="图片">
					<uploadMf v-model:imagesStr="submitObj.imgs" :upstyle="{ height: 40, width: 40 }" :limit="3" ref="refuploadMf" />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="" @click="state.dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="submitfuc">确认</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineAsyncComponent, reactive } from 'vue';
import { QueryTotalReport, ComputeFee, ForceComputeFee, QueryMatchTime, UpdateTotalReport, ExportTotalReport, PartComputeFee } from '/@/api/cwManager/totalReport';
import { ElMessageBox } from 'element-plus';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));

const nameData = ref<Public.options[]>([]);
const submitObj = ref({ oldBalance: 0, imgs: [], id: [] });

const state = reactive({
	dialogVisible: false,
	content: '',
});

const exportProps = async () => {
	await ExportTotalReport({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const handleEdit = (row: any) => {
	submitObj.value.oldBalance = row.oldBalance;
	submitObj.value.id = row.id ? row.id : '';
	submitObj.value.imgs = row.imgs ? row.imgs.split(',') : [];
	state.dialogVisible = true;
};

const submitfuc = async () => {
	const { success } = await UpdateTotalReport({ ...submitObj.value, imgs: submitObj.value.imgs.join(',') });
	if (success) {
		window.$message.success('编辑成功');
		state.dialogVisible = false;
		getList();
	}
};

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'time', title: '日期', formatter: 'formatDate' },
	{ sortable: true, field: 'name', title: '别名' },
	{ sortable: true, field: 'cardNature', title: '卡号性质' },
	{ sortable: true, field: 'oldBalance', title: '上日余额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'inAmount', title: '本日收入', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '本日支出', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'balance', title: '本日余额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'verify', title: '验算', formatter: 'fmtAmt2', align: 'right' },
	{ field: 'imgs', title: '图片', type: 'image', width: '100' },
	{
		title: '操作',
		align: 'center',
		field: '**************',
		type: 'btnList',
		btnList: [{ title: '编辑', handle: handleEdit }],
	},
]);
const table = ref();
const matchTime = ref('');
const query = ref({
	//昨天
	queryDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	accountList: [],
	cardNatureList: [],
});

const getList = () => {
	table.value.query.currentPage = 1;
	startChecking();
	table.value.getList();
};
const forcedCalculations = async () => {
	ElMessageBox.confirm('本操作会清除所有已匹配数据,是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			const { success } = await ForceComputeFee({ ...query.value, ...table.value.query });
			if (success) {
				window.$message.success('强制计算成功');
				startChecking();
			}
		} catch (error) {
			window.$message.error('强制计算失败');
		}
	});
};
const computedProps = async (val: any) => {
	try {
		let response;
		if (val == 'true') {
			response = await ComputeFee({ ...query.value, ...table.value.query });
			if (response.success) {
				window.$message.success('正在全量计算中...');
				getList();
			}
		} else if (val == 'false') {
			response = await PartComputeFee({ ...query.value, ...table.value.query });
			if (response.success) {
				window.$message.success('正在部分计算中...');
				getList();
			}
		}
	} catch (error) {
		window.$message.error('计算失败');
	}
};
const startChecking = () => {
	init();
	// const timer = setInterval(async () => {
	// 	await init();
	// 	if (matchTime.value) {
	// 		clearInterval(timer);
	// 	}
	// }, 10000);
};
const init = async () => {
	const res = await QueryMatchTime({ queryDate: query.value.queryDate });
	if (res.list && res.list.length > 0) {
		matchTime.value = res.list[0].matchTime;
	} else {
		matchTime.value = '';
	}
};
onMounted(async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameData.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
	startChecking();
});
const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.oldBalance = item.oldBalance ? item.oldBalance : 0;
		item.inAmount = item.inAmount ? item.inAmount : 0;
		item.outAmount = item.outAmount ? item.outAmount : 0;
		item.balance = item.balance ? item.balance : 0;
	});
	callback(data);
};
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 80px;
}
</style>
