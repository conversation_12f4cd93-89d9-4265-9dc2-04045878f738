<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-select v-model="query.dateType" placeholder="时间类型" :clearable="false" style="width: 95px; margin: 0 0 5px 0">
					<el-option label="退款时间" value="退款时间" />
					<el-option label="完成审批时间" value="完成审批时间" />
					<el-option label="发起时间" value="发起时间" />
					<el-option label="审批时间" value="审批时间" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable filterable>
					<el-option label="待审核" value="待审核" />
					<el-option label="已审核" value="已审核" />
				</el-select>
				<el-input v-model.trim="query.businessId" placeholder="审批编号" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.brandId" placeholder="采购员" class="publicCss" clearable filterable>
					<el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key" />
				</el-select>
				<el-input v-model.trim="query.buyNo" placeholder="采购单号" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.collectUser" placeholder="收款人" class="publicCss" clearable filterable>
					<el-option v-for="item in bankList" :key="item.id" :label="item.label" :value="item.label" />
				</el-select>
				<el-input v-model.trim="query.collectAccount" placeholder="收款账号" class="publicCss" clearable maxlength="50" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="otherOutbound202506041640"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetPurchaseReturnOutWareList"
				:export-api="ExportPurchaseReturnOutWareList"
				:treeConfig="{ transform: true, rowField: 'id', parentField: 'pId' }"
				isNeedCheckBox
				@select="checkboxChange"
				:asyncExport="{ title: '其他退货出库导出数据', isAsync: true }"
			>
				<template #rightCols>
					<vxe-column title="操作" width="140" fixed="right" align="center" field="operation">
						<template #default="{ row }">
							<div v-if="row.pId == 0" class="table-action-buttons">
								<el-button type="text" @click="onEdit(row)" :disabled="row.status == '已审核'">编辑</el-button>
								<el-button type="text" @click="onAudit(row, '审核')" :disabled="row.status == '已审核'">审核</el-button>
								<el-button type="text" @click="onAudit(row, '取消审核')" :disabled="row.status == '待审核'">取消审核</el-button>
							</div>
						</template>
					</vxe-column>
				</template>
			</vxetable>
			<el-dialog v-model="dialogInfo.visible" :title="dialogInfo.title" width="50%" draggable overflow style="margin-top: -10vh !important">
				<outboundEdit v-if="dialogInfo.visible" :data="dialogInfo.data" :bankList="bankList" @closeSuccess="closeSuccess" @close="dialogInfo.visible = false" />
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import axios from 'axios';
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetAllBianMaBrandAsync } from '/@/api/inventory/warehouse';
import { GetPurchaseReturnOutWareList, ExportPurchaseReturnOutWareList, ChangePurchaseReturnOutWareStatus } from '/@/api/cwManager/purchaseReturnOutWare';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const outboundEdit = defineAsyncComponent(() => import('/@/views/financial/otherReturnShipment/components/outboundEdit.vue'));
const bankList = ref<{ label: string; value: string; id: string; bankType: string }[]>([]);
const query = ref({
	startTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endTime: dayjs().format('YYYY-MM-DD'),
	brandId: '',
	collectAccount: '',
	dateType: '完成审批时间',
	collectUser: '',
	status: '',
	businessId: '',
	buyNo: '',
});
const table = ref();
const checkboxList = ref([]);
const brandList = ref<{ key: string; value: string }[]>([]); // 采购组
const dialogInfo = ref({
	visible: false,
	title: '',
	data: {
		businessId: '',
		brandName: '',
		supplier: '',
		buyNo: '',
		refundChangeGoodsAmount: '',
		shippingFee: '',
		isRefundChangeGoods: '',
		refundAmountTime: '',
		dtls: [],
		instanceId: '',
	},
});

const onAudit = (row: any, type: string) => {
	ElMessageBox.confirm('是否确认' + type + '?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(() => {
		ChangePurchaseReturnOutWareStatus({
			instanceIds: [row.instanceId],
			status: type == '审核' ? '已审核' : '待审核',
		}).then((res) => {
			if (res.success) {
				ElMessage.success('操作成功');
				table.value.refreshTable(true);
			}
		});
	});
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const onEdit = (row: any) => {
	dialogInfo.value.visible = true;
	dialogInfo.value.title = '编辑';
	dialogInfo.value.data = row;
};

const closeSuccess = (success: boolean) => {
	if (success) {
		table.value.refreshTable(true);
		dialogInfo.value.visible = false;
	}
};

const onParent = (row: any, field: string) => {
	const childOnlyFields = ['collectAccount', 'collectUser', 'collectType'];
	if (childOnlyFields.includes(field)) {
		return row.pId != 0 ? row[field] : '';
	}
	if (row.pId == 0) {
		if (field == 'isRefundChangeGoods') {
			return row[field] == 1 ? '是' : '否';
		}
		if (field == 'shippingFee' || field == 'refundChangeGoodsAmount') {
			return row[field] ? row[field].toFixed(2) : 0;
		}
		return row[field];
	}
	return '';
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'status', title: '状态', width: '100', align: 'center', fixed: 'left', treeNode: true },
	{ sortable: true, field: 'businessId', title: '审批编号', width: '160', align: 'center' },
	{ sortable: true, field: 'brandName', title: '采购员', width: '100', align: 'center' },
	{ sortable: true, field: 'handlingMethodReason', title: '退货事由', width: '130', align: 'center' },
	{ sortable: true, field: 'supplier', title: '供应商名称', width: '100', align: 'center' },
	{ sortable: true, field: 'buyNo', title: '采购单号', width: '100', align: 'center', formatter: (row: any) => onParent(row, 'buyNo') },
	{ sortable: true, field: 'handlingMethod', title: '退货处理方式', width: '130', align: 'center', formatter: (row: any) => onParent(row, 'handlingMethod') },
	{ sortable: true, field: 'totalRefundAmount', title: '厂家实际退款总金额', width: '100', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'shippingFee', title: '运费', width: '100', align: 'right', formatter: (row: any) => onParent(row, 'shippingFee') },
	{ sortable: true, field: 'isRefundChangeGoods', title: '是否退换货', width: '100', align: 'center', formatter: (row: any) => onParent(row, 'isRefundChangeGoods') },
	{ sortable: true, field: 'refundChangeGoodsAmount', title: '退换货金额', width: '100', align: 'right', formatter: (row: any) => onParent(row, 'refundChangeGoodsAmount') },
	{ sortable: true, field: 'refundAmountTime', title: '退款时间', width: '100', align: 'center', formatter: 'formatDate' },
	{ sortable: true, field: 'collectType', title: '收款方式', width: '100', align: 'center', formatter: (row: any) => onParent(row, 'collectType') },
	{ sortable: true, field: 'collectUser', title: '收款人', width: '160', align: 'center', formatter: (row: any) => onParent(row, 'collectUser') },
	{ sortable: true, field: 'collectAccount', title: '收款账号', width: '150', align: 'center', formatter: (row: any) => onParent(row, 'collectAccount') },
	{ sortable: true, field: 'initiationTime', title: '发起时间', width: '131', align: 'center' },
	{ sortable: true, field: 'approvalTime', title: '完成审批时间', width: '131', align: 'center' },
	{ sortable: true, field: 'cwApprovalUserName', title: '审批人', width: '100', align: 'center' },
	{ sortable: true, field: 'cwApprovalTime', title: '审批时间', width: '131', align: 'center' },
]);
onMounted(async () => {
	GetAllBianMaBrandAsync({}).then((res) => {
		if (!res?.success) return;
		brandList.value = res.data;
	});
	const { data, success } = await QueryOnlineBankSet({ currentPage: 1, pageSize: 100000 });
	if (success) {
		bankList.value = data.list.map((item: any) => ({
			label: item.accountName,
			value: item.account,
			id: item.id,
			bankType: item.bankType,
		}));
	}
});
</script>

<style scoped lang="scss">
::v-deep .table-action-buttons .el-button + .el-button {
	margin-left: 2px;
}
::v-deep .table-action-buttons .el-button--small {
	padding: 2px 5px;
}

.table-action-buttons {
	display: flex;
	justify-content: center;
}
</style>
