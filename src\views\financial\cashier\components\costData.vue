<template>
	<div style="width: 100%; height: 100%">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="classify"
						startPlaceholder="交易开始日期" endPlaceholder="交易结束日期" style="width: 230px" />
					<el-input v-model="query.businessId" style="width: 160px" placeholder="钉钉流程号" class="classify"
						clearable maxlength="50" />
					<el-input v-model.trim="query.account" class="classify" placeholder="卡号" clearable maxlength="50"
						style="width: 160px" />
					<!-- <el-select v-model="query.feeTypeList" placeholder="收支类型" class="classify" filterable clearable multiple collapse-tags>
						<el-option key="未知" label="未知" value="未知" />
						<el-option v-for="item in expenseType" :key="item.value" :label="item.label" :value="item.value" />
					</el-select> -->
					<div style="height: 35px; margin-top: 7px">
						<el-cascader v-model="query.feeTypeList" :options="optionsExpense" :props="propsExpense"
							placeholder="请选择收支类型" class="publicCss custom-cascader" style="width: 180px" collapse-tags
							collapse-tags-tooltip clearable />
					</div>
					<el-select v-model="query.applyFeeTypeList" placeholder="费用分类" class="publicCss"
						style="width: 160px" filterable clearable multiple collapse-tags collapse-tags-tooltip>
						<el-option v-for="item in expensesList" :key="item" :label="item" :value="item" />
					</el-select>
					<el-select v-model="query.accountName" placeholder="姓名/账号名" class="classify" filterable clearable
						style="width: 160px">
						<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.deptList" placeholder="发起人" class="classify" style="width: 150px"
						filterable clearable multiple collapse-tags :filter-method="outTruckinput">
						<el-option v-for="(item, i) in initiatorList" :key="`${item.value}-${i}`" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.amountType" placeholder="金额类型" clearable style="width: 85px; margin: 0 0 5px 0">
						<el-option label="原始金额" value="修正前" />
						<el-option label="金额" value="修正后" />
					</el-select>
					<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2"
						style="width: 170px; margin: 0 5px 5px 0" minPlaceHolder="≥金额(元)" maxPlaceHolder="<金额(元)" />
					<el-select v-model="query.statusList" placeholder="状态" class="classify" clearable multiple
						collapse-tags style="width: 160px">
						<el-option label="已匹配" value="是" />
						<el-option label="未匹配" value="否" />
						<el-option label="已忽略" value="忽略" />
						<el-option label="已拆分" value="已拆分" />
					</el-select>
					<el-input v-model="query.title" style="width: 160px" placeholder="流程名字" class="classify" clearable
						maxlength="100" />
					<el-select v-model="query.approverStatus" placeholder="审批状态" clearable class="publicCss">
						<el-option label="待审核" value="待审核" />
						<el-option label="已审核" value="已审核" />
					</el-select>
					<!-- <yhUserSelect style="width: 220px" v-model:value="query.approveDDUserId" class="classify" v-model:label="query.approveUserName" /> -->
					<!-- <yhdeptSelect style="width: 220px" v-model:value="query.approveDept" class="classify" /> -->
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-button @click="onResetMethod" type="primary">重置</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="**************"
					:treeConfig="{ transform: true, rowField: 'instanceId', parentField: 'pId' }" :tableCols="tableCols"
					:query="query" showsummary :query-api="QueryFeeData" />
			</template>
		</Container>

		<el-dialog v-model="editDialog" :title="'查看'" width="1000" draggable overflow border>
			<div style="height: 350px">
				<div style="display: flex; justify-content: space-between; margin-bottom: 15px">
					<div>
						<span>姓名/账户名:</span>
						{{ statusForm.payBankAccountName }}
					</div>
					<div>
						<span>卡号:</span>
						{{ statusForm.payBankAccount }}
					</div>
					<div>
						<span>金额(元):</span>
						{{ statusForm.amount }}
					</div>
				</div>
				<el-table :data="statusTableData" style="width: 100%" height="300">
					<el-table-column prop="account" label="银行卡号" show-overflow-tooltip />
					<el-table-column prop="bankType" label="银行" width="150" show-overflow-tooltip />
					<el-table-column prop="inAmount" label="收入金额(元)" width="110" show-overflow-tooltip />
					<el-table-column prop="outAmount" label="支出金额(元)" width="110" show-overflow-tooltip />
					<el-table-column prop="toAccount" label="对方卡号" width="150" show-overflow-tooltip />
					<el-table-column prop="toAccountName" label="对方用户名" width="110" show-overflow-tooltip />
					<el-table-column prop="occurenceTime" label="付款时间" width="150" show-overflow-tooltip />
				</el-table>
			</div>
			<div style="display: flex; justify-content: center">
				<el-button style="width: 80px" @click="editDialog = false">取消</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="editVisible" title="编辑" width="80%" draggable overflow :close-on-click-modal="false">
			<div style="display: flex; gap: 10px">
				<dataRange v-model:startDate="editQuery.startOccurenceTime" v-model:endDate="editQuery.endOccurenceTime"
					:clearable="false" class="publicCss" style="width: 300px" />
				<el-input v-model.trim="editQuery.account" class="publicCss" placeholder="账号" clearable maxlength="50"
					style="width: 150px; margin-bottom: 5px" />
				<!-- <el-select v-model="editQuery.area" placeholder="归属区域" class="publicCss" filterable clearable style="width: 150px">
					<el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select> -->
				<el-select v-model="editQuery.bankType" placeholder="行名" class="publicCss" filterable clearable
					style="width: 150px">
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div style="display: flex; gap: 0">
					<el-select v-model="editQuery.flowType" placeholder="交易类型" clearable
						style="width: 90px; margin: 0 0 5px 0">
						<el-option label="收入" value="收入" />
						<el-option label="支出" value="支出" />
					</el-select>
					<numRange v-model:maxNum="editQuery.maxAmount" v-model:minNum="editQuery.minAmount" :precision="2"
						style="width: 200px; margin: 0 10px 5px 0" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				</div>
				<el-select v-model="editQuery.status" placeholder="状态" class="publicCss" clearable style="width: 150px">
					<el-option label="已匹配" value="是" />
					<el-option label="未匹配" value="否" />
					<el-option label="已忽略" value="忽略" />
				</el-select>
				<!-- <numRange
					v-model:maxNum="editQuery.maxInAmount"
					v-model:minNum="editQuery.minInAmount"
					:precision="2"
					class="publicCss"
					style="width: 200px"
					minPlaceHolder="收入金额最小值"
					maxPlaceHolder="收入金额最大值"
				/>
				<numRange
					v-model:maxNum="editQuery.maxOutAmount"
					v-model:minNum="editQuery.minOutAmount"
					:precision="2"
					class="publicCss"
					style="width: 200px"
					minPlaceHolder="支出金额最小值"
					maxPlaceHolder="支出金额最大值"
				/> -->
				<el-button type="primary" @click="onInquire" v-reclick>查询</el-button>
			</div>
			<div style="height: 600px; width: 100%">
				<vxetable ref="table1" id="************" :tableCols="editTableCols" v-if="editVisible" isNeedCheckBox
					@select="checkboxChange" :query="editQuery" :pageSize="50" showsummary :isDisableCheckBox="false"
					:query-api="QueryBankFlow" />
			</div>
			<div style="display: flex; justify-content: center; margin-top: 20px">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="editSubmit" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-drawer v-model="ProcessApprovalsDrawer" title="查看流程">
			<approvalProcess :viewInfo="viewInfo" v-if="ProcessApprovalsDrawer" @close="ProcessApprovalsDrawer = false"
				@getList="getList" />
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import dayjs from 'dayjs';
import { QueryBankFlow } from '/@/api/cwManager/bankFlow';
import { ExportFeeData } from '/@/api/cwManager/feeData';
import {
	QueryFeeData,
	QueryBankFlowMatch,
	RelieveFeeData,
	IgnoreFeeData,
	UnIgnoreFeeData,
	UpDateFeeData,
	ApproveFeeData,
	QueryFeeDataApplyFeeType,
	QueryFeeDataDeptList,
} from '/@/api/cwManager/feeData';
import { AllDeptViewList } from '/@/api/admin/deptuser';
import { bankList, areaList } from '/@/utils/tools';
import Decimal from 'decimal.js';
import { ElMessageBox } from 'element-plus';
import { QueryOnlineBankSet, QueryCashierFeeType, QueryCashierFeeTypeCasCade } from '/@/api/cwManager/cashierSet';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const yhdeptSelect = defineAsyncComponent(() => import('/@/components/yhCom/deptSelect.vue'));
const approvalProcess = defineAsyncComponent(() => import('/@/components/yhCom/approvalProcess.vue'));
const editDialog = ref(false);
const editVisible = ref(false); //编辑弹窗
const statusVerify = ref(false);
const ProcessApprovalsDrawer = ref(false);
const statusTableData = ref([]);
const optionsExpense = ref([]);
const propsExpense = ref({
	multiple: true,
	emitPath: false,
});
const expenseType = ref<Public.options[]>([]);
const nameList = ref<Public.options[]>([]);
interface CheckboxItem {
	id: string;
}
const checkboxList = ref<CheckboxItem[]>([]);
const expensesList = ref<Public.options[]>([]);
const initiatorList = ref<Public.options[]>([]);
const originalList = ref<Public.options[]>([]);
const editInfo = ref({
	businessId: '', //钉钉流程号
	area: '', //归属区域
	type: '', //交易类型
	feeType: '', //收支类型
	bankRemark: '', //备注
	oldBusinessId: '', //原钉钉流程号
	instanceId: '',
});
const statusForm = ref({
	payBankAccount: '',
	payBankAccountName: '',
	amount: '',
});
const editQuery = ref({
	status: '否',
	startOccurenceTime: '',
	endOccurenceTime: '',
	maxOutAmount: undefined,
	minOutAmount: undefined,
	maxInAmount: undefined,
	minInAmount: undefined,
	area: '',
	bankType: '',
	account: '',
	flowType: '',
	maxAmount: undefined,
	minAmount: undefined,
});

const viewInfo = ref({
	instanceId: '',
	accountName: '',
	status: '',
});

const loading = ref(false);
// 导出
const exportProps = async () => {
	loading.value = true;
	await ExportFeeData({ ...query.value })
		.then((data: any) => {
			if (!data.success) {
				return;
			}
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
		})
		.catch(() => {
			loading.value = false;
		});
};
const rechargeRecord = async (row: any) => {
	statusTableData.value = [];
	if (row.relationBankFlowRecordId) {
		const { data, success } = await QueryBankFlowMatch({ relationBankFlowRecordId: row.relationBankFlowRecordId });
		if (!success) return;
		statusTableData.value = data.list;
		statusForm.value = {
			payBankAccountName: row.payBankAccountName,
			payBankAccount: row.payBankAccount,
			amount: row.amount ? row.amount : row.amount,
		};
		editDialog.value = true;
	}
};

const viewProcess = (row: any) => {
	viewInfo.value = {
		instanceId: row.instanceId,
		accountName: row.payAccount,
		status: row.status,
	};
	ProcessApprovalsDrawer.value = true;
};

const outTruckinput = (e: any) => {
	// 自定义查询方法
	let list = ref();
	list.value = originalList.value;
	if (e) {
		initiatorList.value = list.value.filter((item: any) => {
			return item.label ? item.label.indexOf(e) > -1 : false;
		});
	} else {
		initiatorList.value = [...originalList.value];
	}
};

const onResetMethod = () => {
	query.value.statusList = [];
	query.value.approveDDUserId = '';
	query.value.approveUserName = '';
	query.value.approveDept = '';
	query.value.businessId = '';
	query.value.maxAmount = undefined;
	query.value.minAmount = undefined;
	query.value.account = '';
	query.value.feeTypeList = [];
	query.value.applyFeeTypeList = [];
	query.value.accountName = '';
	query.value.title = '';
	query.value.deptList = [];
	query.value.approverStatus = '';
	getList();
};

const handleEdit = (row: any) => {
	editInfo.value = JSON.parse(JSON.stringify(row));
	editInfo.value.oldBusinessId = row.businessId;
	editInfo.value.instanceId = row.instanceId;
	editQuery.value.startOccurenceTime = '';
	editQuery.value.endOccurenceTime = '';
	if (row.payTime) {
		editQuery.value.startOccurenceTime = dayjs(row.payTime).format('YYYY-MM-DD');
		editQuery.value.endOccurenceTime = dayjs(row.payTime).format('YYYY-MM-DD');
	} else {
		editQuery.value.startOccurenceTime = dayjs().format('YYYY-MM-DD');
		editQuery.value.endOccurenceTime = dayjs().format('YYYY-MM-DD');
	}
	editQuery.value.area = '';
	editQuery.value.bankType = '';
	editQuery.value.maxInAmount = undefined;
	editQuery.value.minInAmount = undefined;
	editQuery.value.maxOutAmount = undefined;
	editQuery.value.minOutAmount = undefined;
	checkboxList.value = [];
	editQuery.value.account = row.payBankAccount ? row.payBankAccount : '';
	if (row.amount) {
		editQuery.value.maxAmount = row.amount;
		editQuery.value.minAmount = row.amount;
	} else {
		editQuery.value.maxAmount = undefined;
		editQuery.value.minAmount = undefined;
	}
	editVisible.value = true;
};

const handApprovalMethod = async (row: any) => {
	ElMessageBox.confirm('是否审批此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await ApproveFeeData({ instanceId: row.instanceId });
			if (success) {
				window.$message.success('审批成功');
				getList();
			}
		})
		.catch(() => { });
};

const handleDelete = async (row: any, val: number) => {
	ElMessageBox.confirm('此操作将忽略此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			if (val == 1) {
				const { success } = await IgnoreFeeData({ instanceId: row.instanceId });
				if (success) {
					window.$message.success('忽略成功');
				}
			} else {
				const { success } = await UnIgnoreFeeData({ instanceId: row.instanceId });
				if (success) {
					window.$message.success('解除忽略成功');
				}
			}
			getList();
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const handleRelieve = async (row: any) => {
	ElMessageBox.confirm('是否解除此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await RelieveFeeData({ instanceId: row.instanceId });
			if (success) {
				window.$message.success('解除成功');
				getList();
			}
		})
		.catch(() => { });
};

//节流
const throttle = (func: (...args: any[]) => void, limit: number) => {
	let lastFunc: ReturnType<typeof setTimeout> | undefined;
	let lastRan: number | undefined;
	return function (...args: any[]) {
		if (lastRan === undefined) {
			func(...args);
			lastRan = Date.now();
		} else {
			clearTimeout(lastFunc);
			lastFunc = setTimeout(
				() => {
					if (Date.now() - (lastRan as number) >= limit) {
						func(...args);
						lastRan = Date.now();
					}
				},
				limit - (Date.now() - (lastRan as number))
			);
		}
	};
};

const checkboxChange = throttle(async (data: any, callback: Function) => {
	if (data.length == 0) {
		checkboxList.value = [];
		return;
	}
	// else if (data.length > 1) {
	// 	window.$message.error('只能选择一条数据');
	// }
	let a = table1.value.selectedSpreadsRowData()
	checkboxList.value = data.concat(a);
}, 1000);

const onInquire = async () => {
	if (editQuery.value.maxInAmount != null && editQuery.value.minInAmount != null && editQuery.value.maxInAmount < editQuery.value.minInAmount) {
		return window.$message.error('收入金额最大值不能小于最小值');
	}
	if (editQuery.value.maxOutAmount != null && editQuery.value.minOutAmount != null && editQuery.value.maxOutAmount < editQuery.value.minOutAmount) {
		return window.$message.error('支出金额最大值不能小于最小值');
	}
	table1.value.getList();
};

const editSubmit = async () => {
	if (checkboxList.value.length == 0) {
		return window.$message.error('请选择数据');
	}
	// else if (checkboxList.value.length > 1) {
	// 	return window.$message.error('只能选择一条数据');
	// }
	let newarr: string[] = [];
	checkboxList.value.map((item) => {
		newarr.push(item.id);
	});
	const { success } = await UpDateFeeData({ relationBankFlowRecordIds: newarr, instanceId: editInfo.value.instanceId });
	if (success) {
		window.$message.success('保存成功');
		editVisible.value = false;
		getList();
	}
};

const editTableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'relationArea', title: '归属区域' },
	{ sortable: true, field: 'relationFeeType', title: '收支类型', width: '90' },
	{ sortable: true, field: 'bankType', title: '行名', width: '80' },
	{ sortable: true, field: 'account', title: '账号', width: '80' },
	{ sortable: true, field: 'balance', title: '账户余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'occurenceTime', title: '交易时间', formatter: 'formatDate', width: '100' },
	{ sortable: true, field: 'inAmount', title: '收入余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '支出余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'toAccount', title: '对方账号' },
	{ sortable: true, field: 'toAccountName', title: '对方名称' },
	{ sortable: true, field: 'flowType', title: '交易类型', width: '90' },
	{ sortable: true, field: 'relationRemark', title: '交易备注' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : row.relationStatus == '已拆分' ? '已拆分' : ''),
	},
]);

const isDisabled1 = (row: any, val: number): boolean => {
	if (val != 1 && row.approverStatus === '已审核') {
		return true;
	}
	if (val == 1) {
		return row.relationStatus != '是' ? true : false;
	} else if (val == 2) {
		return row.relationStatus === '否' && row.pId == null && (row.clickPage == null || row.clickPage == 'FeeData') ? false : true;
	} else if (val == 3) {
		return row.relationStatus === '否' && (row.clickPage == null || row.clickPage == 'FeeData') && row.pId == null ? false : true;
	} else if (val === 4) {
		return (row.relationStatus === '是' || row.approverStatus === '待审核' || row.relationStatus === '已拆分') && row.pId == null && (row.clickPage == null || row.clickPage == 'FeeData')
			? false
			: true;
	} else if (val == 5) {
		return (row.relationStatus === '是' || row.relationStatus === '已拆分') && row.approverStatus === '待审核' && row.pId == null && (row.clickPage == null || row.clickPage == 'FeeData')
			? false
			: true;
	} else if (val == 6) {
		return row.relationStatus == '忽略' ? false : true;
	}
	return false;
};

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'processCode', title: '流程编号', color: (row: any) => (row.isRelation === false ? 'red' : 'black') },
	{ sortable: true, field: 'businessId', title: '钉钉流程号', treeNode: true, width: '175', type: 'click', handle: (row: any) => viewProcess(row), copy: true },
	{ sortable: true, field: 'title', title: '流程名', width: '90' },
	{ sortable: true, field: 'relationFeeType', title: '收支类型', width: '90' },
	{ sortable: true, field: 'indexNo', title: 'erp编码', width: '90' },
	{ sortable: true, field: 'processCreateUserName', title: '发起人姓名', width: '100' },
	{ sortable: true, field: 'deptName', title: '发起人部门', width: '100' },
	{ sortable: true, field: 'useDepartment', title: '使用部门', width: '90' },
	{ sortable: true, field: 'usePlatform', title: '使用平台', width: '90' },
	{ sortable: true, field: 'applyReason', title: '申请事由', width: '90' },
	{ sortable: true, field: 'applyFeeType', title: '费用分类', width: '90' },
	{ sortable: true, field: 'expressCom', title: '快递公司', width: '90' },
	{ sortable: true, field: 'warehouseClass', title: '仓库分类', width: '90' },
	{ sortable: true, field: 'qty', title: '数量', width: '90', align: 'right' },
	{ sortable: true, field: 'unitPrice', title: '单价', width: '90', align: 'right' },
	{ sortable: true, field: 'rawAmount', title: '原始金额', width: '90', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'amount', title: '金额(元)', width: '90', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'payType', title: '付款方式', width: '90' },
	{ sortable: true, field: 'payBankAccountName', title: '姓名/账户名', width: '110' },
	{ sortable: true, field: 'payBankAccount', title: '卡号', width: '100' },
	{ sortable: true, field: 'payTime', title: '支付日期', formatter: 'formatTime', width: '100' },
	{ sortable: true, field: 'skBankAccount', title: '收款账号', width: '90' },
	{ sortable: true, field: 'skBankUserName', title: '收款账户名', width: '100' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		align: 'center',
		type: 'click',
		handle: (row: any) => rechargeRecord(row),
		isDisabled: (row: any) => isDisabled1(row, 1),
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : row.relationStatus == '已拆分' ? '已拆分' : ''),
	},
	{ sortable: true, field: 'operatorUserName', title: '操作人', width: '90' },
	{ sortable: true, field: 'approverUserName', title: '审批人', width: '90' },
	{ sortable: true, field: 'approverStatus', title: '审批状态', width: '90' },
	{ sortable: true, field: 'processRemark', title: '流程备注', width: '100' },
	{ sortable: true, field: 'occurenceDate', title: '交易日期', width: '140' },
	{
		title: '操作',
		align: 'center',
		field:'*************',
		type: 'btnList',
		fixed: 'right',
		width: '180',
		btnList: [
			// { title: '关联', handle: handleEdit, isDisabled: (row: any) => row.isIgnore === true || row.relationStatus == '是' },
			// { title: '忽略', handle: handleDelete, isDisabled: (row: any) => row.isIgnore === true },
			// { title: '解除', handle: handleRelieve },

			{ title: '关联', handle: handleEdit, isDisabled: (row: any) => isDisabled1(row, 2) },
			{ title: '忽略', handle: (row) => handleDelete(row, 1), isDisabled: (row: any) => isDisabled1(row, 3) },
			{ title: '解除忽略', handle: (row) => handleDelete(row, 2), isDisabled: (row: any) => isDisabled1(row, 6) },
			{ title: '解除', handle: handleRelieve, isDisabled: (row: any) => isDisabled1(row, 4) },
			{ title: '审批', handle: handApprovalMethod, isDisabled: (row: any) => isDisabled1(row, 5), permissions: 'auditBtn' },
		],
	},
]);
const options = ref<Public.options[]>([]);
const table = ref();
const table1 = ref();
const query = ref({
	startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	statusList: [],
	approveDDUserId: '',
	approveUserName: '',
	approveDept: '',
	businessId: '',
	maxAmount: undefined,
	minAmount: undefined,
	account: '',
	feeTypeList: [],
	applyFeeTypeList: [],
	accountName: '',
	title: '',
	deptList: [],
	approverStatus: '',
	amountType: '修正后',
});
const getList = () => {
	if (query.value.maxAmount != null && query.value.minAmount != null && query.value.maxAmount < query.value.minAmount) {
		return window.$message.error('金额最大值不能小于最小值');
	}
	table.value.query.currentPage = 1;
	table.value.getList();
};
const getAllDept = async () => {
	const { data } = await AllDeptViewList();
	// options.value = data.map((item: any) => ({ label: item.deptName, value: item.deptId }));
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.accountName }));
	const { data: data2 } = await QueryCashierFeeType({ currentPage: 1, pageSize: ******** });
	expenseType.value = data2.list.map((item: any, index: number) => {
		return {
			label: item.name,
			value: item.name === null || item.name === undefined ? index + '未知' : item.name,
		};
	});

	const { data: data4, success: success4 } = await QueryFeeDataApplyFeeType({});
	if (!success4) return;
	expensesList.value = data4;

	const { data: data3, success: success3 } = await QueryCashierFeeTypeCasCade({});
	if (!success3) return;
	optionsExpense.value = data3 || [];

	const { data: data5, success: success5 } = await QueryFeeDataDeptList({});
	if (!success5) return;
	initiatorList.value = data5.map((item: any) => ({ label: item.deptName, value: item.deptId }));
	originalList.value = [...initiatorList.value];
};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 25px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 80px;
}

::v-deep .custom-cascader .el-input,
::v-deep .custom-cascader .el-input--small,
::v-deep .custom-cascader .el-input--suffix {
	height: 24px !important;
	line-height: 24px !important;
}

.classify {
	margin: 0 5px 5px 0;
	width: 110px;
}
</style>
