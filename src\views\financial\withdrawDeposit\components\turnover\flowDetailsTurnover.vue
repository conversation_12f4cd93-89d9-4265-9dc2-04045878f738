<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="时间类型" style="width: 85px; margin: 0 0 5px 0" clearable
					filterable>
					<el-option key="发生时间" label="发生时间" value="发生时间" />
					<el-option key="复核时间" label="复核时间" value="复核时间" />
					<el-option key="创建时间" label="创建时间" value="创建时间" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss"
					startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 220px" />
				<el-select v-model="query.account" placeholder="别名" clearable filterable class="publicCss"
					style="width: 130px">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.platformId" placeholder="平台" class="publicCss itemCss" clearable filterable>
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.showAccountName" placeholder="持卡人(模糊搜索别名)" class="publicCss"
					style="width: 120px" clearable maxlength="50" />
				<el-select v-model="query.operatorType" placeholder="业务类型" class="publicCss" style="width: 100px"
					clearable filterable>
					<el-option key="提现" label="提现" value="提现" />
					<el-option key="过账" label="过账" value="过账" />
					<el-option key="申请款" label="申请款" value="申请款" />
					<el-option key="期初" label="期初" value="期初" />
					<el-option key="调整" label="调整" value="调整" />
				</el-select>
				<el-select v-model="query.flowType" placeholder="收支类型" style="width: 85px; margin: 0 0 5px 0" clearable
					filterable>
					<el-option key="收入" label="收入" value="收入" />
					<el-option key="支出" label="支出" value="支出" />
				</el-select>
				<el-select v-model="query.verifyStatus" placeholder="核对状态" style="width: 85px; margin: 0 0 5px 0"
					clearable filterable>
					<el-option key="复核前" label="复核前" value="复核前" />
					<el-option key="复核后" label="复核后" value="复核后" />
				</el-select>
				<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2"
					class="publicCss" style="width: 160px" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				<el-input v-model.trim="query.shopName" class="publicCss" style="width: 120px" placeholder="店铺名称"
					clearable maxlength="50" />
				<el-input v-model.trim="query.platformShopId" class="publicCss" style="width: 120px" placeholder="店铺ID"
					clearable maxlength="50" />
				<el-select v-model="query.toAccount" placeholder="对方账户名" clearable filterable class="publicCss"
					style="width: 130px">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.toShowAccountName" placeholder="对方持卡人(模糊搜索对方账户名)" class="publicCss"
					style="width: 120px" clearable maxlength="50" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss" style="width: 85px" clearable
					filterable>
					<el-option key="待复核" label="待复核" value="待复核" />
					<el-option key="已复核" label="已复核" value="已复核" />
				</el-select>
				<el-input v-model.trim="query.operatorUserName" placeholder="创建人" class="publicCss" style="width: 120px"
					clearable maxlength="50" />
				<el-input v-model.trim="query.verifyUserName" placeholder="复核人" class="publicCss" style="width: 120px"
					clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" :pageSize="50" id="************" :pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols" showsummary :query="query" :isAsc="true" :orderBy="'occurrenceTime'"
				isNeedDisposeProps @disposeProps="disposeProps" :query-api="QueryWithDrawFlow">
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryWithDrawFlow, ExportWithDrawInfoTurnover } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { ElMessageBox, ElLoading } from 'element-plus';
import { platformlist, } from '/@/utils/tools';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const pageLoading = ref(false);
const nameList = ref<Public.options[]>([]);
const table = ref();
const query = ref({
	startTime: '',
	platformId: '',
	endTime: '',
	timeType: '发生时间',
	account: '',
	operator: '',
	toShowAccountName: '',
	showAccountName: '',
	verifyUserName: '',
	operatorUserName: '',
	payTimeStart: '',
	payTimeEnd: '',
	operatorUserId: '',
	minAmount: undefined,
	maxAmount: undefined,
	flowType: '',
	operatorType: '',
	shopName: '',
	platformShopId: '',
	status: '',
	toAccount: '',
	verifyStatus: '',
	verifyUserId: '',
});

const exportProps = async () => {
	await ExportWithDrawInfoTurnover({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'accountName', title: '别名', width: '150' },
	{ sortable: true, field: 'operatorType', title: '业务类型', width: '90' },
	{ sortable: true, field: 'flowType', title: '收支类型', width: '90' },
	{
		title: '收入金额',
		align: 'center',
		field:'**************',
		children: [
			{ sortable: true, field: 'unVerifyInAmount', title: '复核前', width: '90', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'verifyInAmount', title: '复核后', width: '90', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{
		title: '支出金额',
		align: 'center',
		field:'**************',
		children: [
			{ sortable: true, field: 'unVerifyOutAmount', title: '复核前', width: '90', formatter: 'fmtAmt2', align: 'right' },
			{ sortable: true, field: 'verifyOutAmount', title: '复核后', width: '90', formatter: 'fmtAmt2', align: 'right' },
		],
	},
	{ sortable: true, field: 'operatorAmount', title: '发生额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'balance', title: '余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'occurrenceTime', title: '发生时间', width: '135' },
	{ sortable: true, field: 'platform', title: '平台', width: '95', formatter: 'formatPlatform' },
	{ sortable: true, field: 'platformShopId', title: '店铺ID', width: '95' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '130' },
	{ sortable: true, field: 'toAccountName', title: '对方账户名(别名)', width: '150' },
	{ sortable: true, field: 'status', title: '状态', width: '80' },
	{ sortable: true, field: 'operatorUserName', title: '创建人', width: '80' },
	{ sortable: true, field: 'operatorTime', title: '创建时间', width: '135' },
	{ sortable: true, field: 'verifyUserName', title: '复核人', width: '80' },
	{ sortable: true, field: 'verifyTime', title: '复核时间', width: '135' },
]);
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
};
const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.imgs = item.imgs ? item.imgs.split(',') : [];
		item.withDrawDate = dayjs(item.withDrawDate).format('YYYY-MM-DD');
	});
	callback(data);
};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
</style>
