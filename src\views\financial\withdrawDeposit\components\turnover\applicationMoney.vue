<template>
	<div v-loading="pageLoading">
		<el-form :model="singleform" :rules="singlerules" style="padding-bottom: 10px" ref="ruleFormRef">
			<div style="padding-bottom: 20px">
				<el-form-item label="申请事由" :label-width="'90px'">
					<el-input v-model.trim="singleform.applyReason" placeholder="申请事由" clearable maxlength="50" style="width: 190px" />
				</el-form-item>
				<el-form-item label="金额(元)" :label-width="'90px'">
					{{ singleform.operatorAmount }}
				</el-form-item>
				<!-- <el-form-item label="账户名" :label-width="'90px'">
					{{ singleform.accountName }}
				</el-form-item> -->
				<el-form-item label="付款方式" :label-width="'90px'" prop="payType">
					<el-select v-model="singleform.payType" placeholder="付款方式" filterable clearable style="width: 190px">
						<el-option label="银行卡" value="银行卡" />
						<el-option label="支付宝" value="支付宝" />
						<el-option label="微信" value="微信" />
						<el-option label="现金" value="现金" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="网银别名" :label-width="'90px'" prop="account">
					<bankSelect v-model:value="singleform.account" placeholder="网银别名" clearable filterable style="width: 190px" />
				</el-form-item> -->
				<el-form-item label="转账操作人" :label-width="'90px'" prop="operatorUser">
					<el-select v-model="singleform.operatorUser" placeholder="转账操作人" filterable clearable style="width: 190px">
						<el-option label="王总" value="王总" />
						<el-option label="出纳" value="出纳" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="申请部门" :label-width="'90px'">
					<el-select v-model="singleform.applyDept" placeholder="申请部门" filterable clearable style="width: 190px">
						<el-option label="运营部" value="运营部" />
						<el-option label="老板组" value="老板组" />
					</el-select>
				</el-form-item> -->
				<el-form-item label="支付时间" :label-width="'90px'">
					<el-date-picker v-model="singleform.payTime" type="datetime" placeholder="支付时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY-MM-DD" time-format="HH:mm:ss" style="width: 190px" />
				</el-form-item>
			</div>
			<div class="table_top">
				<span>支付信息</span>
			</div>
			<el-table :data="singleform.payInfos" style="width: 100%; margin: 10px" height="200px">
				<el-table-column prop="accountName" label="账户名" width="140" :show-overflow-tooltip="true" />
				<el-table-column prop="account" label="账号" :show-overflow-tooltip="true" />
				<el-table-column prop="amount" label="金额(元)" width="100" :show-overflow-tooltip="true" />
				<el-table-column prop="toAccountName" label="对方账户名" width="140" :show-overflow-tooltip="true" />
			</el-table>
			<el-form-item label="备注" :label-width="'90px'">
				<el-input
					v-model="singleform.remark"
					placeholder="请输入"
					type="textarea"
					autocomplete="off"
					clearable
					style="width: 700px"
					maxlength="1000"
					:autosize="{ minRows: 4, maxRows: 4 }"
					resize="none"
				/>
			</el-form-item>
		</el-form>
		<div class="btnGroup">
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="submitForm(ruleFormRef)">发起</el-button>
		</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { GetPurchaseOrdersProcessAliOrderUrl, PassWaitPayPurchaseOrdersProcess, PassConfirmWaitPayPurchaseOrdersProcess } from '/@/api/cwManager/processPayOrApproved';
import { ElMessageBox, ElLoading } from 'element-plus';
import { CommitApplicationMoney } from '/@/api/cwManager/withDrawInfo';
import { decimal } from '/@/utils/decimal';
import dayjs from 'dayjs';
import type { FormInstance } from 'element-plus';
import { QueryOnlineBankSet, QueryOnlineBankSetSelect } from '/@/api/cwManager/cashierSet';
const bankSelect = defineAsyncComponent(() => import('/@/components/yhCom/bankSelect.vue'));
const props = defineProps({
	applicationData: { type: Array, default: () => [] },
});
const verifyTool = ref(false);
const emits = defineEmits(['close', 'getList', 'closingMethod']);
const singlerules = reactive({
	operatorUser: [{ required: true, message: '请选择转账操作人', trigger: 'change' }],
	payType: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
	account: [{ required: true, message: '请选择网银别名', trigger: 'change' }],
});
const nameList = ref<Public.options[]>([]);
const ruleFormRef = ref<FormInstance>();
const singleform = ref<{
	applyReason: string;
	operatorAmount: string | number;
	payInfos: any[]; // 修改为 any[] 类型
	payType: string;
	operatorUser: string;
	applyDept: string;
	remark: string;
	payTime: string;
	accountName: string;
	account: string;
}>({
	applyReason: '',
	operatorAmount: '',
	payInfos: [],
	payType: '',
	remark: '',
	payTime: '',
	operatorUser: '',
	applyDept: '',
	accountName: '',
	account: '',
});
const bankForm = ref<{
	onlineBankId: string | number;
	images: string[];
}>({
	onlineBankId: '',
	images: [],
});
const chooseBankVisible = ref(false);
const onlineBankId = ref();
const bankList = ref<Public.options[]>([]);
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const verify = ref(false);
const pageLoading = ref(false);
const remoteLoading = ref(false);
const handleClose = () => {
	ElMessageBox.confirm('确定关闭吗?')
		.then(() => {
			emits('closingMethod');
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};
const handleRecharge = async (row: any) => {
	// 重新修改表单数据
};
const handleBlur = async () => {
	// 重新计算总数
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (!valid) return;
		const { success } = await CommitApplicationMoney({
			applyReason: singleform.value.applyReason,
			operatorAmount: singleform.value.operatorAmount,
			payInfos: singleform.value.payInfos,
			payType: singleform.value.payType,
			operatorUser: singleform.value.operatorUser,
			applyDept: singleform.value.applyDept,
			remark: singleform.value.remark,
			payTime: singleform.value.payTime,
			accountName: singleform.value.accountName,
			account: singleform.value.account,
		});
		if (success) {
			window.$message.success('操作成功');
			emits('close');
		}
	});
};

const accountChange = (value: any, index: any, row: any) => {
	row.account = value;
};
const onAmountChange = (value: any, index: any, row: any) => {
	verifyTool.value = true;
	handleRecharge(row); // 重新修改表单数据
	handleBlur(); // 重新计算总数
};
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));
};

onMounted(() => {
	singleform.value.payInfos = JSON.parse(JSON.stringify(props.applicationData));
	const firstPayInfo = singleform.value.payInfos[0];
	singleform.value.accountName = firstPayInfo.accountName;
	singleform.value.payTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
	let operatorAmount = 0;
	singleform.value.payInfos.forEach((item: any) => {
		operatorAmount = Number(decimal(operatorAmount, item.verifyOutAmount, 2, '+'));
		item.account = item.account;
		item.accountName = item.accountName;
		item.amount = item.verifyOutAmount;
		item.toAccount = item.toAccount;
		item.toAccountName = item.toAccountName;
	});
	singleform.value.account = firstPayInfo.toAccount ? firstPayInfo.toAccount : '';
	console.log('singleform.value.payInfos', singleform.value.payInfos);
	singleform.value.operatorAmount = operatorAmount;
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	display: flex;
	justify-content: end;
	margin-top: 20px;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

.table_top {
	display: flex;
	justify-content: space-between;
}
</style>
