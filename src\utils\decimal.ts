import Decimal from 'decimal.js'
/**
 *
 * @param num1 第一个数
 * @param num2 第二个数
 * @param precision 保留几位小数
 * @param type 运算类型 + - * / % pow(幂运算)
 * @returns 返回计算结果
 */
//使用decimal.js进行加减乘除 前端计算规定小数点后保留四位小数
export const decimal = (
  num1: number = 0,
  num2: number = 0,
  precision: number = 0,
  type: string = '+'
): Number => {
  let result = new Decimal(num1)
  switch (type) {
    case '+': //加法
      result = result.plus(num2)
      break
    case '-': //减法
      result = result.minus(num2)
      break
    case '*': //乘法
      result = result.times(num2)
      break
    case '/': //除法
      result = result.div(num2)
      break
    case '%': //取余
      result = result.mod(num2)
      break
    case 'pow': //幂运算
      result = result.pow(num2)
      break
    case 'sqrt': //开方
      result = result.sqrt()
      break
  }
  return result.toDecimalPlaces(precision).toNumber() //保留四位小数,四位小数后面不四舍五入,直接截断
}
