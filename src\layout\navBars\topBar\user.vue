<template>
	<div class="layout-navbars-breadcrumb-user pr15" :style="{ flex: layoutUserFlexNum }">
		<el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onComponentSizeChange">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i class="iconfont icon-ziti" :title="$t('message.user.title0')"></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="large" :disabled="state.disabledSize === 'large'">{{ $t('message.user.dropdownLarge') }}</el-dropdown-item>
					<el-dropdown-item command="default" :disabled="state.disabledSize === 'default'">{{ $t('message.user.dropdownDefault') }}</el-dropdown-item>
					<el-dropdown-item command="small" :disabled="state.disabledSize === 'small'">{{ $t('message.user.dropdownSmall') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onLanguageChange">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i class="iconfont" :class="state.disabledI18n === 'en' ? 'icon-fuhao-yingwen' : 'icon-fuhao-zhongwen'" :title="$t('message.user.title1')"></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="zh-cn" :disabled="state.disabledI18n === 'zh-cn'">简体中文</el-dropdown-item>
					<el-dropdown-item command="en" :disabled="state.disabledI18n === 'en'">English</el-dropdown-item>
					<el-dropdown-item command="zh-tw" :disabled="state.disabledI18n === 'zh-tw'">繁體中文</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onSearchClick">
			<el-icon :title="$t('message.user.title2')">
				<ele-Search />
			</el-icon>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onLayoutSetingClick">
			<i class="icon-skin iconfont" :title="$t('message.user.title3')"></i>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon">
			<el-popover placement="bottom" trigger="hover" transition="el-zoom-in-top" :width="300" :persistent="false">
				<template #reference>
					<el-badge :is-dot="hasUnreadNotice">
						<el-icon :title="$t('message.user.title4')">
							<ele-Bell />
						</el-icon>
					</el-badge>
				</template>
				<UserNews :noticeList="state.noticeList" />
			</el-popover>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onScreenfullClick">
			<i class="iconfont" :title="state.isScreenfull ? $t('message.user.title6') : $t('message.user.title5')" :class="!state.isScreenfull ? 'icon-fullscreen' : 'icon-tuichuquanping'"></i>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon mr10" @click="onOnlineUserClick">
			<el-icon title="在线用户">
				<ele-User />
			</el-icon>
		</div>
		<el-dropdown :show-timeout="70" :hide-timeout="50" size="large" @command="onHandleCommandClick">
			<span class="layout-navbars-breadcrumb-user-link">
				<el-tooltip effect="dark" placement="left">
					<template #content>
						姓名：{{ userInfos.user.userName }}<br />
						职位：{{ userInfos.user.nickName }}<br />
					</template>
					<img :src="userInfos.user.avatar" class="layout-navbars-breadcrumb-user-link-photo mr5" />
				</el-tooltip>
				{{ userInfos.user.userName == '' ? userInfos.account : userInfos.user.userName }}
				<el-icon class="el-icon--right">
					<ele-ArrowDown />
				</el-icon>
			</span>
			<template #dropdown>
				<el-dropdown-menu>
					<!-- <el-dropdown-item command="/dashboard/home">{{ $t('message.user.dropdown1') }}</el-dropdown-item> -->
					<!-- <el-dropdown-item :icon="Avatar" command="/system/userCenter">{{ $t('message.user.dropdown2') }}</el-dropdown-item> -->
					<el-dropdown-item :icon="Loading" command="clearCache">{{ $t('message.user.dropdown3') }}</el-dropdown-item>
					<el-dropdown-item :icon="Bottom" command="downLoad">{{ $t('message.user.dropdown7') }}</el-dropdown-item>
					<el-dropdown-item :icon="CircleCloseFilled" divided command="logOut">{{ $t('message.user.dropdown5') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<Search ref="searchRef" />
		<OnlineUser ref="onlineUserRef" />
	</div>

	<el-dialog v-model="dialogVisible" title="下载管理" draggable>
		<vxetable style="height: 600px" ref="table" id="20240925141038" :tableCols="tableCols" order-by="modifiedTime" :is-asc="false" :query-api="queryPageTaskLog" />
	</el-dialog>
</template>

<script setup lang="ts" name="layoutBreadcrumbUser">
import { defineAsyncComponent, ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus';
import screenfull from 'screenfull';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import mittBus from '/@/utils/mitt';
import { Local, Session } from '/@/utils/storage';
import Push from 'push.js';
import { signalR } from '/@/views/system/onlineUser/signalR';
import { Avatar, CircleCloseFilled, Loading, Bottom } from '@element-plus/icons-vue';
import { queryPageTaskLog } from '/@/api/admin/exportLog';
import { getAPI } from '/@/utils/axios-utils';
import { SysAuthApi, SysNoticeApi } from '/@/api-services/api';
import { downLoadFile } from '/@/utils/tools';
// 引入组件
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const UserNews = defineAsyncComponent(() => import('/@/layout/navBars/topBar/userNews.vue'));
const Search = defineAsyncComponent(() => import('/@/layout/navBars/topBar/search.vue'));
const OnlineUser = defineAsyncComponent(() => import('/@/views/system/onlineUser/index.vue'));

// 定义变量内容
const { locale, t } = useI18n();
const router = useRouter();
const stores = useUserInfo();
const storesThemeConfig = useThemeConfig();
const { userInfos } = storeToRefs(stores);
const { CwglThemeConfig } = storeToRefs(storesThemeConfig);
const searchRef = ref();
const onlineUserRef = ref();
const dialogVisible = ref(false);
const state = reactive({
	isScreenfull: false,
	disabledI18n: 'zh-cn',
	disabledSize: 'large',
	noticeList: [] as any, // 站内信列表
});
// 设置分割样式
const layoutUserFlexNum = computed(() => {
	let num: string | number = '';
	const { layout, isClassicSplitMenu } = CwglThemeConfig.value;
	const layoutArr: string[] = ['defaults', 'columns'];
	if (layoutArr.includes(layout) || (layout === 'classic' && !isClassicSplitMenu)) num = '1';
	else num = '';
	return num;
});
// 是否有未读消息
const hasUnreadNotice = computed(() => {
	return state.noticeList.some((r: any) => r.readStatus == undefined || r.readStatus == 0);
});
// 全屏点击时
const onScreenfullClick = () => {
	if (!screenfull.isEnabled) {
		ElMessage.warning('暂不不支持全屏');
		return false;
	}
	screenfull.toggle();
	screenfull.on('change', () => {
		if (screenfull.isFullscreen) state.isScreenfull = true;
		else state.isScreenfull = false;
	});
};
// 布局配置 icon 点击时
const onLayoutSetingClick = () => {
	console.log('布局配置');
	mittBus.emit('openSettingsDrawer');
	//   openSetingsDrawer
};
// 下拉菜单点击时
const onHandleCommandClick = (path: string) => {
	if (path === 'clearCache') {
		Local.clear();
		Session.clear();
		window.location.reload();
	} else if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('message.user.logOutTitle'),
			message: t('message.user.logOutMessage'),
			type: 'warning',
			showCancelButton: true,
			confirmButtonText: t('message.user.logOutConfirm'),
			cancelButtonText: t('message.user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('message.user.logOutExit');
					// await getAPI(SysAuthApi).apiSysAuthLogoutPost();
					clearAccessTokens();
					instance.confirmButtonLoading = false;
					done();
				} else {
					done();
				}
			},
		})
			.then(async () => {
				clearAccessTokens();
			})
			.catch(() => {});
	} else if (path === 'downLoad') {
		console.log('下载');
		dialogVisible.value = true;
	} else {
		router.push(path);
	}
};
// 菜单搜索点击
const onSearchClick = () => {
	searchRef.value.openSearch();
};
// 在线用户列表
const onOnlineUserClick = () => {
	//暂时不开放，这个功能。
	//onlineUserRef.value.openDrawer();
};
// 组件大小改变
const onComponentSizeChange = (size: string) => {
	Local.remove('CwglThemeConfig');
	CwglThemeConfig.value.globalComponentSize = size;
	Local.set('CwglThemeConfig', CwglThemeConfig.value);
	initI18nOrSize('globalComponentSize', 'disabledSize');
	window.location.reload();
};
// 语言切换
const onLanguageChange = (lang: string) => {
	Local.remove('CwglThemeConfig');
	CwglThemeConfig.value.globalI18n = lang;
	Local.set('CwglThemeConfig', CwglThemeConfig.value);
	locale.value = lang;
	other.useTitle();
	initI18nOrSize('globalI18n', 'disabledI18n');
};
// 初始化组件大小/i18n
const initI18nOrSize = (value: string, attr: string) => {
	(<any>state)[attr] = Local.get('CwglThemeConfig')[value];
};
// 页面加载时
onMounted(async () => {
	if (Local.get('CwglThemeConfig')) {
		initI18nOrSize('globalComponentSize', 'disabledSize');
		initI18nOrSize('globalI18n', 'disabledI18n');
	}
	// 手动获取用户桌面通知权限
	if (Push.Permission.GRANTED) {
		// 判断当前是否有权限，没有则手动获取
		Push.Permission.request(null, null);
	}
	// 监听浏览器 当前系统是否在当前页
	document.addEventListener('visibilitychange', () => {
		if (!document.hidden) {
			// 清空关闭消息通知，
			Push.clear();
		}
	});
	// 加载未读的站内信
	var res = await getAPI(SysNoticeApi).apiSysNoticeUnReadListGet();
	state.noticeList = res.data.result ?? [];

	// 接收站内信
	////signalR.on('PublicNotice', receiveNotice);

	signalR.on('SignalrMsg', receiveNotice);

	// // 处理消息已读
	// mittBus.on('noticeRead', (id) => {
	// 	const notice = state.noticeList.find((r: any) => r.id == id);
	// 	if (notice == undefined) return;

	// 	// 设置已读
	// 	notice.readStatus = 1;
	// });
});
// // 页面卸载时
// onUnmounted(() => {
// 	mittBus.off('noticeRead', () => {});
// });

const receiveNotice = (msgtxt: any) => {
	let msgor = JSON.parse(msgtxt);
	console.log(msgor);

	if (!msgor.MsgType) {
		ElNotification({
			position: 'top-right',
			title: msgor.MsgTitle ? msgor.MsgTitle : '',
			message: msgor.MsgContent,
			dangerouslyUseHTMLString: true,
			duration: 10000,
		});
	} else if (msgor.MsgType == 'sysNotice') {
		ElNotification({
			position: 'top-left',
			title: msgor.MsgTitle ? msgor.MsgTitle : '',
			type: 'warning',
			message: msgor.MsgContent,
			dangerouslyUseHTMLString: true,
			duration: 0,
		});
	} else if (msgor.MsgType == 'success' || msgor.MsgType == 'info' || msgor.MsgType == 'error' || msgor.MsgType == 'sitemsg') {
		//   if (msgor.MsgType == "sitemsg")
		// 	that.getSiteMsgCount(true);

		ElNotification({
			position: 'top-right',
			title: msgor.MsgTitle ? msgor.MsgTitle : '',
			type: msgor.MsgType,
			message: msgor.MsgContent,
			dangerouslyUseHTMLString: true,
			duration: 10000,
		});
	} else if (msgor.MsgType == 'Inventory.WarehousingOrderVideo.IsBindCount') {
	} else if (msgor.MsgType == 'OnLineUserCountChange') {
		//   that.$nextTick(() => {
		// 	that.onLineUserSum.DingUserCount =  msgor.ExtData.DingUserCount;
		// 	that.onLineUserSum.OnlineCount =  msgor.ExtData.OnlineCount;
		//   })
	} else {
		//如果是非系统通知，通知给全局监听的地方
		mittBus.emit(msgor.MsgType, msgor.ExtData);
	}
};
const clearAccessTokens = () => {
	const accessTokenKey = 'access-token';
	const refreshAccessTokenKey = `x-${accessTokenKey}`;

	// 清除 token
	Local.remove(accessTokenKey);
	Local.remove(refreshAccessTokenKey);
	Session.clear();

	// 刷新浏览器
	window.location.reload();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'title', title: '标题' },
	{ sortable: true, field: 'message', title: '状态', width: '85' },
	{ sortable: true, field: 'createdTime', title: '开始时间', width: '140', formatter: 'formatTime' },
	{ sortable: true, field: 'modifiedTime', title: '结束时间', width: '140', formatter: 'formatTime' },
	{
		title: '下载地址',
		type: 'btnList',
		width: '70',
		btnList: [
			{
				title: '下载',
				handle: (row: any) => {
					downLoadFile(row.fileUrl, row.title);
				},
				isDisabled: (row: any) => {
					return !row.fileUrl;
				},
			},
		],
	},
	{
		title: '无文件名下载',
		tipmesg: '可在浏览器下载栏查看到下载进度',
		type: 'btnList',
		width: '120',
		btnList: [
			{
				title: '下载',
				handle: (row: any) => {
					window.open(row.fileUrl);
				},
				isDisabled: (row: any) => {
					return !row.fileUrl;
				},
			},
		],
	},
]);
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;

	&-link {
		height: 100%;
		display: flex;
		align-items: center;
		white-space: nowrap;

		&-photo {
			width: 25px;
			height: 25px;
			border-radius: 100%;
		}
	}

	&-icon {
		padding: 0 10px;
		cursor: pointer;
		color: var(--next-bg-topBarColor);
		height: 50px;
		line-height: 50px;
		display: flex;
		align-items: center;

		&:hover {
			background: var(--next-color-user-hover);

			i {
				display: inline-block;
				animation: logoAnimation 0.3s ease-in-out;
			}
		}
	}

	:deep(.el-dropdown) {
		color: var(--next-bg-topBarColor);
	}

	:deep(.el-badge) {
		height: 40px;
		line-height: 40px;
		display: flex;
		align-items: center;
	}

	:deep(.el-badge__content.is-fixed) {
		top: 12px;
	}
}
</style>
