<template>
	<div>
		<div>
			<el-select
				v-if="!props.multiple"
				v-model="innerValue"
				:clearable="props.clearable"
				filterable
				:style="props.cststyle"
				:reserve-keyword="true"
				:placeholder="props.placeholder"
				@clear="clear"
				remote
				:remote-method="getAllShop"
				@change="changeValue"
			>
				<el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
			<el-select
				v-if="props.multiple"
				v-model="valueList"
				:clearable="props.clearable"
				filterable
				multiple
				remote
				:collapse-tags="props.multiple"
				:style="props.cststyle"
				:reserve-keyword="true"
				:placeholder="props.placeholder"
				@clear="clear"
				:remote-method="getAllShop"
				@change="changeValue"
			>
				<el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineProps, onMounted, defineExpose } from 'vue';
import { GetShopList } from '/@/api/operatemanage/shop';
const props = defineProps({
	clearable: {
		type: Boolean,
		default: true,
	},
	cststyle: {
		type: String,
		default: 'width: 100%;',
	},
	placeholder: {
		type: String,
		default: '请选择店铺',
	},
	isDdUserId: {
		type: Boolean,
		default: true,
	},
	// 允许从父组件传入默认值
	defaultValues: {
		type: Array,
		default: () => [], // 默认为空数组
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	platform: {
		type: Number,
		default: 2, // 默认值为2
	},
	field: {
		type: String,
		default: 'shopCode', // 默认值为'shopCode'
	},
});
const innerValue = defineModel('value');
const innerLabel = defineModel('label');
const valueList = defineModel('valueList');
const userOptions = ref<Public.options[]>([]); // 初始化为一个空数组
const clear = () => {
	innerValue.value = null;
	innerLabel.value = '';
	valueList.value = [];
};
const changeValue = (value: any) => {
	if (props.multiple) {
		if (!value || value?.length == 0) {
			innerLabel.value = '';
			valueList.value = [];
		} else {
			innerLabel.value = userOptions.value!.filter((item: any) => value.includes(item.value)).map((item: any) => item.label);
		}
	} else {
		if (value === null) {
			innerLabel.value = '';
			innerValue.value = null;
		} else {
			innerLabel.value = value ? userOptions.value!.find((item: any) => item.value === value)?.label : '';
		}
	}
};
const getAllShop = async (shopName: string) => {
	// if (shopName === '') return;
	let { data, success } = await GetShopList({ platform: props.platform, CurrentPage: 1, PageSize: 100, shopName });
	if (data && success) {
		userOptions.value = data.list?.map((item: any) => {
			return {
				value: item[props.field],
				label: item.shopName,
			};
		});
		if (props.multiple) {
			changeValue(valueList.value);
		} else {
			changeValue(innerValue.value);
		}
	}
};
onMounted(() => {
	// getAllShop();
});
defineExpose({
	userOptions,
	clear,
});
</script>

<style scoped lang="scss">
:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
