<template>
	<div>
		<el-select
			v-model="innerValue"
			:clearable="props.clearable"
			filterable
			remote
			:style="props.cststyle"
			reserve-keyword
			:placeholder="props.placeholder"
			:remote-method="remoteMethod"
			@clear="clear"
			:loading="remoteLoading"
			@change="changeValue"
		>
			<el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
	</div>
</template>

<script setup lang="ts" name="">
import { QueryDept } from '/@/api/cwManager/feeData';
import { ref, defineProps, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
const props = defineProps({
	clearable: {
		type: Boolean,
		default: true,
	},
	cststyle: {
		type: String,
		default: 'width: 100%;',
	},
	placeholder: {
		type: String,
		default: '请输入部门',
	},
});
const innerValue = defineModel('value');
const innerLabel = defineModel('label');
const id = defineModel('id');
const remoteLoading = ref(false);
const userOptions = ref<Public.options[]>();
const changeValue = (value: any) => {
	innerValue.value = value ? userOptions.value!.find((item: any) => item.value === value)?.label : '';
	id.value = value ? userOptions.value!.find((item: any) => item.value === value)?.id : '';
};
const remoteMethod = async (query: any) => {
	if (query && query.length > 50) return ElMessage.error('输入内容过长');
	remoteLoading.value = true;
	if (query !== '') {
		const data = await QueryDept(query);
		if (data) {
			userOptions.value = data?.map((item: any) => {
				return {
					label: item.full_name,
					value: item.dept_id,
					id: item.dept_id,
				};
			});
			innerLabel.value = userOptions.value?.find((item: any) => item.value === innerValue.value)?.name;
		}
	}
	remoteLoading.value = false;
};
const clear = () => {
	innerValue.value = null;
	innerLabel.value = '';
	userOptions.value = [];
};
onMounted(async () => {
	if (innerLabel.value) {
		remoteMethod(innerValue.value);
	}
});
</script>

<style scoped lang="scss"></style>
