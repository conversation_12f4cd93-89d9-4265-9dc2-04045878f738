import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/totalReport/`;

//查询资金总报表
export const QueryTotalReport = (params: any) => request.post(apiPrefix + 'QueryTotalReport', params);

//计算匹配费用 ComputeFee
export const ComputeFee = (params: any) => request.post(apiPrefix + 'ComputeFee', params);

//查询支出明细 QueryReportDetail
export const QueryReportDetail = (params: any) => request.post(apiPrefix + 'QueryReportDetail', params);

//强制计算 ForceComputeFee
export const ForceComputeFee = (params: any) => request.post(apiPrefix + 'ForceComputeFee', params);

//计算时间 QueryMatchTime
export const QueryMatchTime = (params: any) => request.post(apiPrefix + 'QueryMatchTime', params);

export const UpdateTotalReport = (params: any) => request.post(apiPrefix + 'UpdateTotalReport', params);

//导出资金日报表
export const ExportTotalReport = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTotalReport', params, config);

//导出支出明细
export const ExportTotalReportOutAmount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTotalReportOutAmount', params, config);

//导出收入明细
export const ExportTotalReportInAmount = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTotalReportInAmount', params, config);

//部分强制计算 PartComputeFee
export const PartComputeFee = (params: any) => request.post(apiPrefix + 'PartComputeFee', params);