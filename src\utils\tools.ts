export const platformlist = [
	{ value: 0, label: '未知' },
	{ value: 1, label: '天猫' },
	{ value: 2, label: '拼多多' },
	{ value: 3, label: '跨境' },
	{ value: 4, label: '阿里巴巴' },
	{ value: 5, label: '其他' },
	{ value: 6, label: '抖音' },
	{ value: 7, label: '京东' },
	{ value: 8, label: '淘工厂' },
	{ value: 9, label: '淘宝' },
	{ value: 10, label: '苏宁' },
	{ value: 11, label: '分销' },
	{ value: 12, label: '希音-全托' },
	{ value: 16, label: '希音-自营' },
	{ value: 13, label: 'TEMU-全托' },
	{ value: 14, label: '快手' },
	{ value: 15, label: 'TEMU-半托' },
	{ value: 17, label: '抖音供销' },
	{ value: 18, label: 'TikTok全托' },
	{ value: 19, label: '希音半托' },
	{ value: 72, label: '京喜' },
	{ value: 73, label: '京喜nn' },
	{ value: 74, label: '京东自营入仓' },
	{ value: 20, label: '视频号' },
	{ value: 21, label: '小红书' },
];

//快递公司
export const expressCompany = [
	{ value: '南昌中通', label: '南昌中通' },
	{ value: '义乌中通', label: '义乌中通' },
	{ value: '义乌申通', label: '义乌申通' },
	{ value: '南昌中通-三部', label: '南昌中通-三部' },
	{ value: '义乌韵达-大件', label: '义乌韵达-大件' },
	{ value: '义乌韵达后宅', label: '义乌韵达后宅' },
	{ value: '义乌韵达（中通）', label: '义乌韵达（中通）' },
	{ value: '义乌极兔', label: '义乌极兔' },
	{ value: '湖北极兔-葛店', label: '湖北极兔-葛店' },
	{ value: '南昌韵达', label: '南昌韵达' },
	{ value: '南昌韵达-大件', label: '南昌韵达-大件' },
	{ value: '南昌韵达-云仓', label: '南昌韵达-云仓' },
	{ value: '南昌极兔', label: '南昌极兔' },
	{ value: '南昌申通', label: '南昌申通' },
	{ value: '义乌圆通', label: '义乌圆通' },
	{ value: '南昌圆通', label: '南昌圆通' },
];

export const platformLink: any = {
	1: 'https://detail.tmall.com/item.htm?id=',
	天猫: 'https://detail.tmall.com/item.htm?id=',
	淘系: 'https://detail.tmall.com/item.htm?id=',
	2: 'https://mobile.yangkeduo.com/goods2.html?goods_id=',
	拼多多: 'https://mobile.yangkeduo.com/goods2.html?goods_id=',
	8: 'https://detail.tmall.com/item.htm?id=',
	淘工厂: 'https://detail.tmall.com/item.htm?id=',
	9: 'https://detail.tmall.com/item.htm?id=',
	淘宝: 'https://detail.tmall.com/item.htm?id=',
	7: 'https://item.jd.com/',
	京东: 'https://item.jd.com/',
	4: 'https://detail.1688.com/offer/',
	阿里巴巴: 'https://detail.1688.com/offer/',
	6: 'https://haohuo.jinritemai.com/views/product/detail?id=',
	抖音: 'https://haohuo.jinritemai.com/views/product/detail?id=',
};
// 中国银行、招商银行、中国工商银行、农商银行、中信银行、中国农业银行、中国建设银行、中国邮政储蓄银行、北京银行、交通银行、平安银行、浙商银行、中国民生银行、广发银行、支付宝余额、支付宝余额宝
export const bankList = [
	{ value: '中国银行', label: '中国银行' },
	{ value: '招商银行', label: '招商银行' },
	{ value: '中国工商银行', label: '中国工商银行' },
	{ value: '农商银行', label: '农商银行' },
	{ value: '中信银行', label: '中信银行' },
	{ value: '中国农业银行', label: '中国农业银行' },
	{ value: '中国建设银行', label: '中国建设银行' },
	{ value: '中国邮政储蓄银行', label: '中国邮政储蓄银行' },
	{ value: '北京银行', label: '北京银行' },
	{ value: '交通银行', label: '交通银行' },
	{ value: '平安银行', label: '平安银行' },
	{ value: '浙商银行', label: '浙商银行' },
	{ value: '中国民生银行', label: '中国民生银行' },
	{ value: '广发银行', label: '广发银行' },
	{ value: '支付宝', label: '支付宝' },
	{ value: '稠州银行', label: '稠州银行' },
	{ value: '农业银行', label: '农业银行' },
	{ value: '浦发银行', label: '浦发银行' },
	{ value: '网商银行', label: '网商银行' },
	{ value: '光大银行', label: '光大银行' },
	{ value: '兴业银行', label: '兴业银行' },
	{ value: '台州银行', label: '台州银行' },
	{ value: '农村信用社', label: '农村信用社' },
];

export const areaList = [
	{ value: '浙江义乌总公司', label: '浙江义乌总公司' },
	{ value: '江西南昌分公司', label: '江西南昌分公司' },
	{ value: '湖北武汉分公司', label: '湖北武汉分公司' },
	{ value: '陕西西安分公司', label: '陕西西安分公司' },
	{ value: '广东深圳分公司', label: '广东深圳分公司' },
	{ value: '客服服务中心', label: '客服服务中心' },
];

export const downLoadFile = (link: string, fileName: string) => {
	// 创建一个虚拟 a 标签
	var anchor = document.createElement('a');
	// 获取链接的二进制数据
	fetch(link)
		.then((response) => response.blob())
		.then((blob) => {
			// 将数据转换为一个表示二进制数据的 URL
			var url = URL.createObjectURL(blob);
			// 设置 a 标签的属性
			anchor.href = url;
			anchor.download = fileName.trimEnd();
			// 模拟点击 a 标签进行下载
			anchor.click();
			// 释放 URL 对象
			URL.revokeObjectURL(url);
		});
};
