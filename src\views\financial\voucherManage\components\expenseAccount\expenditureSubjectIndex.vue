<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="一级科目" name="first" style="height: 100%">
					<levelSubjectsOne />
				</el-tab-pane>
				<el-tab-pane label="二级科目" name="second" style="height: 100%" lazy>
					<levelSubjectsTwo />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const levelSubjectsOne = defineAsyncComponent(() => import('./components/levelSubjectsOne.vue'));
const levelSubjectsTwo = defineAsyncComponent(() => import('./components/levelSubjectsTwo.vue'));
const activeName = ref('first');
</script>
