<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end" style="width: 200px" :clearable="false" />
				<el-select v-model="query.platform" placeholder="平台" class="publicCss" clearable>
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20250313094053" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="PagePlatformFund"> </vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { platformlist } from '/@/utils/tools';
import { PagePlatformFund } from '/@/api/financewh/funds';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const query = ref({
	start: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
	platform: null,
});
const table = ref();
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const disposeProps = (data: any, callback: Function) => {
	console.log(data, 'data');
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'receiptDate', title: '日期', width: '90', formatter: 'formatDate' },
	{ sortable: true, field: 'platform', title: '平台', width: '90', formatter: 'formatPlatform' },
	{ sortable: true, field: 'z_Amount', title: '总余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'z_AmountIn', title: '总收益', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'z_AmountOut', title: '总支出', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'z_WithdrawAmount', title: '总提现', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{
		title: '货款账户',
		align: 'center',
		children: [
			{ sortable: true, field: 'hk_Amount', title: '货款余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'hk_AmountIn', title: '总收入', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'hk_AmountOut', title: '总支出', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'hk_WithdrawAmount', title: '提现', width: '90', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '推广账户',
		align: 'center',
		children: [
			{ sortable: true, field: 'tg_Amount', title: '推广余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'tg_AmountIn', title: '总收入', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'tg_AmountOut', title: '总支出', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'tg_WithdrawAmount', title: '提现', width: '90', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '保证金账号',
		align: 'center',
		children: [
			{ sortable: true, field: 'bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'bzj_AmountIn', title: '总收入', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'bzj_AmountOut', title: '总支出', width: '100', align: 'right', formatter: 'fmtAmt0' },
			{ sortable: true, field: 'bzj_WithdrawAmount', title: '提现', width: '90', align: 'right', formatter: 'fmtAmt0' },
		],
	},
]);
</script>

<style scoped lang="scss"></style>
