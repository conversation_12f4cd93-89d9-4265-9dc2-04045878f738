<template>
	<div style="width: 100%; height: 100%">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss"
						style="width: 200px" />
					<el-select v-model="query.busAccount" placeholder="别名" class="publicCss itemCss" filterable
						clearable>
						<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
						<el-option label="已匹配" value="是" />
						<el-option label="未匹配" value="否" />
						<el-option label="已忽略" value="忽略" />
						<el-option label="已拆分" value="已拆分" />
					</el-select>
					<el-select v-model="query.approverStatus" placeholder="审批状态" clearable class="publicCss">
						<el-option label="待审核" value="待审核" />
						<el-option label="已审核" value="已审核" />
					</el-select>
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-button @click="onResetMethod" type="primary">重置</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="2024091509395512" :tableCols="tableCols" :query="query" showsummary
					:pageSize="50" :pageSizes="[50, 100, 200, 300]"
					:treeConfig="{ transform: true, rowField: 'id', parentField: 'pId' }" isNeedDisposeProps
					@disposeProps="disposeProps" :query-api="QueryPurchaseOrderRefundRecordList" />
			</template>
		</Container>

		<el-dialog v-model="editDialog" :title="'查看'" width="1000" draggable overflow border>
			<div style="height: 350px">
				<div style="display: flex; justify-content: space-between; margin-bottom: 15px">
					<div>
						<span>别名:</span>
						{{ statusForm.bankType }}
					</div>
					<div>
						<span>收款账号:</span>
						{{ statusForm.account }}
					</div>
					<div>
						<span>金额:</span>
						{{ statusForm.amount }}
					</div>
				</div>
				<el-table :data="statusTableData" style="width: 100%" height="300">
					<el-table-column prop="account" label="银行卡号" show-overflow-tooltip />
					<el-table-column prop="bankType" label="银行" width="150" show-overflow-tooltip />
					<el-table-column prop="inAmount" label="收入金额(元)" width="110" show-overflow-tooltip />
					<el-table-column prop="outAmount" label="支出金额(元)" width="110" show-overflow-tooltip />
					<el-table-column prop="toAccount" label="对方卡号" width="150" show-overflow-tooltip />
					<el-table-column prop="toAccountName" label="对方用户名" width="110" show-overflow-tooltip />
					<el-table-column prop="occurenceTime" label="付款时间" width="150" show-overflow-tooltip />
				</el-table>
			</div>
			<div style="display: flex; justify-content: center">
				<el-button style="width: 80px" @click="editDialog = false">取消</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="editVisible" title="编辑" width="80%" draggable overflow :close-on-click-modal="false">
			<div style="display: flex; gap: 10px">
				<dataRange v-model:startDate="editQuery.startOccurenceTime" v-model:endDate="editQuery.endOccurenceTime"
					:clearable="false" class="publicCss" style="width: 300px" />
				<el-input v-model.trim="editQuery.account" class="publicCss" placeholder="账号" clearable maxlength="50"
					style="width: 150px; margin-bottom: 5px" />
				<!-- <el-select v-model="editQuery.area" placeholder="归属区域" class="publicCss" filterable clearable style="width: 150px">
					<el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select> -->
				<el-select v-model="editQuery.bankType" placeholder="行名" class="publicCss" filterable clearable
					style="width: 150px">
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div style="display: flex; gap: 0">
					<el-select v-model="editQuery.flowType" placeholder="交易类型" clearable
						style="width: 90px; margin: 0 0 5px 0">
						<el-option label="收入" value="收入" />
						<el-option label="支出" value="支出" />
					</el-select>
					<numRange v-model:maxNum="editQuery.maxAmount" v-model:minNum="editQuery.minAmount" :precision="2"
						style="width: 200px; margin: 0 10px 5px 0" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				</div>
				<el-button type="primary" @click="onInquire" v-reclick>查询</el-button>
			</div>
			<div style="height: 600px; width: 100%">
				<vxetable ref="table1" id="************" :tableCols="editTableCols" isNeedCheckBox v-if="editVisible"
					@select="checkboxChange" :query="editQuery" :pageSize="50" showsummary :isDisableCheckBox="false"
					:query-api="QueryBankFlow" />
			</div>
			<div style="display: flex; justify-content: center; margin-top: 20px">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="editSubmit" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, reactive } from 'vue';
import dayjs from 'dayjs';
import Decimal from 'decimal.js';
import { QueryBankFlow } from '/@/api/cwManager/bankFlow';
import {
	QueryFeeData,
	QueryBankFlowMatch,
	RelieveReturnAmount,
	IgnoreReturnAmount,
	UnIgnoreReturnAmount,
	UpdateReturnAmount,
	QueryPurchaseOrderRefundRecordList,
	ApproveReturnAmount,
	ExportPurchaseOrderRefundRecordList,
} from '/@/api/cwManager/feeData';
import { AllDeptViewList } from '/@/api/admin/deptuser';
import { bankList, areaList } from '/@/utils/tools';
import { ElMessageBox } from 'element-plus';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { getAPI } from '/@/utils/axios-utils';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const yhdeptSelect = defineAsyncComponent(() => import('/@/components/yhCom/deptSelect.vue'));
const editDialog = ref(false);
const editVisible = ref(false); //编辑弹窗
const statusVerify = ref(false);
const statusTableData = ref([]);
const nameList = ref<Public.options[]>([]);
interface CheckboxItem {
	id: string;
	inAmount: number;
}
const checkboxList = ref<CheckboxItem[]>([]);
const editInfo = ref({
	businessId: '', //钉钉流程号
	area: '', //归属区域
	type: '', //交易类型
	feeType: '', //费用类型
	bankRemark: '', //备注
	oldBusinessId: '', //原钉钉流程号
	id: '',
	returnAmount: 0, //退款金额
});
const statusForm = ref({
	// payBankAccount: '',
	// payBankAccountName: '',
	amount: '',
	account: '',
	bankType: '',
});
const editQuery = ref({
	status: '否',
	startOccurenceTime: '',
	endOccurenceTime: '',
	maxOutAmount: undefined,
	minOutAmount: undefined,
	maxInAmount: undefined,
	minInAmount: undefined,
	area: '',
	bankType: '',
	account: '',
	flowType: '',
	maxAmount: undefined,
	minAmount: undefined,
});
const rechargeRecord = async (row: any) => {
	statusTableData.value = [];
	if (row.relationBankFlowRecordId) {
		const { data, success } = await QueryBankFlowMatch({ relationBankFlowRecordId: row.relationBankFlowRecordId });
		if (!success) return;
		statusTableData.value = data.list;
		// statusForm.value = {
		// 	payBankAccountName: row.payBankAccountName,
		// 	payBankAccount: row.payBankAccount,
		// 	amount: row.amount ? row.amount : row.amount,
		// };
		let newdata = data.list[0];
		statusForm.value = {
			account: newdata.account,
			bankType: row.payBankAccountName,
			amount: row.returnAmount,
		};
		editDialog.value = true;
	}
};

const onResetMethod = () => {
	query.value.status = '';
	query.value.approveDDUserId = '';
	query.value.approveUserName = '';
	query.value.approveDept = '';
	query.value.businessId = '';
	query.value.maxAmount = undefined;
	query.value.minAmount = undefined;
	query.value.account = '';
	query.value.payBankAccount = '';
	query.value.accountName = '';

	query.value.busAccount = '';
	query.value.endDate = getYesterday();
	query.value.startDate = getYesterday();

	getList();
};

const state = reactive({
	loading: false,
});

const loading = ref(false);
// 导出
const exportProps = async () => {
	loading.value = true;
	await ExportPurchaseOrderRefundRecordList({ ...query.value })
		.then((data: any) => {
			// loading.value = false;
			// const aLink = document.createElement('a');
			// let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			// aLink.href = URL.createObjectURL(blob);
			// aLink.setAttribute('download', '退款数据' + new Date().toLocaleString() + '.xlsx');
			// aLink.click();
			if (!data.success) {
				return;
			}
			window.$message.success(data.msg);
		})
		.catch(() => {
			loading.value = false;
		});
};

const handleEdit = (row: any) => {
	editInfo.value = JSON.parse(JSON.stringify(row));
	editInfo.value.oldBusinessId = row.businessId;
	editInfo.value.id = row.id;
	editQuery.value.startOccurenceTime = '';
	editQuery.value.endOccurenceTime = '';
	if (row.refundDateTime) {
		editQuery.value.startOccurenceTime = dayjs(row.refundDateTime).format('YYYY-MM-DD');
		editQuery.value.endOccurenceTime = dayjs(row.refundDateTime).format('YYYY-MM-DD');
	} else {
		editQuery.value.startOccurenceTime = dayjs().format('YYYY-MM-DD');
		editQuery.value.endOccurenceTime = dayjs().format('YYYY-MM-DD');
	}
	editQuery.value.area = '';
	editQuery.value.bankType = '';
	editQuery.value.maxInAmount = undefined;
	editQuery.value.minInAmount = undefined;
	editQuery.value.maxOutAmount = undefined;
	editQuery.value.minOutAmount = undefined;
	checkboxList.value = [];
	editQuery.value.account = row.payBankAccount ? row.payBankAccount : '';
	// if (row.amount) {
	// 	editQuery.value.maxAmount = row.amount;
	// 	editQuery.value.minAmount = row.amount;
	// } else {
	// 	editQuery.value.maxAmount = undefined;
	// 	editQuery.value.minAmount = undefined;
	// }
	editQuery.value.maxAmount = row.returnAmount;
	editQuery.value.minAmount = row.returnAmount;
	editVisible.value = true;
};

const handApprovalMethod = async (row: any) => {
	ElMessageBox.confirm('是否审批此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await ApproveReturnAmount({ id: row.id });
			if (success) {
				window.$message.success('审批成功');
				getList();
			}
		})
		.catch(() => { });
};

const handleDelete = async (row: any, val: number) => {
	ElMessageBox.confirm('此操作将忽略此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			if (val == 1) {
				const { success } = await IgnoreReturnAmount({ id: row.id });
				if (success) {
					window.$message.success('忽略成功');
				}
			} else {
				const { success } = await UnIgnoreReturnAmount({ id: row.id });
				if (success) {
					window.$message.success('解除忽略成功');
				}
			}
			getList();
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const handleRelieve = async (row: any) => {
	ElMessageBox.confirm('是否解除此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await RelieveReturnAmount({ id: row.id });
			if (success) {
				window.$message.success('解除成功');
				getList();
			}
		})
		.catch(() => { });
};

//节流
const throttle = (func: (...args: any[]) => void, limit: number) => {
	let lastFunc: ReturnType<typeof setTimeout> | undefined;
	let lastRan: number | undefined;
	return function (...args: any[]) {
		if (lastRan === undefined) {
			func(...args);
			lastRan = Date.now();
		} else {
			clearTimeout(lastFunc);
			lastFunc = setTimeout(
				() => {
					if (Date.now() - (lastRan as number) >= limit) {
						func(...args);
						lastRan = Date.now();
					}
				},
				limit - (Date.now() - (lastRan as number))
			);
		}
	};
};

const checkboxChange = throttle(async (data: any, callback: Function) => {
	if (data.length == 0) {
		checkboxList.value = [];
		return;
	}
	let a = table1.value.selectedSpreadsRowData()
	checkboxList.value = data.concat(a);
}, 1000);

const onInquire = async () => {
	if (editQuery.value.maxInAmount != null && editQuery.value.minInAmount != null && editQuery.value.maxInAmount < editQuery.value.minInAmount) {
		return window.$message.error('收入金额最大值不能小于最小值');
	}
	if (editQuery.value.maxOutAmount != null && editQuery.value.minOutAmount != null && editQuery.value.maxOutAmount < editQuery.value.minOutAmount) {
		return window.$message.error('支出金额最大值不能小于最小值');
	}
	table1.value.getList();
};

const editSubmit = async () => {
	if (checkboxList.value.length == 0) {
		return window.$message.error('请选择数据');
	}
	let returnAmountString = 0;
	let totalReturnAmount = new Decimal(0);
	checkboxList.value.forEach((item) => {
		if (item.inAmount) {
			totalReturnAmount = totalReturnAmount.plus(new Decimal(item.inAmount));
		}
	});
	let verify = false;
	returnAmountString = totalReturnAmount.toNumber();
	if (returnAmountString != editInfo.value.returnAmount) {
		verify = true;
		return window.$message.error('金额不符，请重新添加！');
	}
	if (verify) return;
	let newarr: string[] = [];
	checkboxList.value.map((item) => {
		newarr.push(item.id);
	});
	const { success } = await UpdateReturnAmount({ relationBankFlowRecordIds: newarr, id: editInfo.value.id });
	if (success) {
		window.$message.success('保存成功');
		editVisible.value = false;
		getList();
	}
};

const editTableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'relationArea', title: '归属区域' },
	{ sortable: true, field: 'relationFeeType', title: '费用类型', width: '90' },
	{ sortable: true, field: 'bankType', title: '行名', width: '80' },
	{ sortable: true, field: 'account', title: '账号', width: '80' },
	{ sortable: true, field: 'balance', title: '账户余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'occurenceTime', title: '交易时间', formatter: 'formatDate', width: '100' },
	{ sortable: true, field: 'inAmount', title: '收入余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '支出余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'toAccount', title: '对方账号' },
	{ sortable: true, field: 'toAccountName', title: '对方名称' },
	{ sortable: true, field: 'flowType', title: '交易类型', width: '90' },
	{ sortable: true, field: 'relationRemark', title: '交易备注' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : ''),
	},
]);

const isDisabled1 = (row: any, val: number): boolean => {
	if (val != 1 && row.approverStatus === '已审核') {
		return true;
	}
	if (val == 1) {
		return row.relationStatus != '是' ? true : false;
	} else if (val == 2) {
		return row.relationStatus === '否' && row.pId == 0 && (row.clickPage == null || row.clickPage == 'Order') ? false : true
	} else if (val == 3) {
		return (row.relationStatus === '否' && (row.clickPage == null || row.clickPage == 'Order')) && row.pId == 0 ? false : true;
	} else if (val === 4) {
		return (row.relationStatus === '是' || row.approverStatus === '待审核') && row.pId == 0 && (row.clickPage == null || row.clickPage == 'Order') ? false : true
	} else if (val == 5) {
		return ((row.relationStatus === '是' || row.relationStatus === '已拆分') && row.approverStatus === '待审核') && row.pId == 0 && (row.clickPage == null || row.clickPage == 'Order') ? false : true
	} else if (val == 6) {
		return row.relationStatus == '忽略' ? false : true;
	}
	return false;
};

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'processCode', title: '流程编号', color: (row: any) => (row.isRelation === false ? 'red' : 'black') },
	{ sortable: true, field: 'refundDateTime', title: '退款时间', treeNode: true },
	{ sortable: true, field: 'returnAmount', title: '退款金额', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'payBankType', title: '收款方式', width: '180' },
	{ sortable: true, field: 'payBankAccountName', title: '别名' },
	// { sortable: true, field: 'approveDept', title: '发起人部门' },
	{ sortable: true, field: 'payBankAccount', title: '收款账户' },
	// { sortable: true, field: 'applyFeetype', title: '状态', width: '90' },
	// { sortable: true, field: 'amount', title: '金额(元)', width: '90', align: 'right', formatter: 'fmtAmt2' },
	// { sortable: true, field: 'payType', title: '付款方式', width: '90' },
	// { sortable: true, field: 'payBankAccountName', title: '姓名/账户名' },
	// { sortable: true, field: 'payBankAccount', title: '卡号' },
	// { sortable: true, field: 'payTime', title: '支付日期', formatter: 'formatTime', width: '100' },
	// { sortable: true, field: 'skBankAccount', title: '收款账号' },
	// { sortable: true, field: 'skBankUserName', title: '收款账户名' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		align: 'center',
		type: 'click',
		handle: (row: any) => rechargeRecord(row),
		isDisabled: (row: any) => isDisabled1(row, 1),
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : row.relationStatus == '已拆分' ? '已拆分' : ''),
	},
	{ sortable: true, field: 'operatorUserName', title: '操作人', width: '90' },
	{ sortable: true, field: 'approverUserName', title: '审批人', width: '90' },
	{ sortable: true, field: 'approverStatus', title: '审批状态', width: '90' },
	{ sortable: true, field: 'occurenceDate', title: '交易日期', formatter: 'formatDate', width: '90' },
	// { sortable: true, field: 'processRemark', title: '流程备注' },
	{
		title: '操作',
		field:'**************',
		align: 'center',
		type: 'btnList',
		fixed: 'right',
		width: '180',
		btnList: [
			{ title: '关联', handle: handleEdit, isDisabled: (row: any) => isDisabled1(row, 2) },
			{ title: '忽略', handle: (row: any) => handleDelete(row, 1), isDisabled: (row: any) => isDisabled1(row, 3) },
			{ title: '解除忽略', handle: (row: any) => handleDelete(row, 2), isDisabled: (row: any) => isDisabled1(row, 6) },
			{ title: '解除', handle: handleRelieve, isDisabled: (row: any) => isDisabled1(row, 4) },
			{ title: '审批', handle: handApprovalMethod, isDisabled: (row: any) => isDisabled1(row, 5), permissions: 'auditBtn' },
		],
	},
]);
const getYesterday = () => {
	const now = new Date();
	const yesterday = new Date(now.setDate(now.getDate() - 1));
	return yesterday.toISOString().split('T')[0];
};

const options = ref<Public.options[]>([]);
const table = ref();
const table1 = ref();
const query = ref({
	startDate: getYesterday(),
	endDate: getYesterday(),
	status: '',
	approveDDUserId: '',
	approveUserName: '',
	approveDept: '',
	businessId: '',
	maxAmount: undefined,
	minAmount: undefined,
	account: '',
	feeType: '',
	accountName: '',
	busAccount: '',
	payBankAccount: '',
	approverStatus: '',
});
const getList = () => {
	if (query.value.maxAmount != null && query.value.minAmount != null && query.value.maxAmount < query.value.minAmount) {
		return window.$message.error('金额最大值不能小于最小值');
	}
	table.value.query.currentPage = 1;
	table.value.getList();
};
const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.pId = String(item.pId);
	});
	data.data.list.forEach((item: any) => {
		item.occurenceDate = dayjs(item.occurenceDate).format('YYYY-MM-DD');
	});
	callback(data);
};
const getAllDept = async () => {
	const { data } = await AllDeptViewList();
	// options.value = data.map((item: any) => ({ label: item.deptName, value: item.deptId }));
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.accountName }));
};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss"></style>
