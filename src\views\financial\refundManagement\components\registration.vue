<template>
	<div style="width: 100%; height: 100%">
		<Container>
			<template #header>
				<div class="top">
					<el-form :model="singForm" :rules="rules" ref="refsingForm" label-width="110px" width="300px">
						<el-row>
							<el-col :span="4">
								<el-form-item label="退款金额" prop="refundAmount">
									<el-input-number v-model.trim="singForm.refundAmount" placeholder="请输入" :min="0" :max="9999999" :precision="2" :controls="false" />
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="退款时间" prop="refundDatetime">
									<el-date-picker v-model="singForm.refundDatetime" type="datetime" placeholder="选择日期时间" format="YYYY/MM/DD HH:mm:ss" value-format="YYYY-MM-DD H:m:s" />
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="业务别名">
									<el-select v-model="singForm.serviceAlias" placeholder="业务别名" clearable filterable style="width: 170px" @change="onAnotherName">
										<el-option label="无" value="无" />
										<el-option label="昀晗采购" value="昀晗采购" />
										<el-option label="昀晗采购2" value="昀晗采购2" />
										<el-option label="昀晗采购3" value="昀晗采购3" />
										<el-option label="昀晗采购7" value="昀晗采购7" />
										<el-option label="tb556989557472" value="tb556989557472" />
										<el-option label="万俊 （工行）4694" value="万俊 （工行）4694" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="收款账号" prop="collectAccount">
									<el-select v-model="singForm.collectAccount" placeholder="收款账号" clearable filterable disabled style="width: 220px" @change="onCollectChange">
										<el-option v-for="item in receivables" :key="item.value" :label="`${item.userName} ${item.bankType} ${item.label}`" :value="item.value" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="收款方式" prop="collectType">
									<el-input v-model="singForm.collectType" placeholder="收款方式" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="收款人" prop="collectUser">
									<el-input v-model="singForm.collectUser" placeholder="收款人" disabled></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row style="height: 110px; margin-top: 15px">
							<el-col :span="13">
								<el-form-item label="附件" prop="annexUrl">
									<uploadMf v-if="editPriceVisible" v-model:imagesStr="singForm.annexUrl" :upstyle="{ height: 40, width: 40 }" :limit="9"></uploadMf>
								</el-form-item>
							</el-col>
							<el-col :span="11">
								<el-form-item label="备注">
									<el-input type="textarea" :autosize="{ minRows: 4, maxRows: 5 }" resize="none" placeholder="请输入内容" v-model="singForm.remark" maxlength="100" show-word-limit> </el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row style="margin-bottom: 10px">
							<el-col :span="3">
								<el-form-item label="待入判断金额" prop="adjustType">
									<span style="color: red">{{ singForm.pendingJudgeAmount }}</span>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="调整类型" prop="adjustType">
									<el-select v-model="singForm.adjustType" placeholder="调整类型" filterable style="width: 150px">
										<el-option label="主对冲" value="主对冲" />
										<el-option label="多入不退" value="多入不退" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="17">
								<el-form-item label="采购单号" prop="po_Id">
									<div style="display: flex; align-items: baseline; width: 100%">
										<div>
											<el-button class="button-new-tag" size="small" @click="showInput">新增采购单号</el-button>
										</div>
										<div class="tag-container">
											<el-tag :key="tag" v-for="tag in singForm.po_Id" closable :disable-transitions="false" @close="handleClose(tag)" class="tag_item">
												<span class="tag_item_value">{{ tag }}</span>
											</el-tag>
											<el-input-number
												v-show="inputVisible"
												v-model.trim="inputValue"
												placeholder="请输入"
												size="small"
												ref="saveTagInput"
												:min="0"
												:max="9999999"
												:precision="0"
												:controls="false"
												@keyup.enter.native="handleInputConfirm"
												@blur="handleInputConfirm"
												class="input-new-tag"
											/>
										</div>
										<el-button type="primary" size="small" @click="onGeneratedData" style="width: 70px; margin-left: 10px">生成</el-button>
									</div>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
			</template>
			<template #content>
				<vxetable
					showsummary
					ref="table"
					:pageSize="30"
					id="2024100165220"
					:pageSizes="[30, 50, 100, 200, 300]"
					:tableCols="tableCols"
					:query="query"
					:isNeedQueryApi="false"
					:query-api="GeneratePurchaseOrderRefundDetails"
					:isNeedPager="false"
					isNeedDisposeProps
					@disposeProps="disposeProps"
				>
					<template #rightCols>
						<!-- <vxe-colgroup title="操作" align="center"> -->
						<vxe-column field="returnAmount" title="退货款" width="140">
							<template #default="{ row }">
								<div class="line_style">
									<el-input-number v-model.trim="row.returnAmount" placeholder="请输入" size="small" :max="9999999" :precision="2" :controls="false" @blur="saveRowEvent(row, 1)" />
								</div>
							</template>
							<template #footer="{ items }">
								<span>{{ summaryarry.returnAmount_sum }}</span>
							</template>
						</vxe-column>
						<!-- <vxe-column field="adjustAmount" title="调整金额" width="140">
							<template #default="{ row }">
								<div class="line_style">
									<el-input-number v-model.trim="row.adjustAmount" placeholder="请输入" size="small" :max="9999999" :precision="2" :controls="false" @blur="saveRowEvent(row, 2)" />
								</div>
							</template>
							<template #footer="{ items }">
								<span>{{ summaryarry.adjustAmount_sum }}</span>
							</template>
						</vxe-column>
						<vxe-column field="adjustType" title="调整类型" width="140">
							<template #default="{ row }">
								<div class="line_style">
									<el-select v-model="row.adjustType" placeholder="调整类型" clearable filterable>
										<el-option label="对冲(有相对应的对冲采购单号)" value="对冲(有相对应的对冲采购单号)" />
										<el-option label="多入不付" value="多入不付" />
										<el-option label="多退不补" value="多退不补" />
										<el-option label="采购补差价负数入库" value="采购补差价负数入库" />
										<el-option label="未付款" value="未付款" />
										<el-option label="对冲(有相对应的采购单号)" value="对冲(有相对应的采购单号)" />
									</el-select>
								</div>
							</template>
							<template #footer="{ items }">
								<span></span>
							</template>
						</vxe-column> -->
						<vxe-column field="returnFreight" title="退运费" width="140">
							<template #default="{ row }">
								<div class="line_style">
									<el-input-number v-model.trim="row.returnFreight" placeholder="请输入" size="small" :min="0" :max="9999999" :precision="2" :controls="false" @blur="saveRowEvent(row, 3)" />
								</div>
							</template>
							<template #footer="{ items }">
								<span>{{ summaryarry.returnFreight_sum }}</span>
							</template>
						</vxe-column>
						<vxe-column field="totalRefunsds" title="总退款" width="110">
							<template #default="{ row }">
								<div class="line_style">
									{{ row.totalRefunsds }}
								</div>
							</template>
							<template #footer="{ items }">
								<span>{{ summaryarry.totalRefunsds_sum }}</span>
							</template>
						</vxe-column>
						<!-- </vxe-colgroup> -->
					</template>
				</vxetable>
			</template>
		</Container>
	</div>
</template>

<script setup lang="ts" name="">
import { GetAllOnlineBank } from '/@/api/cwManager/bankFlow';
import { ref, reactive, onMounted, onBeforeUnmount, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { GeneratePurchaseOrderRefundDetails, AddPurchaseOrderRefundDetails, editPurchaseOrderRefundDetails, editAddPurchaseOrderRefundDetails } from '/@/api/inventory/purchase';
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { ar, ru } from 'element-plus/es/locale';
const props = defineProps({
	purchaseNumber: { type: Array, default: [] },
	verify: { type: Boolean, default: false }, //是否验证
});
const emit = defineEmits(['onStorageMethod']);
const dialogMapVisible = ref(false);
const loading = ref(true);
const saveTagInput = ref();
const table = ref();
const inputVisible = ref(false);
const editPriceVisible = ref(false);
const urlList = ref([]);
const purchaseNumber = ref([]);
const inputValue = ref(undefined);
const tableData = ref<TableItem[]>([]);
const refsingForm = ref();
const singForm = ref({
	refundAmount: '',
	refundDatetime: '',
	collectAccount: null as string | null,
	serviceAlias: '',
	collectType: null as string | null,
	collectUser: null as string | null,
	annexUrl: [],
	remark: '',
	pendingJudgeAmount: 0,
	adjustType: '主对冲',
	po_Id: [] as string[],
});

interface TableItem {
	adjustAmount: number;
	returnAmount: number;
	returnFreight: number;
	pendingAmount: number;
	totalRefunsds?: number;
}

const summaryarry = ref({
	adjustAmount_sum: 0,
	returnAmount_sum: 0,
	returnFreight_sum: 0,
	totalRefunsds_sum: 0,
});

const receivables = ref([
	{
		label: '',
		value: '',
		name: '',
		id: '',
		accountName: '',
		account: '',
		userName: '',
		bankType: '',
		area: '',
		createdUserId: '',
		createdUserName: '',
		createdTime: '',
		modifiedUserId: '',
		modifiedUserName: '',
		modifiedTime: '',
		ddUserId: '',
		busAccountName: '',
	},
]);

const query = ref<{
	po_Ids: number[];
	adjustType: string;
}>({
	po_Ids: [],
	adjustType: '',
});

const rules = {
	refundAmount: [{ required: true, message: '请输入退款金额', trigger: 'blur' }],
	refundDatetime: [{ required: true, message: '请选择退款时间', trigger: 'blur' }],
	collectAccount: [{ required: true, message: '请选择收款账号', trigger: 'blur' }],
	collectType: [{ required: true, message: '请选择收款方式', trigger: 'blur' }],
	collectUser: [{ required: true, message: '请选择收款人', trigger: 'blur' }],
	annexUrl: [{ required: true, message: '请上传附件', trigger: 'blur' }],
	remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
	pendingJudgeAmount: [{ required: true, message: '请输入待入判断金额', trigger: 'blur' }],
	adjustType: [{ required: true, message: '请选择投诉类型', trigger: 'blur' }],
	po_Id: [{ required: true, message: '请选择采购单号', trigger: 'blur' }],
};

const onSaveMethod = async () => {
	let validResult = false;
	// 使用 Promise 包装 validate 方法
	await new Promise((resolve) => {
		refsingForm.value.validate((valid: boolean) => {
			if (!valid) {
				ElMessage.error('请填写必填项');
				resolve(false);
			} else {
				resolve(true);
			}
		});
	}).then((result) => {
		validResult = result as boolean;
	});
	if (!validResult) {
		return;
	}
	const refundAmount = Number(singForm.value.refundAmount);
	const pendingJudgeAmount = Number(singForm.value.pendingJudgeAmount);
	const totalRefunds = Number(summaryarry.value.totalRefunsds_sum);
	if (totalRefunds !== refundAmount && refundAmount === pendingJudgeAmount) {
		ElMessage.error('退款金额与总退款金额不一致');
		return;
	}
	if (pendingJudgeAmount !== refundAmount && refundAmount === totalRefunds) {
		ElMessage.error('退款金额与待入判断金额不一致');
		return;
	}
	if (refundAmount !== totalRefunds && pendingJudgeAmount === totalRefunds) {
		ElMessage.error('退款金额与总退款金额不一致');
		return;
	}
	if (refundAmount !== totalRefunds && pendingJudgeAmount !== totalRefunds && refundAmount !== pendingJudgeAmount) {
		ElMessage.error('退款金额、待入判断金额、总退款金额均不一致');
		return;
	}
	const params = { ...singForm.value, totalRefunsds_sum: summaryarry.value.totalRefunsds_sum, tableData: tableData.value, adjustType: singForm.value.adjustType };
	loading.value = true;
	let success = false;
	if (props.verify) {
		({ success } = await AddPurchaseOrderRefundDetails(params));
	} else {
		({ success } = await editAddPurchaseOrderRefundDetails(params));
	}
	loading.value = false;
	if (!success) return;
	emit('onStorageMethod');
	ElMessage.success('保存成功');
};

const saveRowEvent = (row: any, val: number) => {
	let index = tableData.value.findIndex((item) => item === row);
	const fieldsToCheck: (keyof TableItem)[] = ['adjustAmount', 'returnAmount', 'returnFreight'];
	fieldsToCheck.forEach((field) => {
		if (!tableData.value[index][field]) {
			tableData.value[index][field] = 0;
		}
	});
	if (val == 1) {
		tableData.value[index].adjustAmount = Number(precisionSub(tableData.value[index].pendingAmount, tableData.value[index].returnAmount));
	}
	tableData.value[index].totalRefunsds = Number(precisionAdd(tableData.value[index].returnAmount, tableData.value[index].returnFreight));
	summaryarry.value.returnFreight_sum = 0;
	summaryarry.value.totalRefunsds_sum = 0;
	summaryarry.value.adjustAmount_sum = 0;
	summaryarry.value.returnAmount_sum = 0;
	tableData.value.forEach((item: any) => {
		summaryarry.value.returnFreight_sum = Number(precisionAdd(summaryarry.value.returnFreight_sum, item.returnFreight ? item.returnFreight : 0));
		summaryarry.value.adjustAmount_sum = Number(precisionAdd(summaryarry.value.adjustAmount_sum, item.adjustAmount ? item.adjustAmount : 0));
		summaryarry.value.returnAmount_sum = Number(precisionAdd(summaryarry.value.returnAmount_sum, item.returnAmount ? item.returnAmount : 0));
	});
	summaryarry.value.totalRefunsds_sum = Number(precisionAdd(summaryarry.value.returnAmount_sum, summaryarry.value.returnFreight_sum));
	onCalculated();
};

const onGeneratedData = async () => {
	if (singForm.value.po_Id.length == 0) {
		ElMessage.error('请输入采购单号');
		return;
	}
	let purchase: any[] = [];
	purchase = singForm.value.po_Id.map((item) => Number(item));
	query.value.po_Ids = purchase;
	query.value.adjustType = singForm.value.adjustType;
	table.value.getList();
};

// 处理小数精度
const precisionAdd = (arg1: any, arg2: any) => {
	var r1, r2, m, n;
	try {
		r1 = arg1.toString().split('.')[1].length;
	} catch (e) {
		r1 = 0;
	}
	try {
		r2 = arg2.toString().split('.')[1].length;
	} catch (e) {
		r2 = 0;
	}
	m = Math.pow(10, Math.max(r1, r2));
	n = r1 >= r2 ? r1 : r2;
	return ((arg1 * m + arg2 * m) / m).toFixed(n);
};
const precisionSub = (arg1: any, arg2: any) => {
	var r1, r2, m, n;
	try {
		r1 = arg1.toString().split('.')[1].length;
	} catch (e) {
		r1 = 0;
	}
	try {
		r2 = arg2.toString().split('.')[1].length;
	} catch (e) {
		r2 = 0;
	}
	m = Math.pow(10, Math.max(r1, r2));
	n = r1 >= r2 ? r1 : r2;
	return ((arg1 * m - arg2 * m) / m).toFixed(n);
};

const onCalculated = () => {
	singForm.value.pendingJudgeAmount = 0;
	let summaryarrys = JSON.parse(JSON.stringify(summaryarry.value));
	summaryarrys.adjustAmount_sum = summaryarrys.adjustAmount_sum;
	let sum = 0;
	tableData.value.forEach((item: any) => {
		sum += item.refund_qty * item.price;
	});
	singForm.value.pendingJudgeAmount = Number(precisionAdd(precisionSub(summaryarrys.pendingAmount_sum, summaryarrys.adjustAmount_sum), summaryarrys.returnFreight_sum));
};

const showInput = () => {
	inputVisible.value = true;
	nextTick(() => {
		saveTagInput.value.focus();
	});
};

const handleInputConfirm = () => {
	if (singForm.value.po_Id.some((id) => String(id) === String(inputValue.value))) {
		ElMessage.error('采购单号存在重复');
		return;
	}
	let input = inputValue.value;
	if (input && singForm.value.po_Id.length < 20) {
		singForm.value.po_Id.push(input);
	} else if (singForm.value.po_Id.length >= 20) {
		ElMessage.warning('一次性最多只能添加20个采购单号');
	}
	inputVisible.value = false;
	inputValue.value = undefined;
};

const handleClose = (tag: string) => {
	singForm.value.po_Id.splice(singForm.value.po_Id.indexOf(tag), 1);
};

const onAnotherName = (e: any) => {
	if (e && e != '无') {
		receivables.value.forEach((item) => {
			if (item.busAccountName == e) {
				singForm.value.collectUser = item.userName;
				singForm.value.collectType = item.bankType;
				singForm.value.collectAccount = item.account;
			}
		});
	} else if (e == '无') {
		singForm.value.collectAccount = null;
		singForm.value.collectType = null;
		singForm.value.collectUser = null;
	} else {
		singForm.value.collectUser = null;
		singForm.value.collectType = null;
	}
};

const onCollectChange = async (e: any) => {
	if (e) {
		const { data } = await QueryOnlineBankSet({ account: e });
		singForm.value.collectUser = null;
		singForm.value.collectType = null;
		singForm.value.collectUser = data.list[0].userName;
		singForm.value.collectType = data.list[0].bankType;
	} else {
		singForm.value.collectUser = null;
		singForm.value.collectType = null;
	}
};

const init = async () => {
	const data = await GetAllOnlineBank();
	loading.value = false;
	receivables.value = data.map((item: any) => ({
		label: item.account,
		value: item.account,
		name: item.bankType,
		id: item.id,
		accountName: item.accountName,
		account: item.account,
		userName: item.userName,
		bankType: item.bankType,
		area: item.area,
		createdUserId: item.createdUserId,
		createdUserName: item.createdUserName,
		createdTime: item.createdTime,
		modifiedUserId: item.modifiedUserId,
		modifiedUserName: item.modifiedUserName,
		modifiedTime: item.modifiedTime,
		ddUserId: item.ddUserId,
		busAccountName: item.busAccountName,
	}));
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'po_Id', title: '采购单号', width: '100' },
	{ field: 'sku_id', title: '商品编码', width: '100' },
	{ field: 'price', title: '单价', width: '100' },
	{ field: 'buy_qty', title: '采购数量', width: '100' },
	{ field: 'qty', title: '已入库数量', width: '100' },
	{ field: 'pending_qty', title: '未入库数量', width: '100' },
	{ field: 'refund_qty', title: '退货数量', width: '100' },
	{ field: 'pendingAmount', title: '未入库金额', width: '100' },
]);
onMounted(() => {
	init();
	if (props.purchaseNumber) {
		editPurchaseOrderRefundDetails(props.purchaseNumber).then(({ data, success }) => {
			if (!success) return;
			nextTick(() => {
				tableData.value = data.list;
				singForm.value.refundDatetime = dayjs(data.list[0].refundDatetime).format('YYYY-MM-DD HH:mm:ss');
				singForm.value.collectAccount = data.list[0].collectAccount;
				singForm.value.collectType = data.list[0].collectType;
				singForm.value.collectUser = data.list[0].collectUser;
				singForm.value.annexUrl = data.list[0].annexUrl.split(',');
				singForm.value.remark = data.list[0].remark;
				singForm.value.adjustType = data.list[0].adjustType;
				singForm.value.po_Id = data.list.map((item: any) => item.po_Id);
				let obj: { [key: string]: boolean } = {};
				singForm.value.po_Id = singForm.value.po_Id.reduce<string[]>((cur, next) => {
					if (!obj[next]) {
						obj[next] = true;
						cur.push(next);
					}
					return cur;
				}, []);
				summaryarry.value = data.summary;
				onCalculated();
			});
		});
	}
	editPriceVisible.value = true;
});
defineExpose({
	onSaveMethod,
});
const disposeProps = async (data: any, callback: Function) => {
	summaryarry.value = data.data.summary;
	tableData.value = data.data.list;
	onCalculated();
	editPriceVisible.value = true;
	callback(data);
};
</script>

<style scoped lang="scss">
.btnGruop {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}
.publicCss {
	width: 150px;
	margin-right: 10px;
}
.line_style {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 5px;
}

.el-tag + .el-tag {
	margin-left: 10px;
}

.button-new-tag {
	// margin-left: 10px;
	margin: 0 5px 0 10px;
	height: 32px;
	line-height: 30px;
	padding-top: 0;
	padding-bottom: 0;
}

.input-new-tag {
	width: 90px;
	margin-left: 10px;
	// vertical-align: bottom;
}

.tag-container {
	white-space: nowrap;
	overflow-x: auto;
	cursor: grab;
	flex-wrap: wrap;
	align-items: center;
	margin-top: -2px;
	max-width: 708px;
}

/* 自定义横向滚动条 */
.tag-container::-webkit-scrollbar {
	height: 6px;
	/* 滚动条高度 */
}

/* 滚动条轨道 */
.tag-container::-webkit-scrollbar-track {
	background: #f1f1f1;
	/* 可以根据需要自定义轨道颜色 */
}

/* 滚动条滑块 */
.tag-container::-webkit-scrollbar-thumb {
	background: #888;
	/* 滚动条颜色 */
	border-radius: 10px;
	/* 圆角 */
}

/* 滚动条滑块悬停时的样式 */
.tag-container::-webkit-scrollbar-thumb:hover {
	background: #555;
	/* 悬停时滑块颜色 */
	cursor: pointer;
	/* 鼠标样式 */
}

.tag_item {
	height: 33px;
	padding-top: 5px;

	.tag_item_value {
		font-size: 13px;
	}
}

.line_style {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 5px;
}

.line_style span {
	display: inline-block;
	max-width: 150px;
	/* 设置合适的最大宽度 */
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	/* 添加省略号 */
}
</style>
