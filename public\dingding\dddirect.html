<!DOCTYPE html>
<html>
	<body style="width: 350px;height: 400px;">
	</body>
	 <script>
       var code= getQueryVariable("code");
       var flag=window.parent.qrlogin(code);
    // if(flag){
    //  window.parent.gotourl("/")
    // }
     function getQueryVariable(variable){
       var query = window.location.search.substring(1);
       var vars = query.split("&");
       for (var i=0;i<vars.length;i++) {
           var pair = vars[i].split("=");
           if(pair[0] == variable){return pair[1];}
       }
       return(false);
    }
</script>
</html>
