<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-date-picker v-model="query.year" type="year" placeholder="请选择年" value-format="YYYY" format="YYYY" class="publicCss" style="width: 130px" />
				<el-select v-model="query.sumTypeList" placeholder="费用大类" class="publicCss" style="width: 200px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in sumTypeList1" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.titleList" placeholder="科目名称" class="publicCss" style="width: 200px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in subjectListTwo" :key="item" :label="item" :value="item" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				:mergeCells="mergeCells"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				ref="table"
				id="levelSubjectsTwo202412221730"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				:orderBy="'sumType'"
				:isAsc="false"
				:isNeedPager="false"
				:queryApi="QueryCashOutlayChartSecondChart"
			>
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryCashOutlayChart } from '/@/api/cwManager/accountsChart';
import { QueryCashOutlayChartSecondChart, ExportCashOutlayChartSecondChart } from '/@/api/cwManager/accountsVouchers';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const vxeVisableColumn = ref<any>([]);
const mergeCells = ref<MergeCell[]>([]);
type MergeCell = { row: number; col: number; rowspan: number; colspan: number };
const pageLoading = ref(false);
const subjectListOne = ref([]);
const subjectListTwo = ref([]);
const sumTypeList1 = ref(['采购款', '独立核算', '快递费', '其他应收款', '人工设置', '日常费用', '营销费用']);
const table = ref();
const query = ref({
	sumTypeList: [],
	titleList: [],
	thisYear: '',
	nextYear: '',
	year: '',
});

const exportProps = async () => {
	await ExportCashOutlayChartSecondChart({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const formatNumber = (value: any): string => {
	if (value === null || value === undefined) {
		return '';
	} else if (value === 0) {
		return '0.00';
	} else if (isNaN(Number(value))) {
		// 非数字值原样返回
		return value;
	} else {
		return Number(value).toLocaleString('en-US', {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
	}
};

const formatObjectFields = (obj: Record<string, any>, fieldsToFormat: string[] = []): Record<string, any> => {
	return Object.fromEntries(
		Object.entries(obj).map(([key, value]) => {
			if (fieldsToFormat.length === 0 || fieldsToFormat.includes(key)) {
				return [key, formatNumber(value)];
			}
			return [key, value];
		})
	);
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'sumType', title: '费用大类', width: 'auto' },
	{ field: 'firstTitle', title: '科目名称(一级)', width: '120' },
	{ field: 'title', title: '科目名称', width: '150' },
	{ field: 'januarySum', title: '一月', width: '90', align: 'right' },
	{ field: 'februarySum', title: '二月', width: '90', align: 'right' },
	{ field: 'marchSum', title: '三月', width: '90', align: 'right' },
	{ field: 'aprilSum', title: '四月', width: '90', align: 'right' },
	{ field: 'maySum', title: '五月', width: '90', align: 'right' },
	{ field: 'juneSum', title: '六月', width: '90', align: 'right' },
	{ field: 'julySum', title: '七月', width: '90', align: 'right' },
	{ field: 'augustSum', title: '八月', width: '90', align: 'right' },
	{ field: 'septemberSum', title: '九月', width: '90', align: 'right' },
	{ field: 'octoberSum', title: '十月', width: '90', align: 'right' },
	{ field: 'novemberSum', title: '十一月', width: '90', align: 'right' },
	{ field: 'decemberSum', title: '十二月', width: '100', align: 'right' },
	{ field: 'sum', title: '合计', width: '110', align: 'right' },
]);

const getColumnIndex = (title: string) => {
	vxeVisableColumn.value = table.value.getColumnsInfo();
	if (!vxeVisableColumn.value) {
		return -1;
	}
	const index = vxeVisableColumn.value.visibleColumn.findIndex((col: any) => col.title === title);
	return index;
};

const disposeProps = async (data: any, callback: Function) => {
	let currentRow = 0;
	let currentCol = getColumnIndex('费用大类');
	let currentexpressCom = '';
	let rowspan = 1;
	mergeCells.value = [];
	data.data.list.forEach((item: any, index: any) => {
		if (item.sumType === currentexpressCom) {
			rowspan++;
			if (index === data.data.list.length - 1) {
				mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
			}
		} else {
			if (rowspan > 1) {
				mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
			}
			currentRow = index;
			currentCol = getColumnIndex('费用大类');
			currentexpressCom = item.sumType;
			rowspan = 1;
		}
		const fieldsToFormat = ['aprilSum', 'augustSum', 'decemberSum', 'februarySum', 'januarySum', 'julySum', 'juneSum', 'marchSum', 'maySum', 'novemberSum', 'octoberSum', 'septemberSum', 'sum'];
		fieldsToFormat.forEach((field) => {
			if (item[field] !== undefined) {
				item[field] = formatNumber(item[field]);
			}
		});
	});
	if (rowspan > 1) {
		mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
	}
	mergeCells.value = Array.from(new Set(mergeCells.value.map((item) => JSON.stringify(item)))).map((item) => JSON.parse(item));
	data.data.summary = formatObjectFields(data.data.summary);
	callback(data);
};

const getAllDept = async () => {
	const { data, success } = await QueryCashOutlayChart({ currentPage: 1, pageSize: 100000 });
	if (!success) return;
	subjectListOne.value = Array.from(new Set(data.list.filter((item: any) => item.pId == null).map((item: any) => item.title)));
	subjectListTwo.value = Array.from(new Set(data.list.filter((item: any) => item.pId != null).map((item: any) => item.title)));
	data.list.forEach((item: any) => {
		sumTypeList1.value.push(item.sumType);
	});
	sumTypeList1.value = Array.from(new Set(sumTypeList1.value));
};
onMounted(() => {
	query.value.year = dayjs().format('YYYY');
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

::v-deep .el-select__tags-text {
	max-width: 80px;
}
</style>
