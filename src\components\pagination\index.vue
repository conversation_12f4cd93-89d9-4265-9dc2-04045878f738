<template>
    <div>
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="size" :small="small"
            :background="background" :layout="layout" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" class="pagination" style="width: 100%;" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineProps, defineEmits } from 'vue';
import { ref } from 'vue'
const props = defineProps({
    currentPage: {//当前页
        type: Number,
        default: 1
    },
    total: {//总条数
        type: Number,
        default: 0,
        required: true
    },
    pageSize: {//每页显示条数
        type: Number,
        default: 10
    },
    size: {//每页显示条数
        type: Array,
        default: [10, 20, 50, 100]
    },
    background: {//背景色
        type: Boolean,
        default: true
    },
    small: {//是否为小型分页
        type: Boolean,
        default: false
    },
    layout: {//组件布局
        type: String,
        default: 'total, sizes, prev, pager, next, jumper'
    }
})
console.log(props, 'props');

const currentPage = ref(props.currentPage)
const total = ref(props.total)
const pageSize = ref(props.pageSize)
const size = ref(props.size)
const background = ref(props.background)
const small = ref(props.small)
const layout = ref(props.layout)

//接受父组件传来的事件
const emit = defineEmits(['sizeChange', 'pageChange'])

//页面数量改变
const handleSizeChange = (val: number) => {
    pageSize.value = val
    emit('sizeChange', pageSize.value)
}

//当前页改变
const handleCurrentChange = (val: number) => {
    currentPage.value = val
    emit('pageChange', currentPage.value)
}

const setPage = (val: number) => {
    currentPage.value = val
}

</script>

<style scoped lang="scss">
// .pagination {
//     margin-top: 20px;
//     width: 100%;
//     display: flex;
//     justify-content: end;
// }
::v-deep .el-pagination {
    display: flex;
    justify-content: end;
    align-items: center;
    margin: 0 !important;
    padding: 0 10px 0 0;
    height: 100%;
}
</style>
