<template>
	<div v-loading="loading" class="outbound-edit-container">
		<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" class="outbound-form">
			<el-row>
				<el-col :span="8">
					<el-form-item label="审批编号">
						{{ ruleForm.businessId }}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="采购员">
						{{ ruleForm.brandName }}
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="供应商名称" prop="supplier">
						<el-input v-model.trim="ruleForm.supplier" class="publicCss" placeholder="供应商名称" clearable maxlength="50" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="采购单号" prop="buyNo">
						<el-input v-model.trim="ruleForm.buyNo" placeholder="采购单号" class="publicCss" clearable maxlength="50" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="退换货金额" prop="refundChangeGoodsAmount">
						<el-input-number v-model="ruleForm.refundChangeGoodsAmount" placeholder="退换货金额" autocomplete="off" :min="0" :max="9999999" :precision="2" :controls="false" style="width: 170px" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="运费" prop="shippingFee">
						<el-input-number v-model="ruleForm.shippingFee" placeholder="运费" autocomplete="off" :min="0" :max="9999999" :precision="2" :controls="false" style="width: 170px" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="是否退换货" prop="isRefundChangeGoods">
						<el-select v-model="ruleForm.isRefundChangeGoods" placeholder="是否退换货" class="publicCss">
							<el-option label="是" :value="1" />
							<el-option label="否" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-button type="text" @click="addRow">新增一行</el-button>
			</el-row>
			<el-row>
				<div style="height: 250px; overflow-y: auto; border: 1px solid #dcdfe6">
					<el-table :data="ruleForm.dtls" style="width: 100%" :border="true">
						<el-table-column prop="collectUser" label="收款人" width="150" :show-overflow-tooltip="true">
							<template #default="{ row, $index }">
								<el-select v-model="row.collectUser" placeholder="收款人" clearable filterable @change="handleChange(row, $index)">
									<el-option v-for="item in props.bankList" :key="item.id" :label="item.label" :value="item.label" />
								</el-select>
							</template>
						</el-table-column>
						<el-table-column prop="collectAccount" label="收款账号" width="150" :show-overflow-tooltip="true" />
						<el-table-column prop="collectType" label="收款方式" width="145" :show-overflow-tooltip="true" />
						<el-table-column prop="totalRefundAmount" label="厂家实际退款总金额" width="150">
							<template #default="{ row, $index }">
								<el-input-number
									v-model="row.totalRefundAmount"
									:min="0"
									:max="9999999"
									:precision="2"
									:controls="false"
									placeholder="请输入"
									@blur="
										() => {
											if (!row.totalRefundAmount && row.totalRefundAmount !== '0') row.totalRefundAmount = '0';
										}
									"
								/>
							</template>
						</el-table-column>
						<el-table-column prop="refundAmountTime" label="退款时间" width="150">
							<template #default="{ row, $index }">
								<el-date-picker v-model="row.refundAmountTime" type="date" placeholder="日期" clearable format="YYYY/MM/DD" value-format="YYYY-MM-DD" style="width: 100%" />
							</template>
						</el-table-column>
						<el-table-column label="操作">
							<template #default="scope">
								<el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-row>
		</el-form>
		<div style="display: flex; justify-content: center; width: 100%; padding-top: 20px">
			<el-button @click="handleCancel">取消</el-button>
			<el-button type="primary" @click="handleSubmit">提交</el-button>
		</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { decimal } from '/@/utils/decimal';
import { ElMessageBox, ElMessage } from 'element-plus';
import { UpdatePurchaseReturnOutWare, GetPurchaseReturnOutWare } from '/@/api/cwManager/purchaseReturnOutWare';
import { cloneDeep } from 'lodash-es';
const ruleFormRef = ref();
const props = defineProps({
	data: {
		type: Object as () => {
			businessId: string;
			brandName: string;
			supplier: string;
			buyNo: string;
			refundChangeGoodsAmount: string;
			shippingFee: string;
			isRefundChangeGoods: string | number;
			instanceId: string;
			dtls: Array<{
				refundAmountTime: string;
				collectUser: string;
				collectAccount: string;
				collectType: string;
				totalRefundAmount: string;
				instanceId: string;
			}>;
		},
		default: () => ({}),
	},
	bankList: {
		type: Array as () => { label: string; value: string; id: string; bankType: string }[],
		default: () => [],
	},
});
const ruleForm = ref({
	businessId: '',
	brandName: '',
	supplier: '',
	buyNo: '',
	refundChangeGoodsAmount: '',
	shippingFee: '',
	isRefundChangeGoods: 0,
	instanceId: '',
	dtls: [] as Array<{
		refundAmountTime: '';
		collectUser: string;
		collectAccount: string;
		collectType: string;
		totalRefundAmount: string;
		instanceId: string;
	}>,
});

const rules = ref({
	supplier: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
	buyNo: [{ required: true, message: '请输入采购单号', trigger: 'blur' }],
	refundChangeGoodsAmount: [{ required: true, message: '请输入退换货金额', trigger: 'blur' }],
	shippingFee: [{ required: true, message: '请输入运费', trigger: 'blur' }],
	isRefundChangeGoods: [{ required: true, message: '请选择是否退换货', trigger: 'blur' }],
});
const emit = defineEmits(['close', 'closeSuccess']);
const loading = ref(false);

onMounted(async () => {
	ruleFormRef.value.clearValidate();
	ruleFormRef.value.resetFields();
	const clonedData = cloneDeep(props.data);
	const params = {
		instanceId: clonedData.instanceId,
	};
	const { data: data1, success } = await GetPurchaseReturnOutWare(params);
	ruleForm.value = {
		businessId: clonedData.businessId,
		brandName: clonedData.brandName,
		supplier: clonedData.supplier,
		buyNo: clonedData.buyNo,
		refundChangeGoodsAmount: clonedData.refundChangeGoodsAmount,
		shippingFee: clonedData.shippingFee,
		isRefundChangeGoods: clonedData.isRefundChangeGoods ? Number(clonedData.isRefundChangeGoods) : 0,
		dtls: data1?.dtls ?? [],
		instanceId: clonedData.instanceId,
	};
});

const handleChange = (row: any, index: number) => {
	const bank = props.bankList.find((item) => item.label == row.collectUser);
	ruleForm.value.dtls[index].collectAccount = bank?.value ?? '';
	ruleForm.value.dtls[index].collectType = bank?.bankType == '支付宝' ? '支付宝' : '银行卡';
};

// 校验行是否填写完整
const validateDtlsComplete = () => {
	for (let i = 0; i < ruleForm.value.dtls.length; i++) {
		const item = ruleForm.value.dtls[i];
		if (!item.collectUser || !item.refundAmountTime || item.totalRefundAmount === '' || item.totalRefundAmount === null || item.totalRefundAmount === undefined) {
			ElMessage.warning(`第${i + 1}行的收款人、退款时间和厂家实际退款总金额必须填写完整！`);
			return false;
		}
	}
	return true;
};

const addRow = () => {
	if (ruleForm.value.dtls.length == 20) {
		ElMessage.warning('最多只能添加20条记录!');
		return;
	}

	if (!validateDtlsComplete()) {
		return;
	}

	ruleForm.value.dtls.push({
		collectUser: '',
		collectAccount: '',
		collectType: '',
		totalRefundAmount: '0',
		instanceId: props.data.instanceId,
		refundAmountTime: '',
	});
};

const handleDelete = (index: number, row: any) => {
	ElMessageBox.confirm('是否删除?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ruleForm.value.dtls.splice(index, 1);
		})
		.catch(() => {});
};

const handleSubmit = async () => {
	ruleFormRef.value.validate().then(async () => {
		if (ruleForm.value.isRefundChangeGoods == 0 && ruleForm.value.dtls.length == 0) {
			ElMessage.warning('请至少添加一条付款明细信息!');
			return;
		}

		if (!validateDtlsComplete()) {
			return;
		}

		ElMessageBox.confirm('是否提交?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				loading.value = true;
				const { success } = await UpdatePurchaseReturnOutWare(ruleForm.value);
				if (success) {
					ElMessage.success('提交成功');
					emit('closeSuccess', true);
				}
				loading.value = false;
			})
			.catch(() => {});
	});
};

const handleCancel = () => {
	emit('close');
};
</script>

<style scoped lang="scss">
.outbound-edit-container {
	padding: 20px;

	.outbound-form {
		max-width: 1200px;
		width: 100%;
		margin: 0 auto;

		:deep(.el-row) {
			margin: 20px 0;
		}
	}
}

.publicCss {
	width: 85%;
}
</style>
