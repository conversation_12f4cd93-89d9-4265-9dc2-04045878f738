<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="ancillaryChecking202503291620"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				orderBy="date"
				:isAsc="false"
				:queryApi="GetFuZhuCheck"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				v-loading="pageLoading"
			>
				<template #otherPlatformWithDraw="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="otherPlatformWithDraw" />
				</template>
				<template #withDrawSelfUse="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="withDrawSelfUse" />
				</template>
				<template #companyBalance="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="companyBalance" />
				</template>
				<template #withDrawBalance="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="withDrawBalance" />
				</template>
				<template #otherHandleFee="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="otherHandleFee" />
				</template>
				<template #handleExpressFee="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="handleExpressFee" />
				</template>
				<template #honestCreditPurchase="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="honestCreditPurchase" />
				</template>
				<template #handleYueJieFuLiao="{ row }">
					<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" :min="0" :max="99999999" field="handleYueJieFuLiao" />
				</template>
				<template #remark="{ row }">
					<el-input v-if="row.statusVerify" v-model.trim="row.remark" placeholder="请输入备注" style="width: 100%" clearable maxlength="100" />
					<span v-else>{{ row.remark }}</span>
				</template>
			</vxetable>

			<el-dialog v-model="detailVisible" :title="dynamicHeading ? '编辑' : '新增'" width="600" draggable overflow style="margin-top: -18vh !important" :close-on-click-modal="false">
				<div v-loading="listLoading">
					<div style="padding: 10px 0">
						<el-button type="primary" @click="openAdd">新增一行</el-button>
					</div>
					<el-table :data="statusTableData" style="width: 100%" height="400" border :summary-method="getSummaries" show-summary>
						<el-table-column type="index" width="50" />
						<el-table-column prop="project" label="项目" width="170" show-overflow-tooltip>
							<template #default="scope">
								<el-input v-if="scope.row.statusVerify" v-model.trim="scope.row.project" placeholder="请输入项目" style="width: 100%" clearable maxlength="50" />
								<span v-else>{{ scope.row.project }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="amount" label="金额" width="150" show-overflow-tooltip>
							<template #default="scope">
								<editableNumber v-if="scope.row.statusVerify" :model-value="scope.row" @update:model-value="(val: any) => Object.assign(scope.row, val)" field="amount" />
								<span v-else>{{ formatThousands(scope.row.amount) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" fixed="right">
							<template #default="{ row, $index }">
								<el-button size="small" type="primary" v-if="!row.statusVerify" text @click="openEdit(row, $index)">编辑</el-button>
								<el-button size="small" type="primary" v-if="row.statusVerify" text @click="openSave(row, $index)">保存</el-button>
								<!-- <el-button size="small" type="primary" v-if="row.statusVerify" text @click="openCancel(row, $index)">取消</el-button> -->
								<el-button size="small" type="primary" text @click="delApprovalFlow(row, $index)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<template #footer>
					<el-button @click="detailVisible = false">取消</el-button>
					<el-button type="primary" @click="onSaveMethod">保存</el-button>
				</template>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, h, VNode, defineEmits } from 'vue';
import { GetFuZhuCheck, ExportFuZhuCheck, EditFuZhuCheck, GetFuZhuCheckOtherData, EditFuZhuCheckOtherData } from '/@/api/cwManager/cwFundsDailyBalance';
const editableNumber = defineAsyncComponent(() => import('/@/components/yhCom/editableNumber.vue'));
import { dateShortcuts } from '/@/utils/shortcuts';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';
import { decimal } from '/@/utils/decimal';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const detailVisible = ref(false);
const dynamicHeading = ref(false);
const listLoading = ref(false);
const statusTableData = ref<any[]>([]);
const dateTime = ref('');
const table = ref();
const query = ref({
	startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
});
const tableData = ref<any[]>([]);
const backupTableData = ref<any[]>([]);

const getSummaries = (param: any) => {
	const { columns, data } = param;
	const sums: (string | number | VNode)[] = []; // 声明类型
	columns.forEach((column: any, index: any) => {
		if (index === 0) {
			sums[index] = h('div', ['合计']);
			return;
		}
		if (['amount'].includes(column.property)) {
			const values = data.map((item: any) => Number(item[column.property]));
			if (!values.every((value: any) => Number.isNaN(value))) {
				sums[index] = h('b', [
					numToMoney(
						values.reduce((prev: number, curr: any) => {
							const value = Number(curr);
							if (!Number.isNaN(value)) {
								return Number(decimal(prev, value, 2, '+'));
							}
							return prev;
						}, 0)
					),
				]);
			} else {
				sums[index] = '';
			}
		} else {
			sums[index] = '';
		}
	});
	return sums;
};

const numToMoney = (num: any) => {
	// 检查输入是否为空
	if (num === null || num === undefined || num === '') {
		return '-';
	}
	// 将输入的数字转换为字符串
	const str = num.toString();
	// 分离整数部分和小数部分
	const [integerPart, decimalPart] = str.split('.');
	// 处理整数部分，千分位分隔
	const formattedInteger = integerPart
		.split('')
		.reverse()
		.reduce((prev: any, next: any, index: any) => {
			return (index % 3 ? next : next + ',') + prev;
		});

	// 合并整数部分和小数部分
	return decimalPart ? `${formattedInteger}.${decimalPart}` : `${formattedInteger}.00`;
};

const exportProps = async () => {
	pageLoading.value = true;
	await ExportFuZhuCheck({ ...query.value, ...table.value.query })
		.then((data: any) => {
			pageLoading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '辅助核算导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			pageLoading.value = false;
		});
};

const onSaveMethod = async () => {
	const emptyProjectRows = statusTableData.value
		.map((item, index) => ({ index: index + 1, project: item.project.trim() })) // 获取索引（+1 让索引从 1 开始）
		.filter((item) => !item.project); // 过滤出 project 为空的项
	if (emptyProjectRows.length > 0) {
		const rowNumbers = emptyProjectRows.map((item) => `第${item.index}行`).join('、');
		ElMessage.error(`${rowNumbers} 的项目为空，请填写`);
		return;
	}
	statusTableData.value.forEach((item) => {
		item.date = dayjs(item.date).format('YYYY-MM-DD');
		detail_FIELDS.forEach((field) => {
			delete item[`${field}_Backup`];
			delete item.statusVerify;
		});
	});
	const { success } = await EditFuZhuCheckOtherData(statusTableData.value);
	if (success) {
		detailVisible.value = false;
		getList();
		ElMessage.success('保存成功');
	}
};

// 明细查询
const onDetailDataProcess = async (row: any) => {
	dateTime.value = dayjs(row.date).format('YYYY-MM-DD');
	const params = {
		startDate: dateTime.value,
		endDate: dateTime.value,
	};
	const { data, success } = await GetFuZhuCheckOtherData(params);
	if (success) {
		statusTableData.value = data.list;
		statusTableData.value.forEach((item: any) => {
			item.statusVerify = false;
			item.date = dateTime.value;
			detail_FIELDS.forEach((field) => {
				item[`${field}_Backup`] = item[field];
			});
		});
		detailVisible.value = true;
	}
};

// 明细字段
const detail_FIELDS = ['amount', 'project'] as const;

const openAdd = () => {
	const hasEmptyProject = statusTableData.value.some((item) => !item.project.trim());
	if (hasEmptyProject) {
		ElMessage.warning('填写完整后再新增');
		return; // 终止新增
	}
	statusTableData.value.push({
		amount: 0,
		project: '',
		statusVerify: true,
		date: dateTime.value,
		amount_Backup: 0,
		project_Backup: '',
	});
};

// 明细编辑
const openEdit = (row: any, index: any) => {
	statusTableData.value[index].statusVerify = true;
};

// 明细保存
const openSave = debounce(async (row: any, index: any) => {
	if (!row.project) {
		ElMessage.warning('项目不能为空');
		return;
	} else if (row.amount === undefined || row.amount === '') {
		ElMessage.warning('金额不能为空');
		return;
	}
	detail_FIELDS.forEach((field) => {
		statusTableData.value[index][`${field}_Backup`] = statusTableData.value[index][field];
	});
	ElMessage.success('操作成功');
	statusTableData.value[index].statusVerify = false;
}, 1000); // 防抖时间1秒

// 明细删除
const delApprovalFlow = (row: any, i: any) => {
	statusTableData.value.splice(i, 1);
};

// 明细取消
const openCancel = (row: any, i: number) => {
	if (row.project === '' && row.amount === 0 && i === statusTableData.value.length - 1) {
		statusTableData.value.splice(i, 1);
	} else {
		detail_FIELDS.forEach((field) => {
			statusTableData.value[i][field] = statusTableData.value[i][`${field}_Backup`];
		});
		statusTableData.value[i].statusVerify = false;
	}
};

// 格式化金额
const formatThousands = (value: number) => {
	if (!value) return '0';
	const numStr = typeof value === 'number' ? value.toFixed(2) : String(value);
	return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const findRowIndex = (row: any) => tableData.value.findIndex((item: any) => item.date === row.date);

// 更新表格数据并同步 UI
const updateTableData = (row: any, status: boolean) => {
	const index = findRowIndex(row);
	if (index !== -1) {
		tableData.value[index].statusVerify = status;
		table?.value.onAssignedData(tableData.value);
	}
};

// 编辑
const handleEdit = (row: any) => updateTableData(row, true);

// 取消
const handleCancel = (row: any) => {
	const index = findRowIndex(row);
	if (index !== -1) {
		// 恢复备份数据
		BACKUP_FIELDS.forEach((field) => {
			tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
		});
		updateTableData(row, false);
	}
};

// 保存
const handleSave = async (row: any) => {
	let a = BACKUP_FIELDS;
	const index = findRowIndex(row);
	if (index === -1) return;
	updateTableData(row, false);
	// 删除备份字段
	BACKUP_FIELDS.forEach((field) => {
		delete row[`${field}_Backup`];
		if (row[field] === null && field !== 'remark') {
			row[field] = 0;
		}
	});
	try {
		const response = await EditFuZhuCheck({ ...row });
		// 处理 API 返回的数据
		if (response.success) {
			backupTableData.value = [...tableData.value]; // 深拷贝，避免数据引用问题
			ElMessage.success('保存成功');
			// 创建新的备份
			BACKUP_FIELDS.forEach((field) => {
				tableData.value[index][`${field}_Backup`] = tableData.value[index][field];
			});
			table?.value.getList();
		} else {
			a.forEach((field) => {
				tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
			});
		}
	} catch (error) {
		console.error('API 请求失败:', error);
		ElMessage.error('保存请求失败，请稍后再试！');
	}
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const BACKUP_FIELDS = ['otherPlatformWithDraw', 'withDrawSelfUse', 'companyBalance', 'withDrawBalance', 'remark'] as const;

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.date = dayjs(item.date).format('YYYY-MM-DD');
		item.statusVerify = false;
		// 创建备份
		BACKUP_FIELDS.forEach((field) => {
			item[`${field}_Backup`] = item[field];
		});
	});
	tableData.value = data.data.list;
	backupTableData.value = JSON.parse(JSON.stringify(data.data.list));
	callback(data);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'date', title: '日期', width: '140', formatter: 'formatDate' },
	{ sortable: true, field: 'handleYueJieFuLiao', title: '应付月结、辅料', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'honestCreditPurchase', title: '诚信赊采购款', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'handleExpressFee', title: '应付快递费', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'otherHandleFee', title: '其他应付款', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'otherPlatformWithDraw', title: '其他平台提现', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'withDrawSelfUse', title: '提现自用', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{
		sortable: true,
		field: 'otherData',
		title: '总经办-其他数据',
		width: '120',
		formatter: 'fmtAmt2',
		align: 'right',
		type: 'click',
		handle: (row: any) => onDetailDataProcess(row),
	},
	{ sortable: true, field: 'withDrawBalance', title: '提现账户余额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'companyBalance', title: '公司账户余额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'remark', title: '备注', width: '200' },
	{
		align: 'center',
		type: 'btnList',
		width: '170',
		fixed: 'right',
		title: '操作',
		field:'20250608094005',
		btnList: [
			{ title: '编辑', handle: handleEdit, isDisabled: (row) => row.statusVerify },
			{ title: '保存', handle: handleSave, isDisabled: (row) => !row.statusVerify },
			{ title: '取消', handle: handleCancel, isDisabled: (row) => !row.statusVerify },
		],
	},
]);
</script>

<style scoped lang="scss">
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
