<template>
	<div style="width: 100%; height: 100%">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:startDate="query.startOccurenceTime" v-model:endDate="query.endOccurenceTime"
						class="publicCss" style="width: 200px" />
					<el-select v-model="query.busAccount" placeholder="别名" class="publicCss itemCss" filterable
						clearable>
						<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<!-- <el-input v-model="query.businessId" class="publicCss" placeholder="钉钉流程号" clearable maxlength="50" /> -->
					<el-input v-model.trim="query.account" class="publicCss itemCss" placeholder="账号" clearable
						maxlength="50" />
					<div style="height: 35px; margin-top: 7px">
						<el-cascader v-model="query.feeTypeList" :options="optionsExpense" :props="propsExpense"
							placeholder="请选择收支类型" class="publicCss custom-cascader" style="width: 190px" collapse-tags
							collapse-tags-tooltip clearable />
					</div>
					<!-- <el-select v-model="query.feeTypeList" placeholder="收支类型" class="publicCss itemCss" filterable clearable multiple collapse-tags>
						<el-option key="未知" label="未知" value="未知" />
						<el-option v-for="item in expenseType" :key="item.value" :label="item.label" :value="item.value" />
					</el-select> -->
					<!-- <el-select v-model="query.type" placeholder="收支类型" style="width: 100px; margin: 0 0 5px 0" filterable clearable @change="disburseChange">
						<el-option key="收入" label="收入" value="收入" />
						<el-option key="支出" label="支出" value="支出" />
					</el-select>
					<el-select v-model="query.sumType" placeholder="费用类型汇总" style="width: 120px; margin: 0 0 5px 0" filterable clearable @change="collectChange" @visible-change="collectVisiblechange">
						<el-option v-for="item in expenseCollect" :key="item" :label="item" :value="item" />
					</el-select>
					<el-select v-model="query.name" placeholder="收支类型" style="width: 100px; margin: 0 5px 5px 0" filterable clearable @visible-change="expenseTypeVisiblechange">
						<el-option v-for="item in expenseTypeList" :key="item" :label="item" :value="item" />
					</el-select> -->
					<el-select v-model="query.flowType" placeholder="交易类型" clearable
						style="width: 90px; margin: 0 0 5px 0">
						<el-option label="收入" value="收入" />
						<el-option label="支出" value="支出" />
					</el-select>
					<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2"
						style="width: 200px; margin: 0 5px 5px 0" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
					<el-select v-model="query.area" placeholder="归属区域" class="publicCss itemCss" filterable clearable>
						<el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.bankType" placeholder="行名" class="publicCss itemCss" filterable clearable>
						<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
					<el-select v-model="query.status" placeholder="状态" class="publicCss itemCss" filterable clearable>
						<el-option label="已匹配" value="是" />
						<el-option label="未匹配" value="否" />
						<el-option label="已忽略" value="忽略" />
					</el-select>
					<el-select v-model="query.approverStatus" placeholder="审批状态" clearable class="publicCss itemCss">
						<el-option label="待审核" value="待审核" />
						<el-option label="已审核" value="已审核" />
					</el-select>
					<el-select v-model="query.isIgnore" placeholder="抵消忽略" clearable class="publicCss itemCss">
						<el-option label="已存在抵消" :value="false" />
						<el-option label="未存在抵消" :value="true" />
					</el-select>
					<!-- <numRange
						v-model:maxNum="query.maxInAmount"
						v-model:minNum="query.minInAmount"
						:precision="2"
						class="publicCss"
						style="width: 200px"
						minPlaceHolder="收入金额最小值"
						maxPlaceHolder="收入金额最大值"
					/>
					<numRange
						v-model:maxNum="query.maxOutAmount"
						v-model:minNum="query.minOutAmount"
						:precision="2"
						class="publicCss"
						style="width: 200px"
						minPlaceHolder="支出金额最小值"
						maxPlaceHolder="支出金额最大值"
					/> -->
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-button @click="onResetMethod" type="primary">重置</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="************" :tableCols="tableCols" :query="query" showsummary
					:query-api="QueryBankFlow" isNeedCheckBox @select="select">
					<template #toolbar_buttons>
						<el-button @click="importProps" type="primary">流水导入</el-button>
						<el-button @click="universalImport" type="primary">通用导入</el-button>
						<el-button @click="downLoadFile" type="primary">通用模版下载</el-button>
						<el-button @click="reversal" type="primary">对账冲正</el-button>
						<el-button @click="onBatchIgnore" type="primary">批量忽略</el-button>
						<el-button @click="onBatchModification" type="primary"
							:disabled="checkType">批量修改费用类型</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
					</template>
				</vxetable>
			</template>
		</Container>

		<el-dialog title="流水导入" v-model="importVisible" width="35%" draggable overflow v-loading="importLoading" style="margin-top: -38vh !important;">
			<div class="topCss" style="margin-bottom: 20px">
				<el-date-picker v-model="ruleForm.importDate" type="date" placeholder="日期" :clearable="false"
					style="width: 150px" format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="publicCss" />
				<el-select v-model="ruleForm.account" filterable remote reserve-keyword placeholder="卡号" clearable
					:remote-method="remoteMethod" @change="changeAccount" class="publicCss" :loading="remoteLoading"
					style="width: 200px">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model="ruleForm.bankName" class="publicCss" style="width: 100px" clearable maxlength="50"
					disabled placeholder="行别" />
				<el-select v-model="ruleForm.isAlipay" class="publicCss" placeholder="交易类型" style="width: 100px"
					v-if="ruleForm.bankName == '支付宝'" clearable>
					<el-option label="余额" value="余额" />
					<el-option label="余额宝" value="余额宝" />
					<el-option label="诚信赊" value="诚信赊" />
				</el-select>
			</div>
			<div style="display: flex; flex-direction: column; justify-content: center">
				<el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
					:on-remove="removeFile" :file-list="fileList" accept=".xlsx,.xls,.csv" :http-request="uploadFile">
					<el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
						<el-button size="small" type="primary">点击上传</el-button>
					</el-tooltip>
				</el-upload>
			</div>
			<div class="btnGroup">
				<el-button type="primary" @click="importVisible = false">取消</el-button>
				<el-button type="primary" @click="submit" v-reclick>确定</el-button>
			</div>
		</el-dialog>

		<el-dialog title="通用导入" v-model="universalVisible" width="35%" draggable overflow v-loading="universalLoading" style="margin-top: -38vh !important;">
			<!-- <template #header="{ close, titleId }">
                <div class="header-title">
					<span :id="titleId" class="title-text"><span>通用导入</span></span>
					<span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
                </div>
            </template> -->
			<div class="topCss" style="margin-bottom: 20px">
				<el-date-picker v-model="universalForm.importDate" type="date" placeholder="日期" :clearable="false"
					style="width: 150px" format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="publicCss" />
				<el-select v-model="universalForm.account" filterable remote reserve-keyword placeholder="卡号" clearable
					:remote-method="remoteMethod" @change="changeAccount" class="publicCss" :loading="unLoading"
					style="width: 200px">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model="universalForm.bankName" class="publicCss" style="width: 100px" clearable
					maxlength="50" disabled placeholder="行别" />
			</div>
			<div style="display: flex; flex-direction: column; justify-content: center">
				<el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
					:on-remove="unremoveFile" :file-list="unfileList" accept=".xlsx,.xls,.csv"
					:http-request="unuploadFile">
					<el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
						<el-button size="small" type="primary">点击上传</el-button>
					</el-tooltip>
				</el-upload>
			</div>
			<div class="btnGroup">
				<el-button type="primary" @click="universalVisible = false">取消</el-button>
				<el-button type="primary" @click="unsubmit" v-reclick>确定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="editVisible" title="编辑" width="80%" draggable overflow :close-on-click-modal="false" @close="onCloseMethod">
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" style="height: 100%">
				<el-tab-pane label="费用数据" name="first">
					<div style="display: flex; gap: 10px;padding-top: 10px;">
						<dataRange v-model:startDate="editQuery.startDate" v-model:endDate="editQuery.endDate"
							:clearable="false" class="publicCss" style="width: 300px" />
						<el-input v-model="editQuery.account" style="width: 150px" placeholder="卡号" clearable
							maxlength="50" />
						<el-input v-model="editQuery.businessId" style="width: 150px" placeholder="钉钉流程号" clearable
							maxlength="50" />
						<numRange v-model:maxNum="editQuery.maxAmount" v-model:minNum="editQuery.minAmount"
							:precision="2" style="width: 230px" minPlaceHolder="金额(元)最小值" maxPlaceHolder="金额(元)最大值" />
						<el-select v-model="editQuery.status" placeholder="状态" class="publicCss" clearable
							style="width: 150px">
							<el-option label="已匹配" value="是" />
							<el-option label="未匹配" value="否" />
							<el-option label="已忽略" value="忽略" />
						</el-select>
						<el-button type="primary" @click="onInquire" v-reclick>查询</el-button>
						<el-button type="primary" @click="onEditMethod" v-reclick v-if="!storageVisible" style="margin-left: 1px;">编辑</el-button>
						<el-button type="primary" @click="onStorageMethod" v-reclick v-if="storageVisible" style="margin-left: 1px;">保存</el-button>
						<el-button @click="onCancelMethod" v-reclick v-if="storageVisible" style="margin-left: 1px;">取消</el-button>
					</div>
					<div style="height: 550px; width: 100%; padding-bottom: 10px;">
						<vxetable ref="table1" id="202410011933" :tableCols="editTableCols" isNeedCheckBox @visibleMethod="visibleMethod"
							v-if="editVisible" @select="checkboxChange" :query="editQuery" :pageSize="50" showsummary @sortEvent="sortEvent"
							:isDisableCheckBox="false" :query-api="QueryFeeData" isNeedDisposeProps @disposeProps="disposeProps">
							<template #amount="{ row }">
								<div v-if="row.toggle">
									<el-input-number v-model="row.amount" placeholder="请输入" autocomplete="off" :min="0" :max="9999999" :precision="2" :controls="false" style="width: 100%" />
								</div>
								<span v-else>{{ row.amount }}</span>
							</template>
						</vxetable>
					</div>
				</el-tab-pane>
				<el-tab-pane label="退款数据" name="second">
					<div style="display: flex; gap: 10px;padding-top: 10px;">
						<dataRange v-model:startDate="editQuery2.startDate" v-model:endDate="editQuery2.endDate"
							:clearable="false" class="publicCss" style="width: 300px" />
						<el-input v-model="editQuery2.account" style="width: 150px" placeholder="收款账号" clearable
							maxlength="50" />
						<numRange v-model:maxNum="editQuery2.maxAmount" v-model:minNum="editQuery2.minAmount"
							:precision="2" style="width: 230px" minPlaceHolder="金额(元)最小值" maxPlaceHolder="金额(元)最大值" />
						<!-- <el-select v-model="editQuery2.status" placeholder="状态" class="publicCss" clearable
							style="width: 150px">
							<el-option label="已匹配" value="是" />
							<el-option label="已忽略" value="忽略" />
						</el-select> -->
						<el-button type="primary" @click="onInquiretwo" v-reclick>查询</el-button>
					</div>
					<div style="height: 550px; width: 100%; padding-bottom: 10px;">
						<vxetable v-if="activeName == 'second' && editVisible" ref="table2" id="*************"
							:tableCols="editTableColstwo" :query="editQuery2" :pageSize="50" showsummary
							:isDisableCheckBox="false" isNeedCheckBox @select="checkboxChange"
							:query-api="QueryPurchaseOrderRefundRecordList" />
					</div>
				</el-tab-pane>
			</el-tabs>

			<div style="display: flex; justify-content: center; margin-top: 20px">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="editSubmit" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="editDialog" :title="'查看'" width="1000" draggable overflow>
			<div style="height: 350px" v-if="showOneDialog">
				<div style="display: flex; justify-content: space-between; margin-bottom: 15px">
					<div>
						<span>行名:</span>
						{{ statusForm.bankType }}
					</div>
					<div>
						<span>账号:</span>
						{{ statusForm.account }}
					</div>
					<div>
						<span>收入/支出金额:</span>
						{{ statusForm.amount }}
					</div>
				</div>
				<el-table :data="statusTableData" v-if="statusVerify == 1" style="width: 100%" height="300" border>
					<el-table-column prop="account" label="银行卡号" show-overflow-tooltip />
					<el-table-column prop="bankType" label="银行" width="130" show-overflow-tooltip />
					<el-table-column prop="inAmount" label="收入金额(元)" width="130" show-overflow-tooltip />
					<el-table-column prop="outAmount" label="支出金额(元)" width="130" show-overflow-tooltip />
					<el-table-column prop="toAccount" label="对方卡号" width="130" show-overflow-tooltip />
					<el-table-column prop="toAccountName" label="对方用户名" width="130" show-overflow-tooltip />
					<el-table-column prop="occurenceTime" label="付款时间" width="130" show-overflow-tooltip />
				</el-table>
				<el-table :data="statusTableData" v-else-if="statusVerify == 2" style="width: 100%" height="300" border>
					<el-table-column prop="businessId" label="钉钉流程号" show-overflow-tooltip />
					<el-table-column prop="title" label="流程名" width="220" show-overflow-tooltip />
					<el-table-column prop="processCreateUserName" label="发起人姓名" width="100" show-overflow-tooltip />
					<el-table-column prop="amount" label="金额(元)" width="100" show-overflow-tooltip />
					<el-table-column prop="payType" label="付款方式" width="100" show-overflow-tooltip />
					<el-table-column prop="processRemark" label="流程备注" width="200" show-overflow-tooltip />
				</el-table>
				<el-table :data="statusTableData" v-else-if="statusVerify == 3" style="width: 100%" height="300" border>
					<el-table-column prop="refundDateTime" label="退款时间" show-overflow-tooltip />
					<el-table-column prop="returnAmount" label="退款金额" show-overflow-tooltip />
					<el-table-column prop="payBankType" label="收款方式" width="220" show-overflow-tooltip />
					<el-table-column prop="payBankAccountName" label="别名" width="100" show-overflow-tooltip />
					<el-table-column prop="payBankAccount" label="收款账号" width="100" show-overflow-tooltip />
					<!-- <el-table-column prop="payType" label="付款方式" width="100" show-overflow-tooltip />
					<el-table-column prop="processRemark" label="流程备注" width="200" show-overflow-tooltip /> -->
				</el-table>
			</div>

			<div style="display: flex; justify-content: center">
				<el-button style="width: 80px" @click="editDialog = false">取消</el-button>
			</div>
		</el-dialog>

		<el-dialog v-model="modificationDialog" :title="'批量修改费用类型'" width="450" draggable overflow>
			<div style="height: 200px; display: flex; align-items: center; padding-bottom: 50px">
				<span>费用类型：</span>
				<el-select v-model="modificationFeeType" placeholder="请选择费用类型" class="publicCss" filterable clearable
					style="width: 75%">
					<el-option v-for="item in expenseType" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div style="display: flex; justify-content: center">
				<el-button @click="modificationDialog = false">取消</el-button>
				<el-button type="primary" @click="modificationSave" v-reclick="1000">确定</el-button>
			</div>
		</el-dialog>

		<el-drawer v-model="ProcessApprovalsDrawer" title="查看流程">
			<approvalProcess :viewInfo="viewInfo" v-if="ProcessApprovalsDrawer" @close="ProcessApprovalsDrawer = false"
				@getList="getList" />
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="">
import { nextTick, ref, defineAsyncComponent, onMounted } from 'vue';
import { bankList } from '/@/utils/tools';
import Decimal from 'decimal.js';
import { QueryCashierFeeType, QueryCashierFeeTypeSum } from '/@/api/cwManager/cashierSet';
import {
	GetAllOnlineBank,
	ImportBankFlow,
	ImportBankFlowCommon,
	QueryBankFlow,
	ApproveBankFlow,
	IgnoreBankFlow,
	BankFlowMatch,
	RelieveBankFlow,
	UpdateBankFlow,
	QueryBankFlowMatch,
	QueryFeeDataMatch,
	BulkIgnoreBankFlow,
	UnIgnoreBankFlow,
	BulkUpDateFeeType,
	QueryBusDataMatch,
	ExportBankFlow,
	UpdateBankFlowToReturnAmount,
} from '/@/api/cwManager/bankFlow';
import { QueryPurchaseOrderRefundRecordList, UpDateFeeDataAmount } from '/@/api/cwManager/feeData';

import { ElMessageBox } from 'element-plus';
import { QueryFeeData } from '/@/api/cwManager/feeData';
import { QueryOnlineBankSetSelect, QueryOnlineBankSet, QueryCashierFeeTypeCasCade } from '/@/api/cwManager/cashierSet';
import dayjs from 'dayjs';
import { areaList } from '/@/utils/tools';
const activeName = ref('first');
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const approvalProcess = defineAsyncComponent(() => import('/@/components/yhCom/approvalProcess.vue'));
const importVisible = ref(false); //导入弹窗
const importLoading = ref(false); //导入loading
const editVisible = ref(false); //编辑弹窗
const checkType = ref(false);
const statusVerify = ref(1);
const modificationDialog = ref(false);
const storageVisible = ref(false);
const modificationFeeType = ref('');
const file = ref(); //上传文件
const fileList = ref([]); //上传文件列表
const options = ref<Public.options[]>([]);
const nameList = ref<Public.options[]>([]);
const disburseList = ref<Public.options[]>([]);
const expenseCollect = ref<Public.options[]>([]);
const expenseTypeList = ref<Public.options[]>([]);
const optionsExpense = ref([]);
const propsExpense = ref({
	multiple: true,
	emitPath: false,
});
const feeTypeList = ref([]);
const expenseType = ref<Public.options[]>([]);
const selectList = ref();
const table = ref();
const remoteLoading = ref(false);
const table1 = ref();
const table2 = ref();
const editDialog = ref(false);
const universalVisible = ref(false);
const universalLoading = ref(false);
const ProcessApprovalsDrawer = ref(false);
const unLoading = ref(false);
const unfileList = ref([]);
const statusTableData = ref([]);
const tableData = ref<CheckboxItem[]>([]);
interface CheckboxItem {
	instanceId: string;
	id: string | number;
	toggle: boolean;
	amountBackup: string;
	amount: string;
}
const checkboxList = ref<CheckboxItem[]>([]);
const statusForm = ref({
	bankType: '',
	account: '',
	amount: '',
});

const query = ref({
	startOccurenceTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endOccurenceTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	businessId: '',
	area: '',
	bankType: '',
	status: '',
	maxOutAmount: null,
	minOutAmount: null,
	maxInAmount: null,
	minInAmount: null,
	maxAmount: null,
	minAmount: null,
	account: '',
	feeTypeList: [],
	flowType: '',
	type: '',
	sumType: '',
	name: '',
	busAccount: '',
	approverStatus: '',
	isIgnore: false,
});
const editQuery = ref({
	status: '否',
	maxAmount: undefined,
	minAmount: undefined,
	businessId: '',
	startDate: '',
	endDate: '',
	account: '',
});
const editQuery2 = ref({
	status: '',
	maxAmount: undefined,
	minAmount: undefined,
	businessId: '',
	startDate: '',
	endDate: '',
	account: '',
});
const ruleForm = ref({
	bankName: '',
	isAlipay: '',
	account: '',
	importDate: '',
});

const universalForm = ref({
	bankName: '',
	account: '',
	importDate: '',
});

const editInfo = ref({
	businessId: '', //钉钉流程号
	area: '', //归属区域
	type: '', //交易类型
	feeType: '', //收支类型
	bankRemark: '', //备注
	oldBusinessId: '', //原钉钉流程号
	id: '',
});

const viewInfo = ref({
	instanceId: '',
	accountName: '',
	status: '',
});

const showOneDialog = ref(true); //是否显示弹窗一

const visibleMethod = (data: any, callback: any) => {
	callback(data.row.relationStatus == '否' ? true : false);
};

const viewProcess = (row: any) => {
	viewInfo.value = {
		instanceId: row.relationInstanceId,
		accountName: row.payAccount,
		status: row.status,
	};
	ProcessApprovalsDrawer.value = true;
};

const getList = () => {
	if (query.value.maxInAmount != null && query.value.minInAmount != null && query.value.maxInAmount < query.value.minInAmount) {
		return window.$message.error('收入金额最大值不能小于最小值');
	}
	if (query.value.maxOutAmount != null && query.value.minOutAmount != null && query.value.maxOutAmount < query.value.minOutAmount) {
		return window.$message.error('支出金额最大值不能小于最小值');
	}
	table.value.query.currentPage = 1;
	selectList.value = [];
	table.value.getList();
};

const downLoadFile = async () => {
	window.open('/excel/cwManage/通用导入模版.xlsx', '_self');
};

const handleClick = async (val: any) => {
	if (val.props.name === 'first') {
		table1.value.clearSelection();
		await QueryFeeData(editQuery.value);
	} else if (val.props.name === 'second') {
		table2.value.clearSelection();
		await QueryPurchaseOrderRefundRecordList(editQuery2.value);
	}
	checkboxList.value = [];
};

const unremoveFile = (file: any, fileList: any) => {
	file.value = null;
};

const unuploadFile = async (data: any) => {
	file.value = data.file;
};

const loading = ref(false);
// 导出
const exportProps = async () => {
	loading.value = true;
	await ExportBankFlow({ ...query.value })
		.then((data: any) => {
			if (!data.success) {
				return;
			}
			window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
		})
		.catch(() => {
			loading.value = false;
		});
};

const modificationSave = async () => {
	if (!modificationFeeType.value) {
		return window.$message.error('请选择费用类型');
	}
	modificationFeeType.value = modificationFeeType.value.includes('未知') ? '' : modificationFeeType.value;
	ElMessageBox.confirm('是否修改所选数据的类型', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const ids = selectList.value.map((item: any) => item.id);
			const { success } = await BulkUpDateFeeType({ ids, feeType: modificationFeeType.value });
			if (success) {
				window.$message.success('操作成功');
				modificationDialog.value = false;
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const onBatchModification = () => {
	if (!selectList.value || selectList.value.length < 1) return window.$message.error('请选择数据');
	modificationFeeType.value = '';
	modificationDialog.value = true;
};

const onBatchIgnore = () => {
	if (!selectList.value || selectList.value.length < 1) return window.$message.error('请选择数据');
	ElMessageBox.confirm('是否将所选数据忽略?', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const ids = selectList.value.map((item: any) => item.id);
			const { success } = await BulkIgnoreBankFlow({ ids });
			if (success) {
				window.$message.success('操作成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const onResetMethod = () => {
	query.value.businessId = '';
	query.value.area = '';
	query.value.bankType = '';
	query.value.status = '';
	query.value.maxOutAmount = null;
	query.value.minOutAmount = null;
	query.value.maxInAmount = null;
	query.value.minInAmount = null;
	query.value.maxAmount = null;
	query.value.minAmount = null;
	query.value.account = '';
	query.value.feeTypeList = [];
	query.value.flowType = '';
	query.value.busAccount = '';
	getList();
};

const remoteMethod = async (account: string) => {
	remoteLoading.value = true;
	unLoading.value = true;
	const { data, success } = await QueryOnlineBankSetSelect({ account, accountName: '' });
	if (success) {
		options.value = data.map((item: any) => {
			return {
				label: item.accountName + '-' + item.bankType + '-' + item.account,
				value: item.account,
				name: item.bankType,
			};
		});
		remoteLoading.value = false;
		unLoading.value = false;
	} else {
		options.value = [];
		remoteLoading.value = false;
		unLoading.value = false;
	}
};

const universalImport = () => {
	unfileList.value = [];
	universalForm.value = {
		bankName: '',
		account: '',
		importDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	};
	file.value = null;
	universalVisible.value = true;
};

const importProps = () => {
	fileList.value = [];
	ruleForm.value = {
		bankName: '',
		isAlipay: '',
		account: '',
		importDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	};
	file.value = null;
	importVisible.value = true;
};
const uploadFile = async (data: any) => {
	file.value = data.file;
};
const removeFile = (file: any, fileList: any) => {
	file.value = null;
};
const select = (val: any) => {
	selectList.value = val;
	checkType.value = val.some((item: any, index: any, arr: any) => item.flowType !== arr[0].flowType); //checkType为true时，批量修改费用类型按钮不可用
};
const reversal = async () => {
	if (!selectList.value || selectList.value.length < 1) return window.$message.error('请选择数据');
	if (selectList.value.length > 2) return window.$message.error('只能选择两条数据');
	if (selectList.value.length < 2) return window.$message.error('必须选择两条数据');
	ElMessageBox.confirm('是否选择这两条数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await BankFlowMatch({ bankFlowId: selectList.value[0].id, id: selectList.value[1].id });
			if (success) {
				window.$message.success('操作成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const unsubmit = async () => {
	let form = new FormData();
	form.append('upfile', file.value);
	form.append('account', universalForm.value.account);
	form.append('importDate', universalForm.value.importDate);
	if (!file.value) return window.$message.error('请上传文件');
	window.$message.info('正在导入中,请稍后...');
	universalLoading.value = true;
	try {
		const { success } = await ImportBankFlowCommon(form);
		if (success) {
			window.$message.success('导入成功');
			universalVisible.value = false;
			getList();
		} else {
			window.$message.error('导入文件错误');
		}
	} catch (error) {
		window.$message.error('导入失败');
	} finally {
		universalLoading.value = false;
	}
};

const submit = async () => {
	let form = new FormData();
	form.append('upfile', file.value);
	form.append('account', ruleForm.value.account);
	form.append('isAlipay', ruleForm.value.isAlipay);
	form.append('importDate', ruleForm.value.importDate);
	if (ruleForm.value.bankName == '支付宝' && !ruleForm.value.isAlipay) {
		return window.$message.error('请选择费用类型');
	}
	if (!file.value) return window.$message.error('请上传文件');
	window.$message.info('正在导入中,请稍后...');
	importLoading.value = true;
	try {
		const { success } = await ImportBankFlow(form);
		if (success) {
			window.$message.success('导入成功');
			importVisible.value = false;
			getList();
		}
	} catch (error) {
		window.$message.error('导入失败');
	} finally {
		importLoading.value = false;
	}
};
const changeAccount = (val: string) => {
	const bankName = options.value.find((item) => item.value === val)?.name;
	ruleForm.value.bankName = bankName || '';
	universalForm.value.bankName = bankName || '';
	if(val?.includes('诚e赊') || val?.includes('诚信赊')) {
		ruleForm.value.isAlipay = '诚信赊';
	} else {
		ruleForm.value.isAlipay = '';
	}
};

const handleEdit = async (row: any) => {
	editInfo.value = JSON.parse(JSON.stringify(row));
	editInfo.value.oldBusinessId = row.businessId;
	editInfo.value.id = row.id;
	editQuery.value.startDate = '';
	editQuery.value.endDate = '';
	if (row.occurenceTime) {
		editQuery.value.startDate = dayjs(row.occurenceTime).format('YYYY-MM-DD');
		editQuery.value.endDate = dayjs(row.occurenceTime).format('YYYY-MM-DD');
	} else {
		editQuery.value.startDate = dayjs().format('YYYY-MM-DD');
		editQuery.value.endDate = dayjs().format('YYYY-MM-DD');
	}
	editQuery.value.businessId = '';
	editQuery.value.maxAmount = undefined;
	editQuery.value.minAmount = undefined;
	editQuery.value.account = row.account ? row.account : '';
	checkboxList.value = [];
	if (row.inAmount && row.inAmount > 0) {
		editQuery.value.maxAmount = row.inAmount;
		editQuery.value.minAmount = row.inAmount;
	} else if (row.outAmount && row.outAmount > 0) {
		editQuery.value.maxAmount = row.outAmount;
		editQuery.value.minAmount = row.outAmount;
	}

	editQuery2.value.startDate = '';
	editQuery2.value.endDate = '';
	if (row.occurenceTime) {
		editQuery2.value.startDate = dayjs(row.occurenceTime).format('YYYY-MM-DD');
		editQuery2.value.endDate = dayjs(row.occurenceTime).format('YYYY-MM-DD');
	} else {
		editQuery2.value.startDate = dayjs().format('YYYY-MM-DD');
		editQuery2.value.endDate = dayjs().format('YYYY-MM-DD');
	}

	editQuery2.value.businessId = '';
	editQuery2.value.maxAmount = undefined;
	editQuery2.value.minAmount = undefined;
	editQuery2.value.account = row.account ? row.account : '';
	checkboxList.value = [];
	if (row.inAmount && row.inAmount > 0) {
		editQuery2.value.maxAmount = row.inAmount;
		editQuery2.value.minAmount = row.inAmount;
	} else if (row.outAmount && row.outAmount > 0) {
		editQuery2.value.maxAmount = row.outAmount;
		editQuery2.value.minAmount = row.outAmount;
	}
	await QueryPurchaseOrderRefundRecordList(editQuery2.value);
	editVisible.value = true;
	nextTick(() => {
		table1?.value.clearSelection();
	});
};

const updateTableData = (ids:any, toggleValue:any, restoreAmount = false) => {
  tableData.value.forEach(item => {
    if (ids.has(item.id)) {
      item.toggle = toggleValue;
      if (restoreAmount) {
        item.amount = item.amountBackup;
      }
    }
  });
  table1.value.onAssignedData(tableData.value);
};

const onEditMethod = () => {
  if (checkboxList.value.length === 0) {
    return window.$message.error('请选择数据');
  } 
  if (checkboxList.value.length > 1) {
	return window.$message.error('只能编辑一条数据');
  }
  const checkboxIds = new Set(checkboxList.value.map(item => item.id));
  updateTableData(checkboxIds, true); // 更新 toggle 为 true
  storageVisible.value = true;
};

const onCloseMethod = () => {
	storageVisible.value = false;
	editQuery.value.status = '';
	checkboxList.value = [];
};

const onCancelMethod = () => {
	const checkboxIds = new Set(checkboxList.value.map(item => item.id));
	updateTableData(checkboxIds, false, true); // 取消时恢复 amount
	storageVisible.value = false;
};

const onStorageMethod = () => {
  if (checkboxList.value.length === 0) {
    return window.$message.error('请选择数据');
  }
  const checkboxIds = new Set(checkboxList.value.map(item => item.id));
  ElMessageBox.confirm('是否保存所选数据', '确认消息', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async() => {
	  const {data, success} = await UpDateFeeDataAmount({instanceId: checkboxList.value[0].instanceId, amount: checkboxList.value[0].amount});
	  if(!success) return;
	  window.$message.success('保存成功');
      updateTableData(checkboxIds, false); // 保存数据时更新 toggle 为 false
      storageVisible.value = false;
    })
    .catch(() => {
      updateTableData(checkboxIds, false, true); // 取消保存时恢复 amount
      storageVisible.value = false;
    });
};

const onInquire = async () => {
	if (editQuery.value.maxAmount != null && editQuery.value.minAmount != null && editQuery.value.maxAmount < editQuery.value.minAmount) {
		return window.$message.error('金额最大值不能小于最小值');
	}
	table1.value.getList();
	sortEvent([]);
};

const sortEvent = (data: any) => {
	storageVisible.value = false;
	table1.value.clearSelection();
	checkboxList.value = [];
};

const onInquiretwo = async () => {
	table2.value.getList();
};
const editSubmit = async () => {
	if (checkboxList.value.length == 0) {
		return window.$message.error('请选择数据');
	} else if (checkboxList.value.length > 1) {
		return window.$message.error('只能选择一条数据');
	}
	if (activeName.value == 'second') {
		const { success } = await UpdateBankFlowToReturnAmount({ relationBusId: checkboxList.value[0].id, id: editInfo.value.id });
		if (success) {
			window.$message.success('保存成功');
			editVisible.value = false;
			getList();
		}
		return;
	}
	// if (!editInfo.value.businessId) return window.$message.error('请输入钉钉流程号');
	const { success } = await UpdateBankFlow({ instanceId: checkboxList.value[0].instanceId, id: editInfo.value.id });
	if (success) {
		window.$message.success('保存成功');
		editVisible.value = false;
		getList();
	}
};

const handApprovalMethod = async (row: any) => {
	ElMessageBox.confirm('是否审批此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await ApproveBankFlow({ id: row.id });
			if (success) {
				window.$message.success('审批成功');
				getList();
			}
		})
		.catch(() => { });
};

const handleRelieve = async (row: any) => {
	ElMessageBox.confirm('是否解除此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await RelieveBankFlow({ id: row.id });
			if (success) {
				window.$message.success('解除成功');
				getList();
			}
		})
		.catch(() => { });
};

const handleDelete = async (row: any) => {
	ElMessageBox.confirm('此操作将忽略此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await IgnoreBankFlow(row);
			if (success) {
				window.$message.success('忽略成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
};

const rechargeRecord = async (row1: any) => {
	let row = JSON.parse(JSON.stringify(row1));
	statusTableData.value = [];
	if (row.relationBankFlowRecordId) {
		const { data: data1, success } = await QueryBankFlowMatch({ bankFlowId: row.relationBankFlowRecordId });
		if (!success) return;
		statusTableData.value = data1.list;
		statusVerify.value = 1;
	} else if (row.relationInstanceId) {
		const { data: data2, success } = await QueryFeeDataMatch({ instanceId: row.relationInstanceId.toString() });
		if (!success) return;
		statusTableData.value = data2.list;
		statusVerify.value = 2;
	} else if (row.relationBusId) {
		const { data: data2, success } = await QueryBusDataMatch({ relationBusId: row.relationBusId?.toString() });
		if (!success) return;
		statusTableData.value = data2.list;
		statusVerify.value = 3;
	}
	statusForm.value = {
		account: row.account,
		bankType: row.bankType,
		amount: row.outAmount ? row.outAmount : row.inAmount,
	};
	editDialog.value = true;
};

const disburseChange = (val: any) => {
	query.value.sumType = '';
	query.value.name = '';
	expenseCollect.value = [];
	expenseCollect.value = disburseList.value
		.filter((item: any) => item.type === val)
		.map((item: any) => item.sumType)
		.filter(Boolean);
};

const collectVisiblechange = (val: any) => {
	if (val && expenseCollect.value.length == 0) {
		window.$message.error('请先选择收支类型');
		return false;
	}
};

const expenseTypeVisiblechange = (val: any) => {
	if (val && expenseTypeList.value.length == 0) {
		window.$message.error('请先选择费用类型');
		return false;
	}
};

const collectChange = async (val: any) => {
	query.value.name = '';
	const { data, success } = await QueryCashierFeeType({ currentPage: 1, pageSize: ********, sumType: val });
	if (!success) return;
	expenseTypeList.value = data.list.map((item: any) => {
		return item.name ? item.name : '未知';
	});
};

const getFeeType = async () => {
	const { data } = await QueryCashierFeeType({ currentPage: 1, pageSize: ******** });
	feeTypeList.value = data.list;
	expenseType.value = data.list.map((item: any, index: number) => {
		return {
			label: item.name,
			value: item.name === null || item.name === undefined ? index + '未知' : item.name,
		};
	});
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list.map((item: any) => ({ label: item.accountName, value: item.account }));

	const { data: data2, success: success2 } = await QueryCashierFeeTypeSum({ currentPage: 1, pageSize: ******** });
	if (!success2) return;
	disburseList.value = data2;

	const { data: data3, success: success3 } = await QueryCashierFeeTypeCasCade({});
	if (!success3) return;
	optionsExpense.value = data3 || [];
};

//节流
const throttle = (func: (...args: any[]) => void, limit: number) => {
	let lastFunc: ReturnType<typeof setTimeout> | undefined;
	let lastRan: number | undefined;
	return function (...args: any[]) {
		if (lastRan === undefined) {
			func(...args);
			lastRan = Date.now();
		} else {
			clearTimeout(lastFunc);
			lastFunc = setTimeout(
				() => {
					if (Date.now() - (lastRan as number) >= limit) {
						func(...args);
						lastRan = Date.now();
					}
				},
				limit - (Date.now() - (lastRan as number))
			);
		}
	};
};

const checkboxChange = throttle(async (data: any, callback: Function) => {
	if (data.length == 0) {
		checkboxList.value = [];
		return;
	}
	checkboxList.value = data;
}, 1000);

const editTableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'processCode', title: '流程编号', color: (row: any) => (row.isRelation === false ? 'red' : 'black') },
	{ sortable: true, field: 'businessId', title: '钉钉流程号', width: '155' },
	{ sortable: true, field: 'title', title: '流程名', width: '130' },
	{ sortable: true, field: 'feeType', title: '收支类型', width: '80' },
	{ sortable: true, field: 'processCreateUserName', title: '发起人姓名', width: '90' },
	{ sortable: true, field: 'applyReason', title: '申请事由', width: '110' },
	{ sortable: true, field: 'rawAmount', title: '原始金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'amount', title: '金额(元)', width: '110', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'payType', title: '付款方式', width: '90' },
	{ sortable: true, field: 'payBankAccountName', title: '姓名/账户名', width: '110' },
	{ sortable: true, field: 'payBankAccount', title: '卡号', width: '110' },
	{ sortable: true, field: 'payTime', title: '支付日期', formatter: 'formatTime', width: '100' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		align: 'center',
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : ''),
	},
	{ sortable: true, field: 'processRemark', title: '流程备注', width: 'auto' },
]);

const editTableColstwo = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'processCode', title: '流程编号', color: (row: any) => (row.isRelation === false ? 'red' : 'black') },
	{ sortable: true, field: 'refundDateTime', title: '退款时间' },
	{ sortable: true, field: 'returnAmount', title: '退款金额', align: 'right' },
	{ sortable: true, field: 'payBankType', title: '收款方式', width: '250' },
	{ sortable: true, field: 'payBankAccountName', title: '别名' },
	// { sortable: true, field: 'approveDept', title: '发起人部门' },
	{ sortable: true, field: 'payBankAccount', title: '收款账号' },
	// { sortable: true, field: 'applyFeetype', title: '状态', width: '90' },
	// { sortable: true, field: 'amount', title: '金额(元)', width: '90', align: 'right', formatter: 'fmtAmt2' },
	// { sortable: true, field: 'payType', title: '付款方式', width: '90' },
	// { sortable: true, field: 'payBankAccountName', title: '姓名/账户名' },
	// { sortable: true, field: 'payBankAccount', title: '卡号' },
	// { sortable: true, field: 'payTime', title: '支付日期', formatter: 'formatTime', width: '100' },
	// { sortable: true, field: 'skBankAccount', title: '收款账号' },
	// { sortable: true, field: 'skBankUserName', title: '收款账户名' },
	// {
	// 	sortable: true,
	// 	field: 'relationStatus',
	// 	title: '状态',
	// 	width: '70',
	// 	align: 'center',
	// 	type: 'click',
	// 	handle: (row: any) => rechargeRecord(row),
	// 	isDisabled: (row: any) => (row.relationStatus != '是' ? true : false),
	// 	formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : ''),
	// },
	// { sortable: true, field: 'processRemark', title: '流程备注' },
	// {
	// 	title: '操作',
	// 	align: 'center',
	// 	type: 'btnList',
	// 	fixed: 'right',
	// 	width: '100',
	// 	btnList: [
	// 		{ title: '关联', handle: handleEdit, isDisabled: (row: any) => row.isIgnore === true || row.status == '是' },
	// 		{ title: '忽略', handle: handleDelete, isDisabled: (row: any) => row.isIgnore === true },
	// 		{ title: '解除', handle: handleRelieve },
	// 	],
	// },
]);
// const isDisabled1 = (row: any, val: number): boolean => {
// 	if (val != 1 && row.approverStatus === '已审核') {
// 		return true;
// 	}
// 	if (val === 1) {
// 		return row.relationStatus !== '是';
// 	} else if (val === 2) {
// 		return row.relationStatus == '已拆分' || row.relationStatus == '已匹配'  || (row.clickPage !== null && row.clickPage !== 'BankFlow') || (row.approverStatus == '已审核' || row.approverStatus == '' || row.approverStatus == null)
// 	} else if (val === 3) {
// 		return row.relationStatus === '已拆分' || (row.clickPage !== null && row.clickPage !== 'BankFlow') || (row.approverStatus == '已审核' || row.approverStatus == '' || row.approverStatus == null)
// 	} else if (val === 4) {
// 		return (row.approverStatus == '已审核' || row.approverStatus == '' || row.approverStatus == null)
// 	}
// 	return false;
// };

const dismissIgnorance = (row: any) => {
	ElMessageBox.confirm('此操作将解除忽略此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await UnIgnoreBankFlow({ id: row.id });
			if (success) {
				window.$message.success('解除忽略成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消');
		});
}

const isDisabled1 = (row: any, val: number): boolean => {
	if (val != 1 && row.approverStatus === '已审核') {
		return true;
	}
	if (val === 1) {
		return row.relationStatus !== '是'
	} else if (val === 2) {
		return row.relationStatus === '否' && (row.clickPage == null || row.clickPage == 'BankFlow') ? false : true
	} else if (val === 3) {
		return (row.relationStatus === '是' || row.approverStatus === '待审核') && (row.clickPage == null || row.clickPage == 'BankFlow') ? false : true
	} else if (val === 4) {
		return (row.relationStatus === '是' && row.approverStatus === '待审核') && (row.clickPage == null || row.clickPage == 'BankFlow') ? false : true
	} else if (val == 5) {
		return row.relationStatus == '忽略' ? false : true;
	}
	return false;
};
const tableCols = ref<VxeTable.Columns[]>([
	//列头
	// { sortable: true, field: 'businessId', title: '钉钉流程号' },
	// { sortable: true, field: 'relationArea', title: '归属区域' },
	{ sortable: true, field: 'relationFeeType', title: '收支类型', width: '90' },
	{ sortable: true, field: 'bankType', title: '行名', width: '80' },
	{ sortable: true, field: 'account', title: '账号', width: '80' },
	{ field: 'busName', title: '别名', width: '80' },
	{ sortable: true, field: 'balance', title: '账户余额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'occurenceTime', title: '交易时间', width: '140' },
	{ sortable: true, field: 'inAmount', title: '收入金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '支出金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'toAccount', title: '对方账号', width: '90' },
	{ sortable: true, field: 'toAccountName', title: '对方名称', width: '90' },
	{ sortable: true, field: 'flowType', title: '交易类型', width: '90' },
	{ sortable: true, field: 'bankRemark', title: '交易备注', width: '90' },
	{
		sortable: true,
		field: 'relationStatus',
		title: '状态',
		width: '70',
		type: 'click',
		handle: (row: any) => rechargeRecord(row),
		isDisabled: (row: any) => isDisabled1(row, 1),
		formatter: (row: any) => (row.relationStatus == '是' ? '已匹配' : row.relationStatus == '否' ? '未匹配' : row.relationStatus == '忽略' ? '已忽略' : ''),
	},
	{ sortable: true, field: 'operatorUserName', title: '操作人', width: '90' },
	{ sortable: true, field: 'approverUserName', title: '审批人', width: '90' },
	{ sortable: true, field: 'approverStatus', title: '审批状态', width: '90' },
	{ field: 'instanceTitle', title: '流程名', width: '90' },
	{ field: 'businessId', title: '流程号', width: '160', type: 'click', handle: (row: any) => viewProcess(row), copy: true  },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		field: '**************',
		fixed: 'right',
		width: '150',
		btnList: [
			// { title: '关联', handle: handleEdit, isDisabled: (row: any) => row.isIgnore === true || row.relationStatus == '是', },
			// // { title: '忽略', handle: handleDelete, isDisabled: (row: any) => row.isIgnore === true || row.relationStatus == '忽略' },
			// { title: '解除', handle: handleRelieve },

			{ title: '关联', handle: handleEdit, isDisabled: (row: any) => isDisabled1(row, 2) },
			{ title: '解除', handle: handleRelieve, isDisabled: (row: any) => isDisabled1(row, 3) },
			{ title: '解除忽略', handle: (row: any) => dismissIgnorance(row), isDisabled: (row: any) => isDisabled1(row, 5), },
			{ title: '审批', handle: handApprovalMethod, isDisabled: (row: any) => isDisabled1(row, 4), permissions: 'auditBtn' },
		],
	},
]);

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.toggle = false
		item.amountBackup = item.amount;
	});
	tableData.value = data.data.list;
	sortEvent([]);
	callback(data);
};

onMounted(async () => {
	// const data = await GetAllOnlineBank();
	// options.value = data.map((item: any) => ({
	// 	label: item.account,
	// 	value: item.account,
	// 	name: item.bankType,
	// }));
	getFeeType();
});
</script>

<style scoped lang="scss">
.btnGroup {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

.itemCss {
	width: 110px;
	margin: 0 3px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 25px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.custom-cascader {
	margin: 0 3px 5px 0;
}

.header-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 30px 0 0;

	.title-text {
		display: flex;
		align-items: center;
		margin: 0px 10px;
		font-size: 18px;
		height: 40px;

		.title-close {
			margin-left: 10px;
		}
	}
}
/* 默认状态 */
.custom-button {
	background-color: #409eff; /* 自定义默认背景色 */
	color: #fff; /* 自定义默认文字颜色 */
}
/* 鼠标悬停状态 */
.custom-button:hover {
	background-color: #66b1ff; /* 自定义hover背景色 */
}
.custom-button:active {
	background-color: #3a8ee6; /* 自定义按下时的背景色 */
}
</style>
