<template>
	<div class="container">
		<div ref="refheard" v-if="state.isheard">
			<el-card class="cardCss headerCss">
				<slot name="header" />
			</el-card>
		</div>

		<el-card class="cardCss contentCss">
			<slot name="content" v-if="state.iscontent" />
		</el-card>

		<el-card class="cardCss footerCss" ref="reffooter" v-if="state.isfooter">
			<div>
				<slot name="footer" />
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts" name="Container">
import { ref, reactive, onMounted, getCurrentInstance, onUnmounted, defineAsyncComponent, computed, defineProps, useSlots } from 'vue';
defineProps({
	pageLoading: {
		type: Boolean,
		default: false,
	},
	//  notab: {
	//   type: Boolean,
	//   default: false,
	//  }
});
const refheard = ref();
const reffooter = ref();
const state = reactive({
	isheard: false,
	iscontent: false,
	isfooter: false,
	heardheight: 0,
	footerheight: 0,
});

let uSlots = useSlots();

const isVertical = onMounted(() => {
	setTimeout(() => {
		state.heardheight = refheard.value?.offsetHeight;
		state.footerheight = reffooter.value?.offsetHeight;
	}, 0);
	if (uSlots.header) {
		state.isheard = true;
	}
	if (uSlots.content) {
		state.iscontent = true;
	}
	if (uSlots.footer) {
		state.isfooter = true;
	}
});
</script>

<style scoped lang="scss">
.container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	background-color: #fff;
}

.cardCss {
	width: 100%;
}

.headerCss {
	margin-bottom: 5px;
	box-sizing: border-box;
	::v-deep .el-card__body {
		padding: 5px;
		box-sizing: border-box;
	}
}
.contentCss {
	flex: 1;
	box-sizing: border-box;
	width: 100%;
	height: 100% !important;
	// margin-top: 5px;
	::v-deep .el-card__body {
		padding: 5px;
		height: 100% !important;
		box-sizing: border-box;
	}
}
.footerCss {
	margin-top: 5px;
	// min-height: 45px;
	display: flex;
	align-items: center;
	justify-content: end;
	::v-deep .el-card__body {
		padding: 0;
	}
}

::v-deep .el-card {
	--el-card-border-color: none !important;
}
</style>
