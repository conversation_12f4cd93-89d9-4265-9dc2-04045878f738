<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="凭证处理" name="first" style="height: 100%">
					<voucherDetails />
				</el-tab-pane>
				<el-tab-pane label="支出科目汇总" name="second" style="height: 100%" lazy>
					<expenditureSubjectIndex />
				</el-tab-pane>
				<el-tab-pane label="正式凭证" name="third" style="height: 100%" lazy>
					<voucherInformation />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const voucherDetails = defineAsyncComponent(() => import('./components/voucherDetails.vue'));
const expenditureSubjectIndex = defineAsyncComponent(() => import('./components/expenseAccount/expenditureSubjectIndex.vue'));
const voucherInformation = defineAsyncComponent(() => import('./components/voucherInformation.vue'));
const activeName = ref('first');
</script>
