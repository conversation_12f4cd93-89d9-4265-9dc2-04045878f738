/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MessageTypeEnum } from './message-type-enum';
 /**
 * 
 *
 * @export
 * @interface MessageInput
 */
export interface MessageInput {

    /**
     * 接收者用户Id
     *
     * @type {number}
     * @memberof MessageInput
     */
    receiveUserId?: number;

    /**
     * 接收者名称
     *
     * @type {string}
     * @memberof MessageInput
     */
    receiveUserName?: string | null;

    /**
     * 用户ID列表
     *
     * @type {Array<number>}
     * @memberof MessageInput
     */
    userIds?: Array<number> | null;

    /**
     * 消息标题
     *
     * @type {string}
     * @memberof MessageInput
     */
    title?: string | null;

    /**
     * @type {MessageTypeEnum}
     * @memberof MessageInput
     */
    messageType?: MessageTypeEnum;

    /**
     * 消息内容
     *
     * @type {string}
     * @memberof MessageInput
     */
    message?: string | null;

    /**
     * 发送者Id
     *
     * @type {string}
     * @memberof MessageInput
     */
    sendUserId?: string | null;

    /**
     * 发送者名称
     *
     * @type {string}
     * @memberof MessageInput
     */
    sendUserName?: string | null;

    /**
     * 发送时间
     *
     * @type {Date}
     * @memberof MessageInput
     */
    sendTime?: Date;
}
