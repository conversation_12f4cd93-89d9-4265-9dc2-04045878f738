<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="资金日报表" name="first" style="height: 100%" lazy>
					<dailyReturnOfFunds />
				</el-tab-pane>
				<el-tab-pane label="支出明细" name="third" style="height: 100%" lazy>
					<expendituresDetails />
				</el-tab-pane>
				<el-tab-pane label="收入明细" name="fourth" style="height: 100%" lazy>
					<revenueDetails />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const dailyReturnOfFunds = defineAsyncComponent(() => import('./dailyReturnOfFunds.vue'));
const breakdownOfExpenditures = defineAsyncComponent(() => import('./breakdownOfExpenditures.vue'));
const expendituresDetails = defineAsyncComponent(() => import('./expendituresDetails.vue'));
const revenueDetails = defineAsyncComponent(() => import('./revenueDetails.vue'));
const activeName = ref('first');
</script>
