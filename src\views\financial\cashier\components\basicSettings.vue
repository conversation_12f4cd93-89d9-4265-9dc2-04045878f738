<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="出纳人员" name="first" style="height: 100%" lazy>
					<cashiers />
				</el-tab-pane>
				<el-tab-pane label="网银别名设置" name="second" style="height: 100%" lazy>
					<onlineBanking />
				</el-tab-pane>
				<el-tab-pane label="收支类型设置" name="third" style="height: 100%" lazy>
					<expenseClassification />
				</el-tab-pane>
				<el-tab-pane label="收入类型设置" name="four" style="height: 100%" lazy>
					<incomeTypeSettings />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
const cashiers = defineAsyncComponent(() => import('./cashiers.vue'));
const onlineBanking = defineAsyncComponent(() => import('./onlineBanking.vue'));
const expenseClassification = defineAsyncComponent(() => import('./expenseClassification.vue'));
const incomeTypeSettings = defineAsyncComponent(() => import('./incomeTypeSettings.vue'));
const activeName = ref('first');
</script>

<style scoped lang="scss"></style>
