<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="总报表" name="first" style="height: 100%" lazy>
					<allReport />
				</el-tab-pane>
				<el-tab-pane label="流水数据" name="second" style="height: 100%" lazy>
					<turnoverData />
				</el-tab-pane>
				<el-tab-pane label="退款数据" name="six" style="height: 100%" lazy>
					<tuiData />
				</el-tab-pane>
                <el-tab-pane label="钉钉流程" name="third" style="height: 100%" lazy>
					<costData />
				</el-tab-pane>
				<el-tab-pane label="过账冲正" name="forth" style="height: 100%" lazy>
					<postingReversal />
				</el-tab-pane>
				<el-tab-pane label="基础设置" name="fifth" style="height: 100%" lazy>
					<basicSettings />
				</el-tab-pane>
				<!-- <el-tab-pane label="流程审批" name="sixth" style="height: 100%" lazy>
					<processApprovals />
				</el-tab-pane> -->
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const allReport = defineAsyncComponent(() => import('./components/allReport.vue'));
const basicSettings = defineAsyncComponent(() => import('./components/basicSettings.vue'));
const costData = defineAsyncComponent(() => import('./components/costData.vue'));
const postingReversal = defineAsyncComponent(() => import('./components/postingReversal.vue'));
const processApprovals = defineAsyncComponent(() => import('./components/processApprovals.vue'));
const turnoverData = defineAsyncComponent(() => import('./components/turnoverData.vue'));
const tuiData = defineAsyncComponent(() => import('./components/tuiData.vue'));

const activeName = ref('first');
</script>
