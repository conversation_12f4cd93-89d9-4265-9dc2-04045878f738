<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.timeType" placeholder="时间类型" clearable style="width: 85px; margin: 0 0 5px 0">
					<el-option label="日期" value="日期" />
					<el-option label="操作时间" value="操作时间" />
				</el-select>
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<!-- <dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" startPlaceholder="操作开始时间" endPlaceholder="操作结束时间" class="publicCss" style="width: 200px" /> -->
				<el-select v-model="query.platform" placeholder="平台" class="publicCss itemCss" clearable filterable @change="fetchShopList">
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model.trim="query.shopId" placeholder="店铺" class="publicCss itemCss" clearable filterable>
					<el-option v-for="item in shopNamelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.platformShopId" class="publicCss" style="width: 110px" placeholder="平台店铺ID" clearable maxlength="50" />
				<el-select v-model="query.amountType" placeholder="金额类型" clearable style="width: 85px; margin: 0 0 5px 0">
					<el-option label="店铺余额" value="店铺余额" />
					<el-option label="提现金额" value="提现金额" />
					<el-option label="账单金额" value="账单金额" />
				</el-select>
				<numRange v-model:maxNum="query.maxAmount" v-model:minNum="query.minAmount" :precision="2" style="width: 160px; margin: 0 5px 5px 0" minPlaceHolder="≥金额" maxPlaceHolder="<金额" />
				<el-select v-model="query.dataSource" placeholder="提现类型" class="publicCss itemCss" clearable filterable>
					<el-option key="RPA" label="RPA" value="RPA" />
					<el-option key="人工" label="人工" value="人工" />
				</el-select>
				<el-select v-model="query.isDiff" placeholder="差异" class="publicCss itemCss" clearable filterable>
					<el-option key="是" label="是" :value="true" />
					<el-option key="否" label="否" :value="false" />
				</el-select>
				<el-input v-model.trim="query.operatorUserName" placeholder="操作人" class="publicCss itemCss" clearable maxlength="50" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss itemCss" clearable filterable>
					<el-option key="待抓取" label="待抓取" value="待抓取" />
					<el-option key="已抓取" label="已抓取" value="已抓取" />
					<el-option key="已提现" label="已提现" value="已提现" />
					<el-option key="提现失败" label="提现失败" value="提现失败" />
					<el-option key="已编辑" label="已编辑" value="已编辑" />
				</el-select>
				<el-select v-model="query.accountName" placeholder="提现账户名(别名)" class="publicCss" clearable filterable>
					<el-option v-for="item in alipayCashWithdrawal" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawInfo"
			>
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<!-- <el-button @click="startImport" type="primary">导入</el-button>
					<el-button @click="downLoadFile" type="primary">下载导入模版</el-button> -->
					<div style="border: 1px solid #dcdfe6; border-radius: 5px; display: flex; align-items: center; padding: 0 10px; margin-left: 10px">
						<el-input v-model.trim="pleaseEnter" class="publicCss inputboBor" style="width: 180px" placeholder="模糊店铺名至对应行" clearable maxlength="50" @blur="pleaseEnterBlur" />
						<div style="height: 20px; width: 1px; background-color: #eee; margin-right: 15px"></div>
						<el-icon :size="20" style="cursor: pointer; margin-right: 10px; color: #dcdfe6" @click="scrollUp">
							<ArrowUp />
						</el-icon>
						<el-icon :size="20" style="cursor: pointer; color: #dcdfe6" @click="scrollDown">
							<ArrowDown />
						</el-icon>
					</div>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="编辑" width="400" draggable overflow @close="handleClose">
		<div style="height: 250px">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="店铺名" :label-width="'90px'">
					{{ singleform.shopName }}
				</el-form-item>
				<el-form-item label="提现账户名" :label-width="'90px'">
					{{ singleform.accountName }}
				</el-form-item>
				<el-form-item label="店铺余额" :label-width="'90px'">
					<el-input-number v-model="singleform.shopBalance" placeholder="请输入" :min="0" :max="**************" :controls="false" class="btnGroup" />
				</el-form-item>
				<el-form-item label="提现金额" :label-width="'90px'" prop="withDrawAmount">
					<el-input-number v-model="singleform.withDrawAmount" placeholder="请输入" :min="0" :max="**************" :controls="false" class="btnGroup" />
				</el-form-item>
				<el-form-item label="原因" :label-width="'90px'">
					<el-input
						v-model="singleform.reason"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						class="btnGroup"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -18vh !important">
		<div style="height: 100px">
			<el-upload
				ref="uploadFile"
				class="upload-demo"
				:auto-upload="false"
				:multiple="false"
				:limit="1"
				action=""
				accept=".xlsx"
				:file-list="fileLists"
				:data="fileparm"
				:http-request="onUploadFile"
				:on-success="onUploadSuccess"
				:on-change="onUploadChange"
				:on-remove="onUploadRemove"
			>
				<template #trigger>
					<el-button size="small" type="primary">选取文件</el-button>
				</template>
				<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
			</el-upload>
		</div>
		<div style="display: flex; justify-content: end; align-items: center">
			<el-button @click="dialogVisible = false">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryWithDrawInfo, UpdateWithDrawInfo, ExportWithDrawInfo, GetWithDrawShopList, ImportWithDrawInfo } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import type { FormInstance } from 'element-plus';
import dayjs from 'dayjs';
import { bankList, platformlist, expressCompany } from '/@/utils/tools';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const ruleFormRef = ref<FormInstance | null>(null);
const nameList = ref<Public.options[]>([]);
const shopNamelist = ref<Public.options[]>([]);
const table = ref();
const dialogVisible = ref(false);
const uploadLoading = ref(false);
const fileLists = ref([]);
const alipayCashWithdrawal = ref<any[]>([]);
const expenseTypeList = ref<string[]>([]);
const storeExpenseTypeList = ref<string[]>([]);
const pleaseEnter = ref('');
interface TableItem {
	shopName: string;
	id: string;
}
const tableData = ref<TableItem[]>([]);
const filteredRows = ref<TableItem[]>([]);
const currentIndex = ref(-1);
const uploadFile = ref();
const fileparm = ref({});
const singleform = ref<{
	shopName: string;
	accountName: string;
	withDrawAmount: string | undefined;
	shopBalance: string | undefined;
	reason: string;
}>({
	shopName: '',
	accountName: '',
	withDrawAmount: undefined,
	shopBalance: undefined,
	reason: '',
});

const singlerules = {
	withDrawAmount: [{ required: true, message: '请输入提现金额', trigger: 'blur' }],
};
const query = ref({
	startTime: '',
	endTime: '',
	platform: '',
	shopId: '',
	platformShopId: '',
	amountType: '',
	timeType: '',
	account: '',
	dataSource: '',
	status: '',
	maxAmount: undefined,
	minAmount: undefined,
	userName: '',
	userId: '',
	isDiff: '',
	operatorUserName: '',
	accountName: '',
});

const pleaseEnterBlur = () => {
	if (!pleaseEnter.value) {
		return;
	}
	filteredRows.value = tableData.value.filter((item) => item.shopName.includes(pleaseEnter.value));
	currentIndex.value = filteredRows.value.length > 0 ? 0 : -1;
	if (currentIndex.value !== -1) {
		table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
	} else {
		ElMessage.warning('未找到匹配的行');
	}
};

const scrollUp = () => {
	if (!pleaseEnter.value || filteredRows.value.length === 0) {
		ElMessage.warning('没有可滚动的行');
		return;
	}
	if (currentIndex.value > 0) {
		currentIndex.value -= 1;
	} else {
		currentIndex.value = filteredRows.value.length - 1;
	}
	table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
};

const scrollDown = () => {
	if (!pleaseEnter.value || filteredRows.value.length === 0) {
		ElMessage.warning('没有可滚动的行');
		return;
	}
	if (currentIndex.value < filteredRows.value.length - 1) {
		currentIndex.value += 1;
	} else {
		currentIndex.value = 0;
	}
	table.value.onScrollToRow(filteredRows.value[currentIndex.value]);
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (!valid) return;
		const { success } = await UpdateWithDrawInfo({ ...singleform.value });
		if (success) {
			window.$message.success('编辑成功');
			editVisible.value = false;
			getList();
		}
	});
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const handleSubmit = () => {
	if (ruleFormRef.value) {
		submitForm(ruleFormRef.value);
	}
};
const downLoadFile = async () => {
	window.open('/excel/cwManage/店铺提现导入模版.xlsx', '_self');
};

//导入弹窗
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
};
const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	var res = await ImportWithDrawInfo(form);
	if (res?.success) window.$message({ message: '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
const exportProps = async () => {
	await ExportWithDrawInfo({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	editVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'withDrawTime', title: '日期', width: '90', formatter: 'formatDate' },
	{ sortable: true, field: 'platform', title: '平台', width: '70', formatter: 'formatPlatform' },
	{ sortable: true, field: 'shopName', title: '店铺名', width: '150' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID', width: '110' },
	{ sortable: true, field: 'accountName', title: '提现账户名(别名)', width: '150' },
	{ sortable: true, field: 'shopBalance', title: '店铺余额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'withDrawAmount', title: '提现金额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'billAmount', title: '账单金额', width: '120', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'isDiff', title: '差异', width: '70', formatter: (row: any) => (row.isDiff ? '是' : '否') },
	{ sortable: true, field: 'reason', title: '原因' },
	{ sortable: true, field: 'dataSource', title: '提现类型', width: '95' },
	{ sortable: true, field: 'modifiedTime', title: '操作时间', width: '95' },
	{ sortable: true, field: 'modifiedUserNameNew', title: '操作人', width: '90' },
	{ sortable: true, field: 'status', title: '状态', width: '75' },
	// {
	// 	title: '操作',
	// 	align: 'center',
	// 	width: '90',
	// 	type: 'btnList',
	// 	minWidth: '90',
	// 	direction: 'column',
	// 	btnList: [{ title: '编辑', handle: onEdit }],
	// 	fixed: 'right',
	// },
]);
const fetchShopList = async () => {
	const params = {
		platform: query.value.platform,
		currentPage: 1,
		pageSize: ********,
	};
	const { data, success } = await GetWithDrawShopList(params);
	shopNamelist.value = [];
	shopNamelist.value = data.map((item: any) => ({
		label: item.shopName,
		value: item.shopId,
	}));
};
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银的数据');
		})
		.map((item: any) => ({ label: item.accountName, value: item.account }));
	alipayCashWithdrawal.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银');
		})
		.map((item: any) => ({
			label: item.accountName,
			value: item.account,
		}));
};
const disposeProps = async (data: any, callback: Function) => {
	tableData.value = data.data.list;
	callback(data);
};
onMounted(() => {
	query.value.timeType = '日期';
	query.value.startTime = dayjs().format('YYYY-MM-DD');
	query.value.endTime = dayjs().format('YYYY-MM-DD');
	getAllDept();
	fetchShopList();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 90%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}

.itemCss {
	width: 115px;
}

.inputboBor :deep(.el-input__wrapper) {
	border: 1px solid rgb(0, 0, 0, 0) !important;
	box-shadow: none !important;
}
</style>
