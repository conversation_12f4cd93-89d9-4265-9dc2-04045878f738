<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-date-picker
					v-model="timeRange"
					type="date"
					placeholder="日期"
					@change="changeTime"
					:clearable="false"
					style="width: 150px"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
					class="publicCss"
				/>
				<el-input v-model.trim="query.shopType" class="publicCss" placeholder="店铺类别" clearable maxlength="50" />
				<el-input v-model.trim="query.shopName" class="publicCss" placeholder="店铺名称" clearable maxlength="50" />
				<div class="publicCss">
					<manyInput v-model:inputt="query.stationCode" :title="'网点编码'" :verifyNumber="false" :placeholder="'网点编码'" :maxRows="300" :maxlength="3000" />
				</div>
				<el-input v-model.trim="query.stationName" class="publicCss" placeholder="网点名称" clearable maxlength="50" />
				<faceSheetSettings v-model:value="query.cityNameList" title="区域" multiple class="publicCss" />
				<el-select v-model="query.platformList" placeholder="平台" class="publicCss" multiple collapse-tags clearable>
					<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
					<el-option key="待抓取" label="待抓取" :value="0" />
					<el-option key="无需充值" label="无需充值" :value="10" />
					<el-option key="待充值" label="待充值" :value="20" />
					<el-option key="充值审批中" label="充值审批中" :value="30" />
					<el-option key="已拒绝充值" label="已拒绝充值" :value="40" />
					<el-option key="已审批核实中" label="已审批核实中" :value="50" />
					<el-option key="已核实未充值" label="已核实未充值" :value="60" />
					<el-option key="已核实已充值" label="已核实已充值" :value="70" />
				</el-select>
				<faceSheetSettings v-model:value="query.expressComList" title="快递公司" multiple class="publicCss" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="onRecharge" type="primary">验算确认</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				:mergeCells="mergeCells"
				isNeedDisposeProps
				isNeedCheckBox
				@select="checkboxChange"
				@disposeProps="disposeProps"
				ref="table"
				id="sheetData20240916093955"
				:tableCols="tableCols"
				:query="query"
				:queryApi="GetElectronicWaybillWorkDatePageList"
				:pageSize="300"
			>
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
				<template #status="{ row }">
					<span :style="{ color: getStatusColor(row.statusName) }">
						{{ row.statusName }}
					</span>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="dialogVisible" :title="singleform.formTitle" width="900" draggable overflow @close="closeMethod">
		<div style="height: 660px" v-loading="singleform.loading">
			<div style="height: 430px">
				<el-form :model="singleform" :rules="singlerules">
					<div style="margin-bottom: 15px; display: flex; justify-content: space-between">
						<div style="flex: 1; margin-right: 20px">
							<el-form-item label="数量" :label-width="'90px'" prop="num">
								<el-input-number v-model="singleform.num" :min="0" :max="**************" :controls="false" disabled style="width: 300px" />
							</el-form-item>
							<el-form-item label="收款账户" :label-width="'90px'">
								{{ singleform.accountName }}
							</el-form-item>
							<el-form-item label="开户行" :label-width="'90px'">
								{{ singleform.accountBankName }}
							</el-form-item>
						</div>
						<div style="flex: 1">
							<el-form-item label="单价" :label-width="'90px'">
								<span v-if="priceCheck">{{ '单价错误有不同单价' }}</span>
								<el-input-number v-else v-model="singleform.price" :min="0" :max="9999999" :controls="false" disabled style="width: 300px" @blur="handleBlur" />
							</el-form-item>
							<el-form-item label="收款账号" :label-width="'90px'">
								{{ singleform.accountNo }}
							</el-form-item>
						</div>
					</div>
					<div>
						<el-form-item label="备注" :label-width="'90px'" style="white-space: pre-wrap; word-break: break-all">
							<el-input
								v-model="singleform.remark"
								placeholder="请输入"
								type="textarea"
								autocomplete="off"
								clearable
								style="width: 700px"
								maxlength="1000"
								:autosize="{ minRows: 6, maxRows: 6 }"
								resize="none"
							/>
						</el-form-item>
						<el-form-item label="合计金额" :label-width="'90px'">
							{{ singleform.total }}
						</el-form-item>
						<el-form-item label="图片" :label-width="'90px'">
							<div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: space-between; height: auto; width: 100%">
								<div style="margin-bottom: 10px">
									<uploadMf v-if="dialogVisible" v-model:imagesStr="singleform.imgUrl" ref="refuploadMf" :upstyle="{ height: 40, width: 40 }" :limit="9"></uploadMf>
								</div>
								<div>
									<el-button :loading="createLoading" :disabled="createLoading" @click="tocreateimg">{{ createLoading ? '上传中' : '截图' }}</el-button>
								</div>
							</div>
						</el-form-item>
					</div>
				</el-form>
			</div>
			<el-table :data="singleform.dtlList" style="width: 100%" height="250px">
				<el-table-column prop="shopType" label="店铺类别" width="120" :show-overflow-tooltip="true" />
				<el-table-column prop="shopName" label="店铺名称" width="120" :show-overflow-tooltip="true" />
				<el-table-column prop="yesterdayRealSend" label="昨日实发" width="120" :show-overflow-tooltip="true" />
				<el-table-column prop="systemResidue" label="系统剩余面单数" width="120" :show-overflow-tooltip="true" />
				<el-table-column prop="expectedTodayRecharge" label="预计充值单量">
					<template #default="{ row, $index }">
						<el-input-number
							v-model="row.expectedTodayRecharge"
							:min="0"
							:max="9999999"
							:precision="0"
							:controls="false"
							placeholder="请输入"
							@change="(value: any) => onAmountChange(value, $index, row)"
						/>
					</template>
				</el-table-column>
				<el-table-column label="操作">
					<template #default="scope">
						<el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave"> 发起流程 </el-button>
			</div>
		</template>
	</el-dialog>

	<el-dialog v-model="editDialog" :title="'数据修改页'" width="680" draggable overflow>
		<div style="height: 180px">
			<el-form :model="editInfo" :rules="editrules">
				<div style="display: flex; justify-content: space-between">
					<div>
						<el-form-item label="昨日快递公司抽单" :label-width="'120px'" prop="yesterdayExtractOrder">
							<el-input-number v-model="editInfo.yesterdayExtractOrder" placeholder="请输入" autocomplete="off" :min="0" :max="9999999" :precision="0" :controls="false" style="width: 170px" />
						</el-form-item>
						<el-form-item label="昨日实发" :label-width="'120px'" prop="yesterdayRealSend">
							<el-input-number
								v-model="editInfo.yesterdayRealSend"
								placeholder="请输入"
								autocomplete="off"
								:min="0"
								:max="9999999"
								:precision="0"
								:controls="false"
								style="width: 170px"
								@blur="onInputMethod(editInfo.yesterdayRealSend, 1)"
							/>
						</el-form-item>
						<el-form-item label="预计今日充值情况" :label-width="'120px'" prop="expectedTodayRecharge">
							{{ editInfo.expectedTodayRecharge }}
						</el-form-item>
					</div>
					<div>
						<el-form-item label="昨日大额面单充值" :label-width="'120px'" prop="yesterdayBigWaybill">
							<el-input-number v-model="editInfo.yesterdayBigWaybill" placeholder="请输入" autocomplete="off" :min="0" :max="9999999" :precision="0" :controls="false" style="width: 170px" />
						</el-form-item>
						<el-form-item label="系统剩余面单数" :label-width="'120px'" prop="systemResidue">
							<el-input-number
								v-model="editInfo.systemResidue"
								placeholder="请输入"
								autocomplete="off"
								:min="0"
								:max="9999999"
								:precision="0"
								:controls="false"
								style="width: 170px"
								@blur="onInputMethod(editInfo.systemResidue, 2)"
							/>
						</el-form-item>
					</div>
				</div>
			</el-form>
		</div>
		<div style="display: flex; justify-content: center">
			<el-button style="width: 80px" @click="onConfirmation">确认</el-button>
		</div>
	</el-dialog>

	<el-dialog v-model="recordDialog" title="充值记录详情" width="880" draggable overflow>
		<div style="height: 600px" v-if="recordDialog">
			<div>
				<el-date-picker
					v-model="recordTime"
					style="width: 250px"
					type="daterange"
					range-separator="至"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					@change="recordchangeTime"
				/>
				<el-select v-model="recordInfo.platformList" placeholder="平台" multiple collapse-tags clearable style="width: 150px; margin: 0 10px">
					<el-option v-for="item in filteredPlatformList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="recordInfo.createdUserName" placeholder="操作人" clearable maxlength="50" style="width: 150px" />
				<el-button @click="rechargeRecord(1)" type="primary" style="margin-left: 10px">查询</el-button>
			</div>
			<vxetable
				showsummary
				ref="table1"
				id="record20240915093955"
				style="height: 95%"
				isNeedPager
				:query="recordInfo"
				:tableCols="recordtableCols"
				:queryApi="QueryElectronicWaybillRecharge"
				:pageSize="50"
			>
			</vxetable>
		</div>
	</el-dialog>

	<div id="oneboxx">
		<el-table :data="singleform.dtlList" style="width: 100%">
			<el-table-column prop="shopType" label="店铺类别" width="300" :show-overflow-tooltip="true" />
			<el-table-column prop="shopName" label="店铺名称" width="300" :show-overflow-tooltip="true" />
			<el-table-column prop="yesterdayRealSend" label="昨日实发" width="150" :show-overflow-tooltip="true" />
			<el-table-column prop="systemResidue" label="系统剩余面单数" width="150" :show-overflow-tooltip="true" />
			<el-table-column prop="expectedTodayRecharge" label="预计充值单量" />
		</el-table>
	</div>
</template>

<script setup lang="ts" name="">
import html2canvas from 'html2canvas';
import { ref, defineAsyncComponent, onMounted, reactive, nextTick, computed } from 'vue';
import { ElMessageBox, ElMessage, FormRules } from 'element-plus';
import type { UploadInstance } from 'element-plus';
import dayjs from 'dayjs';
import { bankList, platformlist, expressCompany } from '/@/utils/tools';
import {
	GetElectronicWaybillWorkDatePageList,
	RechargeElectronicWaybillWorkDate,
	ExportElectronicWaybillWorkDateList,
	UpdateElectronicWaybillWorkDate_Oper,
	EditElectronicWaybillWorkDate,
	QueryElectronicWaybillRecharge,
} from '/@/api/cwManager/electronicWaybill';
import { xmtvideouploadblockasync } from '/@/api/upload/filenew';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const faceSheetSettings = defineAsyncComponent(() => import('/@/components/yhCom/faceSheetSettings.vue'));
const regionList = ref<Public.options[]>([]);
regionList.value = [
	{ label: '南昌', value: '南昌' },
	{ label: '义乌', value: '义乌' },
	{ label: '广东', value: '广东' },
	{ label: '上海', value: '上海' },
	{ label: '湖北', value: '湖北' },
	{ label: '湖南', value: '湖南' },
	{ label: '安徽', value: '安徽' },
	{ label: '苏州', value: '苏州' },
];
const createLoading = ref(false);
const loading = ref(false);
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const platList = ref<Public.options[]>([]);
const options = ref<Public.options[]>([]);
const shopNamelist = ref<Public.options[]>([]);
const dialogVisible = ref(false);
// const mergeCells = ref([]);
type MergeCell = { row: number; col: number; rowspan: number; colspan: number };
const mergeCells = ref<MergeCell[]>([]);
const uploadRef = ref<UploadInstance>();
const tableData: { value: TableDataItem[] } = { value: [] };
const verifyTool = ref(false);
const priceCheck = ref(false);
const editDialog = ref(false);
const checkboxList = ref([]);
const recordTime = ref<string[]>([]); // 明确指定类型为字符串数组
const recordDialog = ref(false);
const conditions = ref({});
const vxeVisableColumn = ref<any>([]);
const table = ref();
const table1 = ref();
const editInfo = ref<{
	yesterdayExtractOrder: number;
	yesterdayBigWaybill: number;
	yesterdayRealSend: number;
	systemResidue: number;
	expectedTodayRecharge: number;
	sendRate?: number;
}>({
	yesterdayExtractOrder: 0,
	yesterdayBigWaybill: 0,
	yesterdayRealSend: 0,
	systemResidue: 0,
	expectedTodayRecharge: 0,
});

const recordInfo = ref({
	time: [],
	platformList: [],
	createdUserName: '',
	expressCom: '',
	startTime: '',
	endTime: '',
	expressComList: [],
});

const query = ref({
	shopType: '',
	shopName: '',
	stationCode: '',
	stationName: '',
	cityNameList: [],
	platformList: [],
	expressComList: [],
	status: '',
	startTime: '',
	endTime: '',
	expressCom: '',
});

const singleform = ref<{
	num: string | undefined;
	price: number | undefined; // 根据实际情况修改
	remark: string;
	total: string;
	formTitle: string;
	loading: boolean;
	imgUrl: string[]; // 根据实际类型
	dtlList: DtlListItem[]; // 明确类型
	electronicWaybillWorkDateId?: string; // 根据实际类型
	accountBankName: string;
	accountName: string;
	accountNo: string;
}>({
	num: undefined,
	price: undefined,
	remark: '',
	total: '',
	formTitle: '充值',
	loading: false,
	imgUrl: [],
	dtlList: [],
	accountBankName: '',
	accountName: '',
	accountNo: '',
});
interface SingleForm {
	num: string;
	price: number;
	remark: string;
	total: string;
	loading: boolean;
	imgUrl: string[];
	dtlList: DtlListItem[];
	yesterdayExtractOrder: string;
	yesterdayBigWaybill: string;
	yesterdayRealSend: string;
	systemResidue: string;
	expectedTodayRecharge: string;
}

const editrules = reactive<FormRules<SingleForm>>({
	yesterdayExtractOrder: [{ required: true, message: '请输入昨日快递公司抽单', trigger: 'change' }],
	yesterdayBigWaybill: [{ required: true, message: '请输入昨日大额面单充值', trigger: 'change' }],
	yesterdayRealSend: [{ required: true, message: '请输入昨日实发', trigger: 'change' }],
	systemResidue: [{ required: true, message: '请输入系统剩余', trigger: 'change' }],
	expectedTodayRecharge: [{ required: true, message: '请输入预计今日充值情况', trigger: 'change' }],
});

const singlerules = reactive<FormRules<SingleForm>>({
	num: [{ required: true, message: '请输入数量', trigger: 'change' }],
	price: [{ required: true, message: '请输入单价', trigger: 'change' }],
});

const filteredPlatformList = computed(() => {
	return platformlist.filter((item) => item.label !== '未知');
});

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().format('YYYY-MM-DD');
		query.value.startTime = timeRange.value;
		query.value.endTime = timeRange.value;
	}
});

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const timeRange = ref('');
const changeTime = (val: string[] | string | null | undefined) => {
	if (Array.isArray(val)) {
		query.value.startTime = val.join(', ');
		query.value.endTime = val.join(', ');
	} else {
		query.value.startTime = val || '';
		query.value.endTime = val || '';
	}
};

const onAmountChange = (value: any, index: any, row: any) => {
	verifyTool.value = true;
	handleRecharge(row); // 重新修改表单数据
	handleBlur(); // 重新计算总数
};

// 获取数据
const rechargeRecord = async (row: any) => {
	if (recordTime.value && recordTime.value.length > 0) {
		recordInfo.value.startTime = recordTime.value[0];
		recordInfo.value.endTime = recordTime.value[1];
	} else if (row != 1 && recordTime.value && recordTime.value.length == 0) {
		recordInfo.value.startTime = timeRange.value;
		recordInfo.value.endTime = timeRange.value;
		recordTime.value = [timeRange.value, timeRange.value];
	} else {
		recordInfo.value.startTime = '';
		recordInfo.value.endTime = '';
	}
	if (row == 1) {
		table1.value.getList();
	} else {
		recordInfo.value.expressCom = row.expressCom;
	}
	recordDialog.value = true;
};

// 修改数据
const recordchangeTime = (val: string[] | string | null | undefined) => {
	if (Array.isArray(val)) {
		recordInfo.value.startTime = val[0];
		recordInfo.value.endTime = val[1];
		recordTime.value = [val[0], val[1]];
	} else {
		recordInfo.value.startTime = val || '';
		recordInfo.value.endTime = val || '';
	}
};

// 获取数据
const editingMethod = (row: any) => {
	editInfo.value = { ...row };
	let expectedRecharge = row.yesterdayRealSend * 2.5 - row.systemResidue;
	// 自定义四舍五入逻辑，使其符合千的倍数
	const roundToThousands = (value: number): number => {
		if (value <= 0) return 0; // 如果小于等于0，则为0
		if (value > 0 && value < 1000) return 1000; // 如果大于0且小于1000，则为1000
		return Math.round(value / 1000) * 1000; // 四舍五入到千的倍数
	};
	// 调用函数处理
	editInfo.value.expectedTodayRecharge = roundToThousands(expectedRecharge);
	editDialog.value = true;
};

const validateAndConvertToNumber = (value: any): number => {
	const num = Number(value);
	return isNaN(num) ? 0 : num;
};

const onConfirmation = async () => {
	const fieldsToCheck = ['yesterdayExtractOrder', 'yesterdayBigWaybill', 'yesterdayRealSend', 'systemResidue', 'expectedTodayRecharge'] as const;
	fieldsToCheck.forEach((field) => {
		if (field in editInfo.value) {
			if (editInfo.value[field] == null) {
				editInfo.value[field] = 0;
			}
		}
	});
	ElMessageBox.confirm('是否确认?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			editInfo.value.systemResidue = validateAndConvertToNumber(editInfo.value.systemResidue);
			editInfo.value.yesterdayBigWaybill = validateAndConvertToNumber(editInfo.value.yesterdayBigWaybill);
			editInfo.value.yesterdayExtractOrder = validateAndConvertToNumber(editInfo.value.yesterdayExtractOrder);
			editInfo.value.yesterdayRealSend = validateAndConvertToNumber(editInfo.value.yesterdayRealSend);
			editInfo.value.expectedTodayRecharge = validateAndConvertToNumber(editInfo.value.expectedTodayRecharge);
			delete editInfo.value.sendRate;
			const { success } = await EditElectronicWaybillWorkDate({ ...editInfo.value });
			if (success) {
				ElMessage({ type: 'success', message: '操作成功!' });
				getList();
				editDialog.value = false;
			}
		})
		.catch(() => {});
};

const onInputMethod = (value: number, type: number) => {
	if (type == 1) {
		editInfo.value.expectedTodayRecharge = Math.ceil(Number(value) * 2.5 - Number(editInfo.value.systemResidue));
	} else {
		editInfo.value.expectedTodayRecharge = Math.ceil(Number(editInfo.value.yesterdayRealSend) * 2.5 - Number(value));
	}
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const handleBlur = () => {
	const num = Number(singleform.value.num) || 0;
	const price = Number(singleform.value.price) || 0;
	const total = num * price;
	singleform.value.total = total.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 导出
const exportProps = async () => {
	loading.value = true;
	await ExportElectronicWaybillWorkDateList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '面单数据' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const summaryProps = (val: any) => {
	val.forEach((item: any) => {
		item.sendRate = `${item.sendRate}%`;
	});
	return val;
};

const onSingleSave = async () => {
	if (priceCheck.value) {
		ElMessage.warning('单价错误存在不同单价');
		return;
	}
	if (!singleform.value.num) {
		ElMessage.warning('请填写必填项');
		return;
	}
	let imgUrlJson = '';
	if (singleform.value.imgUrl.length > 0) {
		imgUrlJson = JSON.stringify(singleform.value.imgUrl);
	}
	singleform.value.loading = true;
	const { success } = await RechargeElectronicWaybillWorkDate({
		...singleform.value,
		imgUrl: imgUrlJson,
	});
	singleform.value.loading = false;
	if (success) {
		ElMessage.success('操作成功');
		getList();
		dialogVisible.value = false;
	}
};

const handleDelete = (index: number, row: any) => {
	ElMessageBox.confirm('是否删除?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			singleform.value.dtlList.splice(index, 1);
			onUpdateMethod();
			handleBlur();
		})
		.catch(() => {});
};

interface TableDataItem {
	expressCom: string;
	yesterdayExtractOrder: number;
	platform: string;
	shopName: string;
	expectedTodayRecharge: number;
	id: string;
	shopType: string;
	status: number;
	statusName: string;
}

interface DtlListItem extends TableDataItem {
	shopType: string; // 根据实际数据定义
	electronicWaybillWorkDateId?: string; // 根据 row.id 的实际类型
	price?: number;
}

const closeMethod = () => {
	singleform.value = {
		num: undefined,
		price: undefined,
		remark: '',
		total: '',
		formTitle: '充值',
		loading: false,
		imgUrl: [],
		dtlList: [],
		accountBankName: '',
		accountName: '',
		accountNo: '',
	};
	nextTick(() => {
		singleform.value.imgUrl = [];
		refuploadMf.value?.clearMethod();
	});
	verifyTool.value = false;
};

const getStatusColor = (statusName: any) => {
	switch (statusName) {
		case '未更新':
		case '审批未通过':
		case '充值未成功':
		case '待充值':
			return 'red';
		case '待抓取':
			return '#FFD700';
		case '余额充足':
		case '审批通过':
		case '充值成功':
		case '无需充值':
			return 'green';
		case '充值审批中':
			return 'blue';
		default:
			return 'black';
	}
};

// 格式化平台
const getPlatformLabel = (platform: string) => {
	const platformLabel = platformlist.find((p) => p.value === Number(platform))?.label || '未知';
	return platformLabel ? platformLabel : '未知';
};

// 将 base64 转换为文件对象的方法
const dataURLtoFile = (dataurl: string, filename: string): File => {
	const arr = dataurl.split(',');
	const mime = arr[0].match(/:(.*?);/)?.[1];
	const bstr = atob(arr[1]);
	let n = bstr.length;
	const u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new File([u8arr], filename, { type: mime });
};

// 生成图片
const tocreateimg = (): void => {
	if (singleform.value.imgUrl.length >= 9) {
		ElMessage.warning('最多上传9张图片');
		return;
	}
	createLoading.value = true;
	setTimeout(() => {
		const element = document.querySelector('#oneboxx') as HTMLElement; // 使用类型断言
		if (element) {
			html2canvas(element, {
				allowTaint: true,
				useCORS: true,
			}).then(async (canvas) => {
				const imgData = canvas.toDataURL('image/png');
				// 生成图片到本地
				// const img = new Image();
				// img.src = imgData;
				// const a = document.createElement('a');
				// a.href = imgData;
				// a.download = `图片${new Date().getTime()}.png`;
				// a.click();

				// 生成URL对象
				const imgFile = dataURLtoFile(imgData, `${new Date().getTime()}.png`); // 将 base64 转换为文件对象
				// 上传图片到服务器
				const formData = new FormData();
				formData.append('data', imgFile); // 文件对象
				formData.append('batchnumber', ''); // 示例：添加其他表单字段
				formData.append('fileName', imgFile.name); // 名称
				formData.append('total', '1');
				formData.append('index', '1');
				try {
					const res = await xmtvideouploadblockasync(formData); // 上传接口
					if (res.success) {
						ElMessage.success('图片上传成功');
						const imgUrlGenerate = res.data.url;
						singleform.value.imgUrl.push(imgUrlGenerate);
						createLoading.value = false;
					} else {
						ElMessage.error('图片上传失败');
					}
				} catch (error) {
					ElMessage.error('图片上传失败');
				}
			});
		} else {
			console.error('Element #oneboxx not found');
		}
	}, 100);
};

// 充值
const handleRecharge = async (row: any) => {
	//verifyTool.valu用来判断是否是弹窗内编辑的
	if (!verifyTool.value) {
		let controlsData = [];
		const { data, success } = await GetElectronicWaybillWorkDatePageList({ ...query.value, expressCom: row.expressCom, pageSize: 9999, currentPage: 1, orderBy: '', isAsc: true });
		if (success) {
			controlsData = data.list;
		} else if (tableData.value.length > 0) {
			controlsData = tableData.value;
		} else {
			ElMessage.error('获取数据失败');
			return;
		}
		//初始化弹窗计算总数
		let filteredItems = controlsData.filter((item: TableDataItem) => {
			// return item.expressCom === row.expressCom && !(item.status === 10 && item.statusName === '无需充值') && !(item.status === 30 && item.statusName === '充值审批中');
			return item.expressCom === row.expressCom;
		});
		// const hasPendingApproval = controlsData.some((item: TableDataItem) => item.expressCom === row.expressCom && item.status === 30 && item.statusName === '充值审批中');
		// if (hasPendingApproval) {
		// 	ElMessage.warning('存在状态为 "充值审批中" 的项目，请处理后再进行操作。');
		// 	return;
		// }
		const prices = new Set(
			filteredItems.map((item: any) => {
				item.price;
			})
		);
		if (prices.size > 1) {
			priceCheck.value = true;
		} else {
			priceCheck.value = false;
		}
		const processFilteredItems = () => {
			// filteredItems = filteredItems.filter((item: any) => item.statusName !== '待抓取'); //过滤掉待抓取的数据
			singleform.value.dtlList = JSON.parse(JSON.stringify(filteredItems)).map((item: any) => ({
				...item, // 保留原有的属性
				electronicWaybillWorkDateId: item.id, // 添加新字段
			}));
			onUpdateMethod();
			// 检查 price 是否有不同的值
			const prices = new Set(singleform.value.dtlList.map((item) => item.price));
			singleform.value.accountBankName = row.accountBankName; //开户行
			singleform.value.accountName = row.accountName; //收款账户
			singleform.value.accountNo = row.accountNo; //收款账号
			// 如果集合的大小大于 1，说明有不同的价格
			if (prices.size > 1) {
				// priceCheck.value = true;
			} else {
				singleform.value.price = singleform.value.dtlList && singleform.value.dtlList.length > 0 && singleform.value.dtlList[0].price ? singleform.value.dtlList[0].price : 0;
				handleBlur();
				// priceCheck.value = false;
			}
			if (singleform.value.dtlList.length > 0) {
				// tocreateimg(); //生成图片
			}
			dialogVisible.value = true;
		};
		const hasPendingItems = filteredItems.some((item: any) => item.statusName === '待抓取'); //判断是否有待抓取的数据
		if (hasPendingItems) {
			await ElMessageBox.confirm('该数据包含“待抓取”状态的数据，是否继续充值？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			});
		}
		processFilteredItems();
	} else {
		// 弹窗内编辑后的change重新计算总数
		let filteredItems = singleform.value.dtlList.filter((item) => item.statusName !== '待抓取');
		singleform.value.dtlList = JSON.parse(JSON.stringify(filteredItems)).map((item: any) => ({
			...item, // 保留原有的属性
			electronicWaybillWorkDateId: item.id, // 添加新字段
		}));
		onUpdateMethod();
	}
};

// 更新单价
const onUpdateMethod = () => {
	singleform.value.num = singleform.value.dtlList.reduce((sum: number, item: TableDataItem) => sum + item.expectedTodayRecharge, 0).toString();
	singleform.value.remark = singleform.value.dtlList
		.filter((item: TableDataItem) => item.expectedTodayRecharge != 0)
		.map((item: TableDataItem) => {
			const platformLabel = platformlist.find((p) => p.value === Number(item.platform))?.label || '未知';
			return `${item.expressCom}充值 ${item.shopName} ${item.expectedTodayRecharge}单`;
		})
		.join('\n');
};

const recordtableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'shopName', title: '店铺名称' },
	{ sortable: true, field: 'platform', title: '平台', formatter: 'formatPlatform', width: '70' },
	{ sortable: true, field: 'expectedTodayRecharge', title: '预计充值', width: '110', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'realTodayRecharge', title: '实际充值', width: '110', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'createdUserName', title: '操作人', width: '80' },
	{ sortable: true, field: 'auditState', title: '状态', width: '110', formatter: (row: any) => (!row.statusName ? ' ' : row.statusName) },
	{ sortable: true, field: 'createdTime', title: '时间', width: '135' },
]);

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'shopType', title: '店铺类别', summaryEvent: true, width: '120', fixed: 'left' },
	{ sortable: true, field: 'workDate', title: '日期', width: '85' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '120' },
	{ sortable: true, field: 'stationCode', title: '网点编码', width: '90' },
	{ sortable: true, field: 'stationName', title: '网点名称', width: '140' },
	{ sortable: true, field: 'yesterdayExtractOrder', title: '昨日快递公司抽单', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'yesterdayBigWaybill', title: '昨日大额面单充值', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'yesterdayRealSend', title: '昨日实发', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'systemResidue', title: '系统剩余面单数', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'systemResidueMoney', title: '系统剩余面单金额', width: '100', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'expectedTodayRecharge', title: '预计今日充值情况', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'sendRate', title: '发货占比', width: '90', formatter: 'fmtPercent', align: 'right' },
	{ sortable: true, field: 'realTodayRecharge', title: '实际充值', width: '90', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'verify', title: '验算', width: '100', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'status', title: '状态', width: '70' },
	{ sortable: true, field: 'sendRpaTime', title: '更新时间', width: '140' },
	{ sortable: true, field: 'rechargeUserName', title: '充值人', width: '80' },
	{ sortable: true, field: 'cityName', title: '区域', width: '70' },
	{ sortable: true, field: 'platform', title: '平台', formatter: 'formatPlatform', width: '70' },
	{ sortable: true, field: 'expressCom', title: '快递公司', width: '100' },
	{
		field: 'adit',
		title: '编辑',
		align: 'center',
		width: '80',
		type: 'btnList',
		btnList: [{ title: '编辑', handle: editingMethod, isDisabled: (row) => (row.status == 0 || row.status == 10 || row.status == 20 || row.status == 40 || row.status == 60 ? false : true) }],
	},
	{
		title: '操作',
		align: 'center',
		width: '100',
		field:'20250608092433',
		type: 'btnList',
		minWidth: '100',
		btnList: [
			{ title: '充值', handle: handleRecharge, isDisabled: (row) => row.readOnly == true },
			{ title: '充值记录', handle: rechargeRecord },
		],
		fixed: 'right',
	},
]);
const getColumnIndex = (title: string) => {
	vxeVisableColumn.value = table.value.getColumnsInfo();
	if (!vxeVisableColumn.value) {
		return -1;
	}
	const index = vxeVisableColumn.value.visibleColumn.findIndex((col: any) => col.title === title);
	return index;
};

const onRecharge = async () => {
	if (checkboxList.value.length === 0) {
		ElMessage.warning('请选择要充值的数据');
		return;
	}
	const ids = checkboxList.value.map((item: any) => ({ id: item.id }));
	const { data, success } = await UpdateElectronicWaybillWorkDate_Oper(ids);
	if (success) {
		ElMessage.success('操作成功');
		getList();
	}
};

const disposeProps = async (data: any, callback: Function) => {
	query.value = { ...query.value };
	let currentRow = 0;
	let currentCol = getColumnIndex('发货占比');
	let expressComCol = getColumnIndex('操作');
	let currentexpressCom = '';
	let rowspan = 1;
	mergeCells.value = [];
	data.data.list.forEach((item: any, index: any) => {
		item.workDate = dayjs(item.workDate).format('YYYY-MM-DD');
		const today = dayjs().startOf('day');
		const workDate = dayjs(item.workDate).startOf('day');
		item.readOnly = workDate.isBefore(today);
		item.systemResidueMoney = item.systemResidueMoney || 0;
		item.verify = item.verify || 0;
		item.realTodayRecharge = item.realTodayRecharge || 0;
		item.sendRate = item.sendRate ? item.sendRate.toFixed(2) : 0;
		if (item.expressCom === currentexpressCom) {
			rowspan++;
			if (index === data.data.list.length - 1) {
				mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
				mergeCells.value.push({ row: currentRow, col: expressComCol, rowspan, colspan: 1 });
			}
		} else {
			if (rowspan > 1) {
				mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
				mergeCells.value.push({ row: currentRow, col: expressComCol, rowspan, colspan: 1 });
			}
			currentRow = index;
			currentCol = getColumnIndex('发货占比');
			expressComCol = getColumnIndex('操作');
			currentexpressCom = item.expressCom;
			rowspan = 1;
		}
	});
	if (rowspan > 1) {
		mergeCells.value.push({ row: currentRow, col: currentCol, rowspan, colspan: 1 });
		mergeCells.value.push({ row: currentRow, col: expressComCol, rowspan, colspan: 1 });
	}
	mergeCells.value = Array.from(new Set(mergeCells.value.map((item) => JSON.stringify(item)))).map((item) => JSON.parse(item));
	tableData.value = data.data.list;
	callback(data);
};
</script>

<style scoped lang="scss">
.button-container {
	margin-left: auto;
	display: flex;
}

.image-gallery {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.image-container {
	position: relative;
	display: inline-block;
}

.del {
	position: absolute;
	top: 0;
	right: 5px;
	font-size: 15px;
	width: 15px;
	height: 15px;
	border-radius: 50%;
	color: gray;
	background-color: white;
	text-align: center;
	line-height: 15px;
	cursor: pointer;
	transition:
		background-color 0.3s,
		color 0.3s;
}

.del:hover {
	background-color: gray;
	color: white;
}

.publicCss {
	width: 150px;
	margin-right: 10px;
}

:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
