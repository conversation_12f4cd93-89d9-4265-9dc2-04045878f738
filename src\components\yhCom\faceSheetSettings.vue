<template>
	<div>
		<el-select
			v-model="account"
			:placeholder="props.placeholder + props.title"
			:size="props.size"
			:style="cststyle"
			filterable
			:clearable="props.clearable"
			:multiple="props.multiple"
			:collapseTags="props.multiple"
			@change="changeBank"
		>
			<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
		</el-select>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted } from 'vue';
import { GetElectronicWaybillSetList } from '/@/api/cwManager/electronicWaybill';
const props = defineProps({
	title: {
		type: String,
		default: '区域',
	},
	clearable: {
		type: Boolean,
		default: true,
	},
	placeholder: {
		type: String,
		default: '请选择',
	},
	cststyle: {
		type: String,
		default: 'width: 100%;',
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	collapseTags: {
		type: <PERSON>olean,
		default: false,
	},
	size: {
		type: String,
		default: 'small',
	},
	filterFn: {
		type: [Function, Boolean],
		default: false,
	},
	labelOption: {
		type: Array,
		default: () => ['accountName'],
	},
	valueOption: {
		type: String,
		default: 'account',
	},
});
const query = ref({
	title: props.title,
});
const emit = defineEmits(['bankChange']);
const options = ref<Public.options[]>([]);
const account = defineModel('value');
const accountName = defineModel('label');
const id = defineModel('id');

const getBankList = async () => {
	let { data, success } = await GetElectronicWaybillSetList(query.value);
	if (!success) options.value = [];
	options.value = data.list?.map((item: any) => {
		return {
			label: item.val,
			value: item.val,
			id: item.id,
		};
	});
};

const changeBank = (value: any) => {
	let res;
	if (!props.multiple) {
		const res = options.value.find((item: any) => item.value === value);
		id.value = value ? res?.id : '';
		accountName.value = value ? res?.label : '';
	} else {
		res = options.value.filter((item: any) => value.includes(item.value));
		id.value = value ? res.map((item: any) => item.id) : '';
		accountName.value = value ? res.map((item: any) => item.label) : '';
	}
	emit('bankChange', res);
};

onMounted(async () => {
	await getBankList();
});
</script>

<style scoped lang="scss"></style>
