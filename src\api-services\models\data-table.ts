/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CultureInfo } from './culture-info';
import { DataColumn } from './data-column';
import { DataSet } from './data-set';
import { IContainer } from './icontainer';
import { ISite } from './isite';
import { SerializationFormat } from './serialization-format';
 /**
 * 
 *
 * @export
 * @interface DataTable
 */
export interface DataTable {

    /**
     * @type {IContainer}
     * @memberof DataTable
     */
    container?: IContainer;

    /**
     * @type {boolean}
     * @memberof DataTable
     */
    designMode?: boolean;

    /**
     * @type {boolean}
     * @memberof DataTable
     */
    caseSensitive?: boolean;

    /**
     * @type {boolean}
     * @memberof DataTable
     */
    isInitialized?: boolean;

    /**
     * @type {SerializationFormat}
     * @memberof DataTable
     */
    remotingFormat?: SerializationFormat;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    childRelations?: Array<any> | null;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    columns?: Array<any> | null;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    constraints?: Array<any> | null;

    /**
     * @type {DataSet}
     * @memberof DataTable
     */
    dataSet?: DataSet;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    defaultView?: Array<any> | null;

    /**
     * @type {string}
     * @memberof DataTable
     */
    displayExpression?: string | null;

    /**
     * @type {{ [key: string]: any; }}
     * @memberof DataTable
     */
    extendedProperties?: { [key: string]: any; } | null;

    /**
     * @type {boolean}
     * @memberof DataTable
     */
    hasErrors?: boolean;

    /**
     * @type {CultureInfo}
     * @memberof DataTable
     */
    locale?: CultureInfo;

    /**
     * @type {number}
     * @memberof DataTable
     */
    minimumCapacity?: number;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    parentRelations?: Array<any> | null;

    /**
     * @type {Array<DataColumn>}
     * @memberof DataTable
     */
    primaryKey?: Array<DataColumn> | null;

    /**
     * @type {Array<any>}
     * @memberof DataTable
     */
    rows?: Array<any> | null;

    /**
     * @type {string}
     * @memberof DataTable
     */
    tableName?: string | null;

    /**
     * @type {string}
     * @memberof DataTable
     */
    namespace?: string | null;

    /**
     * @type {string}
     * @memberof DataTable
     */
    prefix?: string | null;

    /**
     * @type {ISite}
     * @memberof DataTable
     */
    site?: ISite;
}
