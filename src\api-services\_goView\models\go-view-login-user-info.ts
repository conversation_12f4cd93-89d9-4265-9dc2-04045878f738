/* tslint:disable */
/* eslint-disable */
/**
 * GoView
 * GoView 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 2.2.8
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 用户信息
 *
 * @export
 * @interface GoViewLoginUserInfo
 */
export interface GoViewLoginUserInfo {

    /**
     * 用户 Id
     *
     * @type {string}
     * @memberof GoViewLoginUserInfo
     */
    id?: string | null;

    /**
     * 用户名
     *
     * @type {string}
     * @memberof GoViewLoginUserInfo
     */
    username?: string | null;

    /**
     * 昵称
     *
     * @type {string}
     * @memberof GoViewLoginUserInfo
     */
    nickname?: string | null;
}
