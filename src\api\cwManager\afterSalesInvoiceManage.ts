import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/AfterSalesInvoiceManage/`;

const apiPrefixa = `${import.meta.env.VITE_APP_BASE_API_CustomerService}/AfterSalesInvoiceManage/`;

//获取公司开票信息分页数据
export const GetCompanyInvoicingInfoPage = (params: any, config = {}) => request.post(apiPrefix + 'GetCompanyInvoicingInfoPage', params, config);

//保存公司开票信息
export const SaveCompanyInvoicingInfo = (params: any, config = {}) => request.post(apiPrefix + 'SaveCompanyInvoicingInfo', params, config);

//分页获取售后发票管理(财务)分页
export const GetAfterSalesInvoiceManageFinancialPage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetAfterSalesInvoiceManageFinancialPage', params, config);
};
//获取红冲编号
export const GetOpenSuccessAfterSalesInvoiceManageFinancialPage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetOpenSuccessAfterSalesInvoiceManageFinancialPage', params, config);
};

//售后发票数据-审核
export const AuditAfterSalesInvoiceManage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'AuditAfterSalesInvoiceManage', params, config);
};

//售后发票数据-清空附件
export const ClearAfterSalesInvoiceManageExportFile = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ClearAfterSalesInvoiceManageExportFile', params, config);
};

//导出售后发票
export const ExportAfterSalesInvoiceManageFinancial = (params: any, config: any = { responseType: 'blob' }) => {
	return request.post(apiPrefix + 'ExportAfterSalesInvoiceManageFinancial', params, config);
};

//获取售后发票管理(客服)明细
export const GetAfterSalesInvoiceManageCustomerInfo = (params: any, config = {}) => {
	return request.post(apiPrefixa + 'GetAfterSalesInvoiceManageCustomerInfo', params, config);
};

//获取店铺
export const GetShopList = (params: any, config = {}) => {
	return request.post(apiPrefixa + 'GetShopList', params, config);
};

//保存店铺信息
export const SaveCompanyInvoicingShopInfo = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SaveCompanyInvoicingShopInfo', params, config);
};

//批量审核通过
export const BatchAuditAfterSalesInvoiceManage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'BatchAuditAfterSalesInvoiceManage', params, config);
};

//审核备注
export const BatchRejectAfterSalesInvoiceManage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'BatchRejectAfterSalesInvoiceManage', params, config);
};

//获取公司
export const GetCompanyList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetCompanyList', params, config);
};

//作废售后发票数据
export const NullifyAfterSalesInvoiceManage = (params: any, config = {}) => {
	return request.post(apiPrefix + 'NullifyAfterSalesInvoiceManage', params, config);
};
