/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface FileOutput
 */
export interface FileOutput {

    /**
     * Id
     *
     * @type {number}
     * @memberof FileOutput
     */
    id?: number;

    /**
     * 名称
     *
     * @type {string}
     * @memberof FileOutput
     */
    name?: string | null;

    /**
     * URL
     *
     * @type {string}
     * @memberof FileOutput
     */
    url?: string | null;

    /**
     * 大小
     *
     * @type {number}
     * @memberof FileOutput
     */
    sizeKb?: number;

    /**
     * 后缀
     *
     * @type {string}
     * @memberof FileOutput
     */
    suffix?: string | null;

    /**
     * 路径
     *
     * @type {string}
     * @memberof FileOutput
     */
    filePath?: string | null;

    /**
     * 文件类别
     *
     * @type {string}
     * @memberof FileOutput
     */
    fileType?: string | null;

    /**
     * 是否公开  若为true则所有人都可以查看，默认只有自己或有权限的可以查看
     *
     * @type {boolean}
     * @memberof FileOutput
     */
    isPublic?: boolean;

    /**
     * 上传人
     *
     * @type {string}
     * @memberof FileOutput
     */
    createUserName?: string | null;

    /**
     * 上传时间
     *
     * @type {Date}
     * @memberof FileOutput
     */
    createTime?: Date | null;

    /**
     * 关联对象名称
     *
     * @type {string}
     * @memberof FileOutput
     */
    relationName?: string | null;

    /**
     * 关联对象Id
     *
     * @type {number}
     * @memberof FileOutput
     */
    relationId?: number | null;

    /**
     * 所属Id
     *
     * @type {number}
     * @memberof FileOutput
     */
    belongId?: number | null;
}
