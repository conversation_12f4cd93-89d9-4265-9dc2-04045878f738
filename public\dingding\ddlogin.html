<!DOCTYPE html>
<html>
  <script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
	<body>
	   <div id="login_container"></div>
	</body>
   <script>
    init();
    async function init() {
        let appid = "";
        var REDIRECT_URI="";
        var res=await window.parent.getdingdingurl();
        var info=res.split(",");
        //REDIRECT_URI=info[0]+"/dingding/dddirect.html";
        REDIRECT_URI=`${window.location.origin}/dingding/dddirect.html`;
        console.log('REDIRECT_URI',REDIRECT_URI)
        //REDIRECT_URI="http://localhost:8002/dingding/dddirect.html";
        appid=info[1];
        let url = encodeURIComponent(REDIRECT_URI);
        let goto = encodeURIComponent(`https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appid}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}`)
        let obj = DDLogin({
            id:"login_container",//这里需要你在自己的页面定义一个HTML标签并设置id，例如<div id="login_container"></div>或<span id="login_container"></span>
            goto: goto, 
            style: "border:none;background-color:#FFFFFF;",
            width : "365",//官方参数 365
            height: "400"//官方参数 400
        });

        let handleMessage = (event) =>{
        let origin = event.origin;
        console.log("origin", event.origin);
        if(origin == "https://login.dingtalk.com" ) {
          let loginTmpCode = event.data; 
          console.log("loginTmpCode", loginTmpCode);
          window.location.href = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appid}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}&loginTmpCode=${loginTmpCode}`
        }
      };
      if (typeof window.addEventListener != 'undefined') {
          window.addEventListener('message', handleMessage, false);
      } else if (typeof window.attachEvent != 'undefined') {
          window.attachEvent('onmessage', handleMessage);
      }
      // setTimeout(()=>{
      //   var demo = document.getElementById('login_container').children[0]
      //   .contentWindow
      //   .document
      //   // .getElementById('xoc')
      //   // .style
      //   // .color = 'green'
      //   console.log("demo",demo)
      // },1000)

  }
  
</script>
</html>
