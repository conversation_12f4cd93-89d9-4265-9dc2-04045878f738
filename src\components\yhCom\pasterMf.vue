<template>
	<div style="background: white; display: flex; justify-content: center; align-items: center">
		<!-- v-clickoutside="handleClickOutside" -->
		<!-- :style="isborder?{border: '1px solid red'}:{}"    @click="imgclickk" onfocus='alert("得到焦点");' -->
		<!-- {{keyarr}} :id="keyarr[1]" -->
		<section
			v-if="state.divshow && !state.bigimg && ispaste"
			:id="44"
			:style="{
				width: `${computedWidth}px`,
				height: `${computedHeight}px`,
				display: 'flex',
				flexWrap: 'wrap',
				backgroundColor: '#eee',
				borderRadius: '5px',
				position: 'relative',
			}"
			@blur="onblurtext"
			ref="replyInput"
			@input="onDivInput($event, i)"
			tabindex="0"
			@paste="changeContent($event, i)"
			:contenteditable="ispaste"
			spellcheck="false"
			:placeholder="state.placetext"
			class="pastimgg"
		>
			<!-- <img :src="state.bigimg" class="imgsty" alt=""  /> -->
			<!-- @click.stop="imgclickk('img')" -->

			<!-- <div style="width: 100%; height: 100%; background-color: #eee; color: white; font-size: 30px; justify-content: center; align-items: center;">
                <span>{{ textnow }}</span>
            </div> -->
			<div></div>
		</section>
		<!-- <div v-else-if="(!state.divshow||state.bigimg)&&ispaste" style="width: 100%; height: 100%; background: #eee;  position: relative;" class="flexcenter">
            <img :src="state.bigimg" class="imgsty" alt="" @click="imgclick"/>
        </div>
        <div v-else style="width: 100%; height: 100%; justify-content: center; background: #eee; align-items: center; display: flex;  position: relative;">
            <img :src="state.bigimg" class="imgsty" alt="" />
        </div> -->
	</div>
</template>

<script lang="ts" setup name="pasterMf">
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, reactive, onMounted, getCurrentInstance, onUnmounted, defineProps, defineAsyncComponent, computed, nextTick, toRaw, defineExpose, watch, inject, defineEmits } from 'vue';
//  import { defineConfig, loadEnv, ConfigEnv } from 'vite';

//  const clickoutside = {
//         bind(el, binding, vnode) {
//             function documentHandler(e) {
//                 if (el.contains(e.target)) {
//                     return false;
//                 }
//                 if (binding.expression) {
//                     binding.value(e);
//                 }
//             }

//             el.vueClickOutside = documentHandler;
//             let a = document.addEventListener('click', documentHandler);
//         },
//         // update() {
//         // },
//         unbind(el, binding) {
//             document.removeEventListener('click', el.vueClickOutside);
//             delete el.vueClickOutside;
//         },
//     };

const props = defineProps({
	// rowdata: {
	//     type: Object,
	//     default: () => ({}),
	// },
	ispaste: {
		type: Boolean,
		default: true,
	},
	i: {
		type: Number,
		default: 1,
	},
	keyy: {
		type: Array,
		default: function () {
			return [1, 2];
		},
	},
	imagesStr: {
		type: Array,
		default: function () {
			return [];
		},
	},
	name: {
		type: String,
		default: '',
	},
	hownum: {
		type: String,
		default: '',
	},
	inputedit: {
		type: Boolean,
		default: false,
	},
	upstyle: {
		type: Object,
		default: () => ({ height: 65, width: 65 }),
	},
});

const computedWidth = computed(() => props.upstyle.width || 65);
const computedHeight = computed(() => props.upstyle.height || 65);

const state = reactive({
	placetext: '',
	canvasimg: [],
	bigimg: '',
	isborder: false,
	showborder: true,
	keyarr: [],
	divshow: true,
	textshow: '',
	longto: false,

	imgArr: [],
});

// 在 setup 内部使用
// const emit = defineEmits(['clickoutside']);

onMounted(async () => {
	var e = 10;
	var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
		a = t.length,
		n = '';
	for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
	state.randrom = n;

	state.keyarr = props.keyy;
	state.namenum = props.hownum;

	state.imgArr = props.imagesStr;
});

const emit = defineEmits(['update:imagesStr', 'ischange', 'childtoimg', 'callback']);

const moudleclick = () => {
	state.isborder = true;
};

const ischange = (val) => {
	if (val) {
		emit('ischange', val);
	}
};
const uploadToServer = (file, callback) => {
	var xhr = new XMLHttpRequest();
	var formData = new FormData();
	formData.append('file', file);
	// const env = loadEnv('', process.cwd());
	xhr.open('post', 'https://nanc.yunhanmy.com:10010' + '/api/uploadnew/file/UploadCommonFileAsync');
	// xhr.open('post', env.VITE_APP_BASE_API_UpLoadNew_Domin+'/api/uploadnew/file/UploadCommonFileAsync')
	xhr.withCredentials = true;
	xhr.responseType = 'json';
	xhr.send(formData);
	xhr.onreadystatechange = () => {
		if (xhr.readyState === 4 && xhr.status === 200) {
			// debugger;
			callback(xhr.response);
		}
	};
};
const changeContent = (e, index) => {
	let _this = state;
	_this.imgArr = props.imagesStr; //给图片数组赋值，同步父组件图片数据，防止图片数组为空
	_this.draw = true;
	ischange(true);
	const dataTransferItemList = e.clipboardData.items;
	const items = [].slice.call(dataTransferItemList).filter(function (item) {
		return item.type.indexOf('image') !== -1;
	});

	if (items.length === 0 && props.inputedit) {
		const ee = document.getElementById('quanmodule' + _this.keyarr[0]).children;
		let a = ee[0].children[0].children[1].children[0].children[props.keyy[1]].children[0];
		if (a.innerText.length >= 50) {
			setTimeout(() => {
				a.innerText = a.innerText.slice(0, 50);
				a.style.fontSize = '20px';
				a.style.wordBreak = 'break-all';
				_this.textshow = a.innerText;
			}, 10);

			ElMessage('已为你限制字数为50');
		}
		return;
	}
	// _this.divshow = false;

	const dataTransferItem = items[0];
	const file = dataTransferItem?.getAsFile();
	if (file) {
		uploadToServer(file, (res) => {
			// _this.canvasimg.push(res.data.url);
			if (res.success) _this.bigimg = res.data.url;

			_this.imgArr.push(res.data.url);
			emit('update:imagesStr', _this.imgArr);
			// emit("childtoimg",props.name,[props.keyy,res.data])
			// emit("callback",res)
		});

		var event = event || window.event;
		// var file = event.target.files[0];
		var reader = new FileReader();
		reader.onload = function (e) {
			_this.canvasimg[index] = e.target.result;
		};
		reader.readAsDataURL(file);
	}

	setTimeout(() => {
		imgclick();
	}, 300);
};
// 清除图片数据
const clearMethods = () => {
	state.imgArr = [];
};
const imgclick = (val) => {
	state.divshow = true;
	state.bigimg = false;
};
const onblurtext = () => {
	let res = {
		url: state.textshow,
		filePath: '',
		fileName: '',
	};
	emit('childtoimg', props.name, [props.keyy, res]);
};
const onDivInput = (e, index) => {
	let _this = state;
	emit('ischange', true);
	if (!props.inputedit) {
		if (e.target.innerText) {
			e.target.innerText = '';
			_this.divshow = true;
			return;
		}
	}
	_this.divshow = false;
};
const qulist = (index) => {
	let DomList = document.getElementById('replyInput' + index);
	let img = document.getElementsByName('img');
};
defineExpose({
	clearMethods,
});
</script>

<style lang="scss" scoped>
.pastimgg {
	// width: 100%;
	// background-color: #eee;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}
.imgsty {
	max-width: 100%;
	max-height: 100%;
	height: auto;
	width: auto;
	// top: 0;
	// left: 0;
	// position: absolute;
}
.flexcenter {
	display: flex;
	justify-content: center;
	align-items: center;
}
.pastimgg {
	cursor: pointer;
	color: transparent;
}
</style>
