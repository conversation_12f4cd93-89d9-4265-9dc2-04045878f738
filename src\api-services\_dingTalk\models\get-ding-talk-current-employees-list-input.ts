/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 获取在职员工列表参数
 *
 * @export
 * @interface GetDingTalkCurrentEmployeesListInput
 */
export interface GetDingTalkCurrentEmployeesListInput {

    /**
     * 在职员工状态筛选，可以查询多个状态。不同状态之间使用英文逗号分隔。2：试用期、3：正式、5：待离职、-1：无状态
     *
     * @type {string}
     * @memberof GetDingTalkCurrentEmployeesListInput
     */
    statusList?: string | null;

    /**
     * 分页游标，从0开始。根据返回结果里的next_cursor是否为空来判断是否还有下一页，且再次调用时offset设置成next_cursor的值。
     *
     * @type {number}
     * @memberof GetDingTalkCurrentEmployeesListInput
     */
    offset?: number;

    /**
     * 分页大小，最大50。
     *
     * @type {number}
     * @memberof GetDingTalkCurrentEmployeesListInput
     */
    size?: number;
}
