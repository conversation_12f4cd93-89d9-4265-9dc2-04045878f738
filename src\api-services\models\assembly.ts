/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CustomAttributeData } from './custom-attribute-data';
import { MethodInfo } from './method-info';
import { Module } from './module';
import { SecurityRuleSet } from './security-rule-set';
import { Type } from './type';
import { TypeInfo } from './type-info';
 /**
 * 
 *
 * @export
 * @interface Assembly
 */
export interface Assembly {

    /**
     * @type {Array<TypeInfo>}
     * @memberof Assembly
     */
    definedTypes?: Array<TypeInfo> | null;

    /**
     * @type {Array<Type>}
     * @memberof Assembly
     */
    exportedTypes?: Array<Type> | null;

    /**
     * @type {string}
     * @memberof Assembly
     */
    codeBase?: string | null;

    /**
     * @type {MethodInfo}
     * @memberof Assembly
     */
    entryPoint?: MethodInfo;

    /**
     * @type {string}
     * @memberof Assembly
     */
    fullName?: string | null;

    /**
     * @type {string}
     * @memberof Assembly
     */
    imageRuntimeVersion?: string | null;

    /**
     * @type {boolean}
     * @memberof Assembly
     */
    isDynamic?: boolean;

    /**
     * @type {string}
     * @memberof Assembly
     */
    location?: string | null;

    /**
     * @type {boolean}
     * @memberof Assembly
     */
    reflectionOnly?: boolean;

    /**
     * @type {boolean}
     * @memberof Assembly
     */
    isCollectible?: boolean;

    /**
     * @type {boolean}
     * @memberof Assembly
     */
    isFullyTrusted?: boolean;

    /**
     * @type {Array<CustomAttributeData>}
     * @memberof Assembly
     */
    customAttributes?: Array<CustomAttributeData> | null;

    /**
     * @type {string}
     * @memberof Assembly
     */
    escapedCodeBase?: string | null;

    /**
     * @type {Module}
     * @memberof Assembly
     */
    manifestModule?: Module;

    /**
     * @type {Array<Module>}
     * @memberof Assembly
     */
    modules?: Array<Module> | null;

    /**
     * @type {boolean}
     * @memberof Assembly
     */
    globalAssemblyCache?: boolean;

    /**
     * @type {number}
     * @memberof Assembly
     */
    hostContext?: number;

    /**
     * @type {SecurityRuleSet}
     * @memberof Assembly
     */
    securityRuleSet?: SecurityRuleSet;
}
