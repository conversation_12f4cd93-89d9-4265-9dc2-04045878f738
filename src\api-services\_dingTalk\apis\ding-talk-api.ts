/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../../base';
import { AdminResultDingTalkBaseResponseGetDingTalkCurrentEmployeesListOutput } from '../models';
import { AdminResultDingTalkBaseResponseListDingTalkEmpRosterFieldVo } from '../models';
import { AdminResultDingTalkSendInteractiveCardsOutput } from '../models';
import { AdminResultGetDingTalkTokenOutput } from '../models';
import { DingTalkSendInteractiveCardsInput } from '../models';
import { GetDingTalkCurrentEmployeesListInput } from '../models';
import { GetDingTalkCurrentEmployeesRosterListInput } from '../models';
/**
 * DingTalkApi - axios parameter creator
 * @export
 */
export const DingTalkApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {GetDingTalkCurrentEmployeesListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet: async (body: GetDingTalkCurrentEmployeesListInput, accessToken: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet.');
            }
            // verify required parameter 'accessToken' is not null or undefined
            if (accessToken === null || accessToken === undefined) {
                throw new RequiredError('accessToken','Required parameter accessToken was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet.');
            }
            const localVarPath = `/api/dingTalk/dingTalkCurrentEmployeesList/{access_token}`
                .replace(`{${"access_token"}}`, encodeURIComponent(String(accessToken)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {GetDingTalkCurrentEmployeesRosterListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet: async (body: GetDingTalkCurrentEmployeesRosterListInput, accessToken: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet.');
            }
            // verify required parameter 'accessToken' is not null or undefined
            if (accessToken === null || accessToken === undefined) {
                throw new RequiredError('accessToken','Required parameter accessToken was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet.');
            }
            const localVarPath = `/api/dingTalk/dingTalkCurrentEmployeesRosterList/{access_token}`
                .replace(`{${"access_token"}}`, encodeURIComponent(String(accessToken)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} token 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkSendInteractiveCardsTokenPost: async (token: string, body?: DingTalkSendInteractiveCardsInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'token' is not null or undefined
            if (token === null || token === undefined) {
                throw new RequiredError('token','Required parameter token was null or undefined when calling apiDingTalkDingTalkSendInteractiveCardsTokenPost.');
            }
            const localVarPath = `/api/dingTalk/dingTalkSendInteractiveCards/{token}`
                .replace(`{${"token"}}`, encodeURIComponent(String(token)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkTokenGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/dingTalk/dingTalkToken`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DingTalkApi - functional programming interface
 * @export
 */
export const DingTalkApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {GetDingTalkCurrentEmployeesListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body: GetDingTalkCurrentEmployeesListInput, accessToken: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDingTalkBaseResponseGetDingTalkCurrentEmployeesListOutput>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body, accessToken, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {GetDingTalkCurrentEmployeesRosterListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body: GetDingTalkCurrentEmployeesRosterListInput, accessToken: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDingTalkBaseResponseListDingTalkEmpRosterFieldVo>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body, accessToken, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} token 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkSendInteractiveCardsTokenPost(token: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkSendInteractiveCardsTokenPost(token, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultGetDingTalkTokenOutput>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkTokenGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DingTalkApi - factory interface
 * @export
 */
export const DingTalkApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {GetDingTalkCurrentEmployeesListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body: GetDingTalkCurrentEmployeesListInput, accessToken: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDingTalkBaseResponseGetDingTalkCurrentEmployeesListOutput>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body, accessToken, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {GetDingTalkCurrentEmployeesRosterListInput} body 
         * @param {string} accessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body: GetDingTalkCurrentEmployeesRosterListInput, accessToken: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDingTalkBaseResponseListDingTalkEmpRosterFieldVo>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body, accessToken, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} token 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkSendInteractiveCardsTokenPost(token: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkSendInteractiveCardsTokenPost(token, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultGetDingTalkTokenOutput>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkTokenGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DingTalkApi - object-oriented interface
 * @export
 * @class DingTalkApi
 * @extends {BaseAPI}
 */
export class DingTalkApi extends BaseAPI {
    /**
     * 
     * @summary 获取在职员工列表 🔖
     * @param {GetDingTalkCurrentEmployeesListInput} body 
     * @param {string} accessToken 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body: GetDingTalkCurrentEmployeesListInput, accessToken: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDingTalkBaseResponseGetDingTalkCurrentEmployeesListOutput>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenGet(body, accessToken, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取员工花名册字段信息 🔖
     * @param {GetDingTalkCurrentEmployeesRosterListInput} body 
     * @param {string} accessToken 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body: GetDingTalkCurrentEmployeesRosterListInput, accessToken: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDingTalkBaseResponseListDingTalkEmpRosterFieldVo>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenGet(body, accessToken, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 发送钉钉互动卡片 🔖
     * @param {string} token 
     * @param {DingTalkSendInteractiveCardsInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkSendInteractiveCardsTokenPost(token: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkSendInteractiveCardsTokenPost(token, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取企业内部应用的access_token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultGetDingTalkTokenOutput>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkTokenGet(options).then((request) => request(this.axios, this.basePath));
    }
}
