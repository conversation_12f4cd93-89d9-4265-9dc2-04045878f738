/* tslint:disable */
/* eslint-disable */
/**
 * GoView
 * GoView 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 2.2.8
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { GoViewProState } from './go-view-pro-state';
 /**
 * GoView 项目详情
 *
 * @export
 * @interface GoViewProDetailOutput
 */
export interface GoViewProDetailOutput {

    /**
     * 项目Id
     *
     * @type {number}
     * @memberof GoViewProDetailOutput
     */
    id?: number;

    /**
     * 项目名称
     *
     * @type {string}
     * @memberof GoViewProDetailOutput
     */
    projectName?: string | null;

    /**
     * @type {GoViewProState}
     * @memberof GoViewProDetailOutput
     */
    state?: GoViewProState;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof GoViewProDetailOutput
     */
    createTime?: Date | null;

    /**
     * 预览图片url
     *
     * @type {string}
     * @memberof GoViewProDetailOutput
     */
    indexImage?: string | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof GoViewProDetailOutput
     */
    createUserId?: number | null;

    /**
     * 项目备注
     *
     * @type {string}
     * @memberof GoViewProDetailOutput
     */
    remarks?: string | null;

    /**
     * 项目内容
     *
     * @type {string}
     * @memberof GoViewProDetailOutput
     */
    content?: string | null;
}
