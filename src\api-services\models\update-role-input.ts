/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DataScopeEnum } from './data-scope-enum';
import { StatusEnum } from './status-enum';
 /**
 * 
 *
 * @export
 * @interface UpdateRoleInput
 */
export interface UpdateRoleInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateRoleInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateRoleInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateRoleInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateRoleInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateRoleInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateRoleInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateRoleInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateRoleInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateRoleInput
     */
    tenantId?: number | null;

    /**
     * 编码
     *
     * @type {string}
     * @memberof UpdateRoleInput
     */
    code?: string | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateRoleInput
     */
    orderNo?: number;

    /**
     * @type {DataScopeEnum}
     * @memberof UpdateRoleInput
     */
    dataScope?: DataScopeEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateRoleInput
     */
    remark?: string | null;

    /**
     * @type {StatusEnum}
     * @memberof UpdateRoleInput
     */
    status?: StatusEnum;

    /**
     * 名称
     *
     * @type {string}
     * @memberof UpdateRoleInput
     */
    name: string;

    /**
     * 菜单Id集合
     *
     * @type {Array<number>}
     * @memberof UpdateRoleInput
     */
    menuIdList?: Array<number> | null;
}
