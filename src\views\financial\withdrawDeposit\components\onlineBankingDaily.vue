<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-date-picker
					v-model="timeRange"
					type="date"
					placeholder="日期"
					@change="changeTime"
					:clearable="false"
					style="width: 150px"
					format="YYYY/MM/DD"
					value-format="YYYY-MM-DD"
					:shortcuts="dateShortcuts"
					class="publicCss"
				/>
				<el-input v-model.trim="query.accountUserName" placeholder="用户名(模糊搜索提现账号)" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.account" placeholder="别名" class="publicCss" clearable filterable>
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.cardNature" placeholder="银行性质" class="publicCss" clearable filterable>
					<el-option v-for="item in natureList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				:pageSize="50"
				id="************"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				showsummary
				:query="query"
				:isAsc="false"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:query-api="QueryWithDrawOnlineBankBalance"
			>
				<template #toolbar_buttons>
					<el-button @click="computeProps" type="primary">计算</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<span style="margin: 0 5px; color: red">计算时间：{{ calculationTime }}</span>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" title="编辑" width="400" draggable overflow>
		<div style="height: 230px">
			<el-form :model="singleform" :rules="singlerules">
				<el-form-item label="截图" :label-width="'90px'">
					<div style="height: 70px">
						<uploadMf v-if="editVisible" v-model:imagesStr="singleform.imgs" ref="refuploadMf" :upstyle="{ height: 40, width: 40 }" :limit="3" uploadName="上传"></uploadMf>
					</div>
				</el-form-item>
				<el-form-item label="备注" :label-width="'90px'" style="white-space: pre-wrap; word-break: break-all">
					<el-input
						v-model="singleform.remark"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						style="width: 700px"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave"> 确定 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { QueryWithDrawOnlineBankBalance, UpdateWithDrawOnlineBankBalance, ComputeWithDrawOnlineBankBalance, ExportWithDrawOnlineBankBalance, QueryCwComputeTime } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { ElMessageBox, ElLoading } from 'element-plus';
import { bankList, platformlist, expressCompany } from '/@/utils/tools';
import { dateTimeShortcuts, dateRangeShortcuts, dateShortcuts } from '/@/utils/shortcuts';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const timeRange = ref('');
const calculationTime = ref('');
const nameList = ref<Public.options[]>([]);
const natureList = ref<Public.options[]>([]);
const table = ref();
const singleform = ref<{
	remark: string;
	imgs: string[];
}>({
	remark: '',
	imgs: [],
});

const singlerules = {};
const query = ref({
	startTime: '',
	endTime: '',
	account: '',
	accountUserName: '',
	cardNature: '',
});
const paging = ref({
	currentPage: 1,
	pageSize: 50,
	orderBy: '',
	isAsc: false,
});
const changeTime = (val: string[] | string | null | undefined) => {
	if (Array.isArray(val)) {
		query.value.startTime = val.join(', ');
		query.value.endTime = val.join(', ');
	} else {
		query.value.startTime = val || '';
		query.value.endTime = val || '';
	}
};

const onSingleSave = async () => {
	let imgArr = singleform.value.imgs;
	let imgs = imgArr.join(',');
	const { success } = await UpdateWithDrawOnlineBankBalance({ ...singleform.value, imgs });
	if (success) {
		window.$message.success('编辑成功');
		editVisible.value = false;
		getList();
	}
};

const computeProps = async () => {
	ElMessageBox.confirm('是否确认计算？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { success } = await ComputeWithDrawOnlineBankBalance({ ...query.value });
		if (success) {
			window.$message.success('计算成功');
			getList();
		}
	});
};

const exportProps = async () => {
	await ExportWithDrawOnlineBankBalance({ ...query.value, ...paging.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onEdit = (row: any) => {
	singleform.value = row
		? {
				...JSON.parse(JSON.stringify(row)),
				imgs: Array.isArray(row.imgs) ? JSON.parse(JSON.stringify(row.imgs)) : [],
				remark: row.remark ?? '',
			}
		: { yesterdayBalance: '', remark: '', imgs: [] };
	editVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
	calculationTimeMethod();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'withDrawDate', title: '日期', width: '90' },
	{ sortable: true, field: 'accountName', title: '提现账号(别名)' },
	{ sortable: true, field: 'cardNature', title: '银行性质', width: '90' },
	{ sortable: true, field: 'cardType', title: '卡号类型', width: '110' },
	// { sortable: true, field: 'yesterdayBalance', title: '昨日余额' },
	{ sortable: true, field: 'inAmount', title: '收入金额', formatter: 'fmtAmt2', align: 'right' },
	// { sortable: true, field: 'withDrawAmount', title: '提现金额' },
	// { sortable: true, field: 'receivedAmount', title: '到账金额' },
	{ sortable: true, field: 'outAmount', title: '转出金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'balance', title: '余额(复核后)', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'lastBalance', title: '上日余额', formatter: 'fmtAmt2', align: 'right' },
	{ field: 'imgs', title: '截图', type: 'image', width: '100' },
	{ sortable: true, field: 'remark', title: '备注' },
	{
		title: '操作',
		align: 'center',
		width: '90',
		type: 'btnList',
		field:'**************',
		minWidth: '90',
		direction: 'column',
		btnList: [{ title: '编辑', handle: onEdit }],
		fixed: 'right',
	},
]);

const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	nameList.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银');
		})
		.map((item: any) => ({ label: item.accountName, value: item.account }));
	natureList.value = data1.list
		.map((item: any) => ({ label: item.cardNature, value: item.cardNature }))
		.filter((item: any) => item.label !== null && item.value !== null)
		.reduce((acc: any, current: any) => {
			const exists = acc.some((item: any) => item.label === current.label && item.value === current.value);
			if (!exists) {
				acc.push(current);
			}
			return acc;
		}, []);
};

const calculationTimeMethod = async () => {
	const { data: data2, success: success2 } = await QueryCwComputeTime({ type: 1, dataTime: query.value.startTime });
	if (!success2) return;
	calculationTime.value = data2 && data2.computeTime ? dayjs(data2.computeTime).format('YYYY-MM-DD HH:mm:ss') : '';
};

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any, index: any) => {
		item.imgs = item.imgs ? item.imgs.split(',') : [];
		item.withDrawDate = dayjs(item.withDrawDate).format('YYYY-MM-DD');
	});
	paging.value.currentPage = data.query.currentPage;
	paging.value.pageSize = data.query.pageSize;
	paging.value.orderBy = data.query.orderBy;
	paging.value.isAsc = data.query.isAsc;
	callback(data);
};
onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		query.value.startTime = timeRange.value;
		query.value.endTime = timeRange.value;
	}
	getAllDept();
	calculationTimeMethod();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number .el-input__inner {
	text-align: left;
}
</style>
