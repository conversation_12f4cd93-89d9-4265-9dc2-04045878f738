<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="storageAsset202503161056"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				:isAsc="false"
				:isNeedPager="false"
				:queryApi="GetWarehouseAsset"
				v-loading="loading"
			>
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog title="在途明细" v-model="showRefundDialog" width="45%" draggable overflow style="margin-top: -30vh !important">
		<div style="height: 100px">
			<el-table :data="subTableData" style="width: 100%" tooltip-effect="light" row-key="id" border="">
				<el-table-column prop="zrsaleTransit" label="昨日销售在途" width="155" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.zrsaleTransit) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="payAmount" label="实发金额" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.payAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="currentReturnAmount" label="当期退货金额" width="155" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.currentReturnAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="totalGoodsIncome" label="平台总收入" width="165" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.totalGoodsIncome) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="saleTransit" label="销售在途" width="155" align="right">
					<template #default="scope">
						<span>{{ formatThousands(scope.row.saleTransit) }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { GetWarehouseAsset, ExportWarehouseAsset } from '/@/api/cwManager/cwFundsDailyBalance';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const loading = ref(false);
const table = ref();
const query = ref({
	startDate: '',
	endDate: '',
});
const showRefundDialog = ref(false);
const subTableData = ref<any[]>([]);
const onInTransitDetails = (row: any) => {
	const time = row.assetDate ? dayjs(row.assetDate).subtract(1, 'day').format('YYYY-MM-DD') : '';
	GetWarehouseAsset({ startDate: time, endDate: time }).then((res: any) => {
		subTableData.value = [];
		showRefundDialog.value = true;
		if (res.data.list.length > 0) {
			subTableData.value = [
				{
					zrsaleTransit: res.data.list[0].saleTransit,
					payAmount: row.payAmount,
					currentReturnAmount: row.currentReturnAmount,
					totalGoodsIncome: row.totalGoodsIncome,
					saleTransit: row.saleTransit,
				},
			];
		}
	});
};

const formatThousands = (value: number) => {
	if (!value) return '0';
	const numStr = typeof value === 'number' ? value.toFixed(2) : String(value);
	return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const exportProps = async () => {
	loading.value = true;
	await ExportWarehouseAsset({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '仓储资产导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'assetDate', title: '日期', width: '140', formatter: 'formatDate' },
	{ sortable: true, field: 'inventory', title: '国内库存', width: '250', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'buyTransit', title: '采购在途', width: '250', formatter: 'fmtAmt2', align: 'right' },
	{
		sortable: true,
		field: 'saleTransit',
		title: '销售在途',
		width: '250',
		formatter: (row: any) => (row.saleTransit ? row.saleTransit.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : row.saleTransit),
		align: 'right',
		type: 'click',
		handle: (row: any) => onInTransitDetails(row),
	},
	{ sortable: true, field: 'deliveryBalance', title: ' 快递面单余额', width: '250', formatter: 'fmtAmt2', align: 'right' },
]);

onMounted(() => {});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
