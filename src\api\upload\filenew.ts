import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_UpLoadNew}/file/`;
const groupPrefix = `${import.meta.env.VITE_APP_BASE_API_UpLoadNew_Domin}`;

// 上传文件
export const UploadCommonFileAsync = (params: any, config = {}) => request.post(groupPrefix + apiPrefix + 'UploadCommonFileAsync', params, config);

export const xmtvideouploadblockasync = (params: any, config = {}) => request.post(groupPrefix + apiPrefix + 'xmtvideouploadblockasync', params, config);
