<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" startPlaceholder="开始时间" endPlaceholder="结束时间" style="width: 230px" />
				<el-input v-model.trim="query.businessId" class="publicCss" placeholder="钉钉流程号" clearable maxlength="50" />
				<el-input v-model.trim="query.vouchersNumber" class="publicCss" placeholder="凭证号" clearable maxlength="50" />
				<el-input v-model.trim="query.chartCode" class="publicCss" placeholder="科目代码" clearable maxlength="50" />
				<el-input v-model.trim="query.chartTitle" class="publicCss" placeholder="科目名称" clearable maxlength="50" />
				<el-input v-model.trim="query.auxiliaryType" class="publicCss" placeholder="辅助核算类型" clearable maxlength="50" />
				<el-input v-model.trim="query.auxiliaryCode" class="publicCss" placeholder="辅助核算编码" clearable maxlength="50" />
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="voucherInformation202412221630"
				:tableCols="tableCols"
				:pageSize="30"
				:pageSizes="[30, 50, 100, 200, 300]"
				:query="query"
				orderBy="createdTime"
				:isAsc="false"
				:isNeedPager="true"
				:queryApi="QueryAccountsVoucherInfo"
			>
				<template #toolbar_buttons>
					<el-button @click="onDerive" type="primary">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="deriveVisible" title="导出" width="400" draggable overflow>
		<div style="height: 150px; display: flex; align-items: center; justify-content: center; padding-bottom: 40px">
			<inputNumberModule v-model="deriveNumber" placeholder="请输入起始凭证号" style="width: 70%" clearable :maxlength="10" :max="*************" :inputDefault="true" />
		</div>
		<div style="display: flex; justify-content: end">
			<el-button @click="deriveVisible = false">取消</el-button>
			<el-button type="primary" @click="exportProps">确定</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits } from 'vue';
import { ElMessageBox, ElMessage, FormRules } from 'element-plus';
import { QueryAccountsVoucherInfo, ExportAccountsVoucherInfo } from '/@/api/cwManager/accountsVouchers';
const inputNumberModule = defineAsyncComponent(() => import('/@/components/yhCom/inputNumberModule.vue'));
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const pageLoading = ref(false);
const deriveVisible = ref(false);
const timeRange = ref('');
const deriveNumber = ref('');
const table = ref();
const query = ref({
	startTime: '',
	endTime: '',
	businessId: '',
	vouchersNumber: '',
	chartCode: '',
	chartTitle: '',
	auxiliaryType: '',
	auxiliaryCode: '',
});

const onDerive = () => {
	deriveNumber.value = '';
	deriveVisible.value = true;
};

const exportProps = async () => {
	if (!deriveNumber.value) {
		ElMessage.warning('请输入起始凭证号');
		return;
	}
	await ExportAccountsVoucherInfo({ ...query.value, number: deriveNumber.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
	deriveVisible.value = false;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.query.orderBy = 'createdTime';
	table.value.getList();
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'createdTime', title: '日期', width: '140' },
	{ sortable: true, field: 'businessId', title: '钉钉流程号', width: '160' },
	{ sortable: true, field: 'vouchersName', title: '凭证字', width: '90' },
	{ sortable: true, field: 'vouchersNumber', title: '凭证号', width: '90' },
	{ sortable: true, field: 'chartRemark', title: '摘要', width: '250' },
	{ sortable: true, field: 'chartCode', title: '科目代码', width: '90' },
	{ sortable: true, field: 'chartTitle', title: '科目名称', width: 'auto' },
	{ sortable: true, field: 'inAmount', title: '借方金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '贷方金额', width: '90', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'auxiliaryType', title: '辅助核算类型', width: '110' },
	{ sortable: true, field: 'auxiliaryCode', title: '辅助核算编码', width: '130' },
	{ sortable: true, field: 'qty', title: '数量', width: '90' },
	{ sortable: true, field: 'price', title: '单价', width: '90', formatter: 'fmtAmt2', align: 'right' },
]);

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(0, 'day').format('YYYY-MM-DD');
		query.value.startTime = timeRange.value;
		query.value.endTime = timeRange.value;
	}
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
