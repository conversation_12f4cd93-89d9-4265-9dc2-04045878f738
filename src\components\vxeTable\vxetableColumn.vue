<template>
	<template v-if="props.type === 'image'" v-auth="props.item.permissions">
		<!-- 字符串图片 -->
		<el-badge
			v-if="props.row[props.item.field] && !Array.isArray(props.row[props.item.field]) && props.row[props.item.field][0] != '[' && props.row[props.item.field].split(',').length > 0"
			:value="props.row[props.item.field].split(',').length > 1 ? props.row[props.item.field].split(',').length : ''"
			class="item"
		>
			<el-image
				style="width: 50px; height: 34px"
				:src="props.row[props.item.field].split(',')[0]"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:preview-src-list="props.row[props.item.field].split(',')"
				:initial-index="4"
				fit="cover"
			/>
		</el-badge>
		<!-- 数组图片 -->
		<el-badge
			v-else-if="props.row[props.item.field] && Array.isArray(props.row[props.item.field]) && props.row[props.item.field].length > 0"
			:value="props.row[props.item.field].length > 1 ? props.row[props.item.field].length : ''"
			class="item"
		>
			<el-image
				style="width: 50px; height: 34px"
				:src="props.row[props.item.field][0]"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:preview-src-list="props.row[props.item.field]"
				:initial-index="4"
				fit="cover"
			/>
		</el-badge>
		<!-- JSON数组图片 -->
		<el-badge
			v-else-if="props.row[props.item.field] && !Array.isArray(props.row[props.item.field]) && props.row[props.item.field][0] == '[' && JSON.parse(props.row[props.item.field]).length > 0"
			:value="JSON.parse(props.row[props.item.field]).length > 1 ? JSON.parse(props.row[props.item.field]).length : ''"
			class="item"
		>
			<el-image
				style="width: 50px; height: 34px"
				:src="JSON.parse(props.row[props.item.field])[0]"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:preview-src-list="JSON.parse(props.row[props.item.field])"
				:initial-index="4"
				fit="cover"
			/>
		</el-badge>
	</template>
	<template v-if="props.type === 'click'" v-auth="props.item.permissions">
		<div class="alignDisplay" :style="{ justifyContent: props.item.align == 'center' ? 'center' : props.item.align == 'right' ? 'end' : 'start' }">
			<el-button v-if="props.item.copy !== true" type="primary" link @click="props.item.handle && props.item.handle(row)" :disabled="props.item.isDisabled && props.item.isDisabled(row)">
				{{ formats(row, props.item) }}
			</el-button>
			<div v-else @click="props.item.handle && props.item.handle(row)" style="color: #64c5b1; cursor: pointer">
				{{ formats(row, props.item) }}
			</div>
		</div>
	</template>
	<template v-if="props.type === 'btnList'" v-auth="props.item.permissions">
		<div :class="props.item.direction === 'row' || props.item.direction === undefined ? 'alignDisplay' : 'alignDisplayColumn'" :style="batchStyle(props.item.direction)">
			<div v-if="props.item.btnList && Array.isArray(props.item.btnList)" v-for="btnItem in props.item.btnList" class="publicMargin">
				<el-button v-if="btnItem.permissions" type="primary" link @click="btnItem.handle && btnItem.handle(row)" :disabled="btnItem.isDisabled && btnItem.isDisabled(row)" v-auth="btnItem.permissions">
					{{ formats(row, btnItem) }}
				</el-button>
				<el-button v-else type="primary" link @click="btnItem.handle && btnItem.handle(row)" :disabled="btnItem.isDisabled && btnItem.isDisabled(row)">
					{{ formats(row, btnItem) }}
				</el-button>
			</div>
		</div>
	</template>
	<template v-if="props.type === 'html'" v-auth="props.item.permissions">
		<div class="alignDisplay" :style="{ justifyContent: props.item.align == 'center' ? 'center' : props.item.align == 'right' ? 'end' : 'start' }">
			<div v-if="props.item.formatter && typeof props.item.formatter === 'function'" class="publicMargin" v-html="props.item.formatter(row)"></div>
		</div>
	</template>
	<template v-if="props.type === 'switch'" v-auth="props.item.permissions">
		<div class="alignDisplay" :style="{ justifyContent: props.item.align == 'center' ? 'center' : props.item.align == 'right' ? 'end' : 'start' }">
			<el-switch v-model="props.row[props.item.field]" @change="props.item.handle && props.item.handle(row)" />
		</div>
	</template>
</template>

<script setup lang="ts" name="">
import { ref, onMounted } from 'vue';
import { formatters } from '/@/utils/vxetableFormats';
const props = defineProps({
	tableCols: { type: Array<VxeTable.Columns>, default: () => [] }, //列头
	data: { type: Array, default: () => [] }, //列表数据
	item: { type: Object, default: () => {} }, //当前行配置信息
	rowIndex: { type: Number, default: null }, //当前行下标
	row: { type: Object, default: () => {} }, //当前行数据
	type: { type: String, default: '' }, //当前列类型
});

const batchStyle = (direction: string | undefined) => {
	if (direction === undefined || direction === 'row') {
		return { justifyContent: props.item.align == 'center' ? 'center' : props.item.align == 'right' ? 'end' : 'start' };
	} else if (direction === 'column') {
		return { alignItems: props.item.align == 'center' ? 'center' : props.item.align == 'right' ? 'end' : 'start' };
	}
};

const formats = (row: any, item: VxeTable.Columns): any => {
	if (item.formatter && typeof item.formatter === 'function') {
		return item.formatter(row);
	} else if (item.formatter && typeof item.formatter === 'string' && item.formatter === 'formatLinkProcode') {
		return formatters.formatLinkProcode(row[item.field!], row.platform);
	} else if (item.formatter && typeof item.formatter === 'string') {
		return formatters[item.formatter](row[item.field!]);
	}
	return row[item.field!] ? row[item.field!] : item.title;
};
</script>

<style scoped lang="scss">
.item {
	height: 36px;
	::v-deep .el-badge__content.is-fixed {
		top: 8px;
	}
}
.alignDisplay {
	display: flex;
	justify-content: center;
}
.alignDisplayColumn {
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>
