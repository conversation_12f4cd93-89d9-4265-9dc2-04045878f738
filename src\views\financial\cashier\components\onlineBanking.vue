<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-select v-model="query.accountName" placeholder="别名" class="publicCss" filterable clearable>
					<el-option v-for="item in expenseTypeList" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select v-model="query.bankType" placeholder="行别" class="publicCss" filterable clearable>
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.account" class="publicCss" placeholder="卡号" clearable maxlength="50" />
				<el-input v-model="query.showUserName" class="publicCss" placeholder="持有人" clearable maxlength="50" />
				<el-select v-model="query.cardType" placeholder="卡号类型" class="publicCss" clearable filterable>
					<el-option key="提现网银" label="提现网银" value="提现网银" />
					<el-option key="出纳账单" label="出纳账单" value="出纳账单" />
					<el-option key="其他对公网银" label="其他对公网银" value="其他对公网银" />
				</el-select>
				<el-select v-model="query.cardNature" placeholder="卡号性质" class="publicCss" clearable filterable>
					<el-option key="个人" label="个人" value="个人" />
					<el-option key="一般户" label="一般户" value="一般户" />
					<el-option key="基本户" label="基本户" value="基本户" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="startImport" type="primary">导入</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<el-button @click="onImportTemplate" type="primary">下载导入模版</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="************" :tableCols="tableCols" :query="query" :queryApi="QueryOnlineBankSet">
				<template #toolbar_buttons>
					<el-button @click="handleAdd" type="primary">新增</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="addVisible" :title="isEdit ? '编辑' : '新增'" width="25%" draggable :close-on-click-modal="false">
		<el-form label-width="auto" :model="ruleForm" style="max-width: 600px" :rules="rules" v-if="addVisible" ref="ruleFormRef">
			<el-form-item label="别名:" label-position="right" prop="accountName">
				<el-input v-model="ruleForm.accountName" class="publicCss" placeholder="别名" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="行别:" label-position="right" prop="bankType">
				<el-select v-model="ruleForm.bankType" placeholder="行别" class="publicCss" filterable clearable>
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="卡号:" label-position="right" prop="account">
				<el-input v-model="ruleForm.account" class="publicCss" placeholder="卡号" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="流水导入类型:" label-position="right" prop="userName">
				<el-input v-model="ruleForm.userName" class="publicCss" placeholder="流水导入类型" clearable maxlength="30" />
			</el-form-item>
			<el-form-item label="开票抬头:" label-position="right">
				<el-input v-model="ruleForm.billHeader" class="publicCss" placeholder="开票抬头" clearable maxlength="30" />
			</el-form-item>
			<el-form-item label="业务别名:" label-position="right">
				<el-input v-model="ruleForm.busAccountName" class="publicCss" placeholder="业务别名" clearable maxlength="30" />
			</el-form-item>
			<el-form-item label="卡号类型:" label-position="right" prop="cardType">
				<el-select v-model="ruleForm.cardType" placeholder="卡号类型" class="publicCss" filterable clearable multiple collapse-tags>
					<el-option key="出纳账单" label="出纳账单" value="出纳账单" />
					<el-option key="提现网银" label="提现网银" value="提现网银" />
					<el-option key="其他对公网银" label="其他对公网银" value="其他对公网银" />
				</el-select>
			</el-form-item>
			<el-form-item label="卡号性质:" label-position="right" prop="cardNature">
				<el-select v-model="ruleForm.cardNature" placeholder="卡号性质" class="publicCss" filterable clearable>
					<el-option key="个人" label="个人" value="个人" />
					<el-option key="基本户" label="基本户" value="基本户" />
					<el-option key="一般户" label="一般户" value="一般户" />
				</el-select>
			</el-form-item>
			<el-form-item label="持有人:" label-position="right" prop="showUserName">
				<el-input v-model="ruleForm.showUserName" class="publicCss" placeholder="持有人" clearable maxlength="30" />
			</el-form-item>
			<el-form-item label="手机号:" label-position="right" prop="phone">
				<el-input v-model="ruleForm.phone" class="publicCss" placeholder="手机号" clearable maxlength="30" />
			</el-form-item>
		</el-form>
		<div style="display: flex; justify-content: center; margin-top: 20px">
			<el-button @click="addVisible = false">取消</el-button>
			<el-button type="primary" @click="Submit(ruleFormRef)" v-reclick="1000">确定</el-button>
		</div>
	</el-dialog>
	<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important">
		<div style="height: 100px">
			<el-upload
				ref="uploadFile"
				class="upload-demo"
				:auto-upload="false"
				:multiple="false"
				:limit="1"
				action=""
				accept=".xlsx"
				:file-list="fileLists"
				:data="fileparm"
				:http-request="onUploadFile"
				:on-success="onUploadSuccess"
				:on-change="onUploadChange"
				:on-remove="onUploadRemove"
			>
				<template #trigger>
					<el-button size="small" type="primary">选取文件</el-button>
				</template>
				<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
			</el-upload>
		</div>
		<div style="display: flex; justify-content: end; align-items: center">
			<el-button @click="dialogVisible = false">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { QueryOnlineBankSet, InsertOrUpdateOnlineBankSet, DeleteOnlineBankSet, ExportOnlineBankSet, ImportOnlineBankSet } from '/@/api/cwManager/cashierSet';
import { bankList } from '/@/utils/tools';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const yhUserSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const addVisible = ref(false);
const isEdit = ref(false);
const ruleFormRef = ref<FormInstance>();
const dialogVisible = ref(false);
const uploadLoading = ref(false);
const fileLists = ref([]);
const expenseTypeList = ref<string[]>([]);
const uploadFile = ref();
const fileparm = ref({});
const ruleForm = ref({
	accountName: '',
	account: '',
	bankType: '',
	userName: '',
	billHeader: '',
	ddUserId: '',
	area: '',
	busAccountName: '',
	cardType: [],
	cardNature: '',
	showUserName: '',
	phone: '',
});
const table = ref();
const query = ref({
	bankType: '',
	showUserName: '',
	cardNature: '',
	cardType: '',
	accountName: '',
	account: '',
});
const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	var res = await ImportOnlineBankSet(form);
	if (res?.success) window.$message({ message: '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
//导入弹窗
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const rules = {
	accountName: [{ required: true, message: '请输入别名', trigger: 'blur' }],
	bankType: [{ required: true, message: '请选择行别', trigger: 'change' }],
	account: [{ required: true, message: '请输入卡号', trigger: 'blur' }],
	showUserName: [{ required: true, message: '请输入持有人', trigger: 'blur' }],
	cardType: [{ required: true, message: '请选择卡号类型', trigger: 'change' }],
	cardNature: [{ required: true, message: '请选择卡号性质', trigger: 'change' }],
	userName: [{ required: true, message: '请输入流水导入类型', trigger: 'blur' }],
};
const exportProps = async () => {
	await ExportOnlineBankSet({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};
const Submit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			let card = ruleForm.value.cardType;
			let cardType;
			if (Array.isArray(card)) {
				cardType = card.join(',');
			} else {
				cardType = '';
			}
			const { success } = await InsertOrUpdateOnlineBankSet({ ...ruleForm.value, cardType });
			if (success) {
				ElMessage.success(isEdit.value ? '保存成功' : '新增成功');
				getList();
				clear();
				addVisible.value = false;
			}
		} else {
			ElMessage.error('表单验证失败!');
		}
	});
};
const clear = () => {
	ruleForm.value = {
		userName: '',
		accountName: '',
		bankType: '',
		account: '',
		area: '',
		ddUserId: '',
		busAccountName: '',
		billHeader: '',
		cardType: [],
		cardNature: '',
		showUserName: '',
		phone: '',
	};
};
const handleAdd = () => {
	isEdit.value = false;
	clear();
	addVisible.value = true;
};
const onImportTemplate = () => {
	const aLink = document.createElement('a');
	aLink.href = '/excel/cwManage/网银别名设置导入模版.xlsx';
	aLink.setAttribute('download', '网银别名设置导入模版.xlsx');
	document.body.appendChild(aLink);
	aLink.click();
	document.body.removeChild(aLink);
};
const handleEdit = (row: any) => {
	isEdit.value = true;
	ruleForm.value = JSON.parse(JSON.stringify(row));
	ruleForm.value.cardType = row.cardType ? row.cardType.split(',') : [];
	addVisible.value = true;
};
const handleDelete = async (row: any) => {
	ElMessageBox.confirm('此操作将删除此数据', '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await DeleteOnlineBankSet(row);
			if (success) {
				ElMessage.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '已取消删除',
			});
		});
};

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ sortable: true, field: 'accountName', title: '别名' },
	{ sortable: true, field: 'bankType', title: '行别' },
	{ sortable: true, field: 'account', title: '卡号' },
	{ sortable: true, field: 'showUserName', title: '持有人' },
	{ sortable: true, field: 'phone', title: '手机号' },
	{ sortable: true, field: 'userName', title: '流水导入类型' },
	{ sortable: true, field: 'billHeader', title: '开票抬头' },
	{ sortable: true, field: 'busAccountName', title: '业务别名' },
	{ sortable: true, field: 'cardType', title: '卡号类型' },
	{ sortable: true, field: 'cardNature', title: '卡号性质' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		field: '**************',
		btnList: [
			{ title: '编辑', handle: handleEdit },
			{ title: '删除', handle: handleDelete },
		],
	},
]);
const getAllDept = async () => {
	const { data, success } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success) return;
	const accountNamesSet = new Set<string>();
	data.list.forEach((item: any) => {
		if (item.accountName) {
			accountNamesSet.add(item.accountName);
		}
	});
	if (expenseTypeList.value.length === 0) {
		expenseTypeList.value = Array.from(accountNamesSet);
	} else {
		const currentSet = new Set(expenseTypeList.value);
		accountNamesSet.forEach((name) => currentSet.add(name));
		expenseTypeList.value = Array.from(currentSet);
	}
};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss"></style>
