import request from '/@/utils/yhrequest';
//凭证基础设置
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Financewh}/FundsAnalysis/`;

//QueryAllFundCharAnalysis 查询总资金分析
export const QueryAllFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryAllFundCharAnalysis', { params, ...config });

//QueryPlatformFundCharAnalysis 查询平台资金分析
export const QueryPlatformFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryPlatformFundCharAnalysis', { params, ...config });

//QueryCNFundCharAnalysis 查询出纳资金分析
export const QueryCNFundCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCNFundCharAnalysis', { params, ...config });

//QueryCashFlowCharAnalysis 查询总现金流分析
export const QueryCashFlowCharAnalysis = (params: any, config = {}) => request.get(apiPrefix + 'QueryCashFlowCharAnalysis', { params, ...config });
