<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="资金概览" name="forth" style="height: 100%" lazy>
					<fundingOverview />
				</el-tab-pane>
				<el-tab-pane label="总资产" name="first" style="height: 100%" lazy>
					<summaryFunds />
				</el-tab-pane>
				<el-tab-pane label="现金流" name="fifth" style="height: 100%" lazy>
					<totalCashFlow />
				</el-tab-pane>
				<el-tab-pane label="附表-平台资金" name="third" style="height: 100%" lazy>
					<PlatformCashFlow />
				</el-tab-pane>
				<el-tab-pane label="附表-出纳资金" name="sixth" style="height: 100%" lazy>
					<cashierFunds />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const PlatformCashFlow = defineAsyncComponent(() => import('./components/PlatformCashFlow.vue'));
const platformOfFunds = defineAsyncComponent(() => import('./components/platformOfFunds.vue'));
const summaryFunds = defineAsyncComponent(() => import('./components/summaryFunds.vue'));
const fundingOverview = defineAsyncComponent(() => import('./components/fundingOverview.vue'));
const totalCashFlow = defineAsyncComponent(() => import('./components/totalCashFlow.vue'));
const cashierFunds = defineAsyncComponent(() => import('./components/cashierFunds.vue'));
const activeName = ref('forth');
</script>
