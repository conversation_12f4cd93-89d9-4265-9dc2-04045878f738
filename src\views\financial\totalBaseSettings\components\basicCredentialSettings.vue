<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.id" placeholder="科目编码" class="publicCss" clearable maxlength="18" />
				<el-input v-model.trim="query.title" placeholder="科目名称" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.category" placeholder="科目类别" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.iGrade" placeholder="科目层级" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.balanceDirection" placeholder="余额方向" class="publicCss" clearable filterable>
					<el-option key="借" label="借" value="借" />
					<el-option key="贷" label="贷" value="贷" />
				</el-select>
				<el-input v-model.trim="query.supplementary" placeholder="辅助核算" class="publicCss" clearable maxlength="50" />
				<bankSelect v-model:value="query.account" placeholder="网银别名" class="publicCss" clearable filterable />
				<el-select v-model="query.hasQuantity" placeholder="数量核算" class="publicCss" clearable filterable>
					<el-option key="是" label="是" :value="true" />
					<el-option key="否" label="否" :value="false" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				keyField="id"
				:pageSize="300"
				id="basicCredentialSettings202412131015"
				:pageSizes="[50, 100, 200, 300]"
				:tableCols="tableCols"
				:query="query"
				:isAsc="false"
				:isNeedPager="false"
				:treeConfig="{ transform: true, rowField: 'id', parentField: 'pId' }"
				:query-api="QueryAccountsChart"
			>
				<template #toolbar_buttons>
					<el-button @click="onAddMethod({})" type="primary">新增</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<!-- <el-button @click="onAllTreeEvent" type="primary">{{ verifyTheTree ? '展开当前页树表格' : '关闭当前页树表格' }}</el-button> -->
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="editVisible" :title="dynamicHeading ? '编辑' : '新增'" width="450" draggable overflow style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
		<div style="padding-top: 10px" v-loading="listLoading">
			<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
				<el-form-item label="科目编码" :label-width="'100px'" prop="id">
					<el-input-number
						v-model="singleform.id"
						placeholder="科目编码"
						class="btnGroup"
						clearable
						autocomplete="off"
						:min="0"
						:max="*********000000000"
						:precision="0"
						:controls="false"
						maxlength="50"
						:disabled="dynamicHeading"
					/>
				</el-form-item>
				<el-form-item label="科目名称" :label-width="'100px'" prop="title">
					<el-input v-model.trim="singleform.title" placeholder="科目名称" class="btnGroup" clearable maxlength="50" />
				</el-form-item>
				<el-form-item label="科目类别" :label-width="'100px'" prop="category">
					<el-input v-model.trim="singleform.category" placeholder="科目类别" class="btnGroup" clearable maxlength="50" />
				</el-form-item>
				<el-form-item label="余额方向" :label-width="'100px'" prop="balanceDirection">
					<el-select v-model="singleform.balanceDirection" placeholder="余额方向" class="btnGroup" clearable filterable>
						<el-option key="借" label="借" value="借" />
						<el-option key="贷" label="贷" value="贷" />
					</el-select>
				</el-form-item>
				<el-form-item label="辅助核算类型" :label-width="'100px'">
					<el-select v-model="singleform.supplementary" placeholder="辅助核算类型" class="btnGroup" clearable filterable multiple collapse-tags>
						<el-option v-for="item in accountingList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="数量核算" :label-width="'100px'" prop="hasQuantity">
					<el-select v-model="singleform.hasQuantity" placeholder="数量核算" class="btnGroup" clearable filterable>
						<el-option key="是" label="是" :value="true" />
						<el-option key="否" label="否" :value="false" />
					</el-select>
				</el-form-item>
				<el-form-item label="关联网银别名" :label-width="'100px'" prop="relationAccount">
					<bankSelect v-model:value="singleform.relationAccount" placeholder="关联网银别名" class="btnGroup" clearable filterable v-if="editVisible" />
				</el-form-item>
				<el-form-item label="备注" :label-width="'100px'" style="white-space: pre-wrap; word-break: break-all">
					<el-input
						v-model="singleform.remark"
						placeholder="请输入"
						type="textarea"
						autocomplete="off"
						clearable
						style="width: 700px"
						maxlength="100"
						show-word-limit
						:autosize="{ minRows: 5, maxRows: 5 }"
						resize="none"
					/>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="editVisible = false">取消</el-button>
				<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import { QueryAccountsChart, ExportAccountsChart, InsertAccountsChart, UpdateAccountsChart, DeleteAccountsChart } from '/@/api/cwManager/accountsChart';
import { ElMessageBox, ElMessage, FormRules, FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const bankSelect = defineAsyncComponent(() => import('/@/components/yhCom/bankSelect.vue'));
const pageLoading = ref(false);
const editVisible = ref(false);
const dynamicHeading = ref(false);
const listLoading = ref(false);
const verifyTheTree = ref(true);
const accountingList = ref<Public.options[]>([
	{ label: '店铺名称', value: '店铺名称' },
	{ label: '店铺名称2', value: '店铺名称2' },
	{ label: '区域地址', value: '区域地址' },
	{ label: '组织结构', value: '组织结构' },
	{ label: '供应商（发票）', value: '供应商（发票）' },
	{ label: '快递站点', value: '快递站点' },
	{ label: '我的供应商', value: '我的供应商' },
]);
const ruleFormRef = ref<FormInstance>();
const table = ref();
const singleform = ref<{
	id: string | undefined;
	pId: string | undefined;
	title: string;
	category: string;
	balanceDirection: string;
	supplementary: string | Array<string>;
	hasQuantity: string | boolean;
	remark: string;
	fullTitle: string;
	relationAccount: string;
}>({
	id: undefined, //子级科目编码
	pId: undefined, //父级科目编码
	title: '', //科目名称
	category: '', //科目类别
	balanceDirection: '', //余额方向
	supplementary: [], //辅助核算
	hasQuantity: '', //数量核算
	remark: '', //备注
	fullTitle: '', //全称
	relationAccount: '', //网银别名
});

const singlerules = {
	id: [{ required: true, message: '请输入科目编码', trigger: 'blur' }],
	title: [{ required: true, message: '请输入科目名称', trigger: 'blur' }],
	category: [{ required: true, message: '请输入科目类别', trigger: 'blur' }],
	balanceDirection: [{ required: true, message: '请输入余额方向', trigger: 'blur' }],
	supplementary: [{ required: true, message: '请输入辅助核算', trigger: 'blur' }],
	hasQuantity: [{ required: true, message: '请输入数量核算', trigger: 'blur' }],
};
const query = ref({
	id: undefined, //科目编码
	title: '', //科目名称
	category: '', //科目类别
	balanceDirection: '', //余额方向
	supplementary: '', //辅助核算
	account: '', //关联网银别名
	hasQuantity: '', //数量核算
	iGrade: undefined, //科目层级
});

function validateNumericCode(code: any, fieldName: string): boolean {
	if (code && !/^\d+$/.test(code.toString())) {
		ElMessage.error(`${fieldName}只能输入数字`);
		return false;
	}
	return true;
}

// 全部展开树结构
const onAllTreeEvent = () => {
	if (verifyTheTree.value) {
		table.value.expandAllTreeEvent(true);
	} else {
		table.value.expandAllTreeEvent(false);
	}
	verifyTheTree.value = !verifyTheTree.value;
};

const closeExpansion = () => {
	if (query.value.id) {
		table.value.expandAllTreeEvent(true);
		verifyTheTree.value = false;
	} else {
		table.value.expandAllTreeEvent(false);
		verifyTheTree.value = true;
	}
};

const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			let singleformValue = singleform.value.supplementary;
			let supplementary = Array.isArray(singleformValue) ? singleformValue.join(',') : singleformValue;
			listLoading.value = true;
			if (dynamicHeading.value) {
				const { success } = await UpdateAccountsChart({ ...singleform.value, supplementary });
				if (success) {
					ElMessage.success('编辑成功');
					editVisible.value = false;
					getList();
				}
			} else {
				const { success } = await InsertAccountsChart({ ...singleform.value, supplementary });
				if (success) {
					ElMessage.success('新增成功');
					editVisible.value = false;
					getList();
				}
			}
			listLoading.value = false;
		} else {
			ElMessage.error('表单验证失败!');
		}
	});
}, 1000); // 防抖时间1秒

const exportProps = async () => {
	await ExportAccountsChart({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};

const onDelete = (row: any) => {
	ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const params = { id: row.id };
			const { success } = await DeleteAccountsChart(params);
			if (success) {
				ElMessage.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage({ type: 'info', message: '已取消' });
		});
};

const handleClose = () => {
	if (ruleFormRef.value) {
		ruleFormRef.value.resetFields();
		ruleFormRef.value.clearValidate();
	}
};

const onEdit = (row: any) => {
	singleform.value = JSON.parse(JSON.stringify(row));
	singleform.value.supplementary = row.supplementary ? row.supplementary.split(',') : [];
	dynamicHeading.value = true;
	editVisible.value = true;
};

const onAddMethod = (row: any) => {
	if (row.children && row.children.length > 0) {
		singleform.value.pId = row.id;
	}
	singleform.value = {
		id: undefined, //科目编码
		pId: row && row.id ? row.id : undefined, //父级科目编码
		title: '', //科目名称
		category: '', //科目类别
		balanceDirection: '', //余额方向
		supplementary: [], //辅助核算
		hasQuantity: '', //数量核算
		remark: '', //备注
		fullTitle: '', //全称
		relationAccount: '', //网银别名
	};
	dynamicHeading.value = false;
	editVisible.value = true;
};

const getList = async () => {
	if (!validateNumericCode(query.value.id, '科目编码')) {
		return;
	}
	table.value.query.currentPage = 1;
	await table.value.getList();
	setTimeout(() => {
		if (query.value.id) {
			table.value.expandAllTreeEvent(true);
		} else {
			table.value.clearTreeExpand();
		}
	}, 100);
};

const isDisabled = (row: any, val: number): boolean => {
	if (val === 1) {
		return row.children && row.children.length > 0;
	}
	return false;
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'id', title: '科目编码', width: '150', treeNode: true },
	{ sortable: true, field: 'title', title: '科目名称', width: '180' },
	{ sortable: true, field: 'category', title: '科目类别', width: '100' },
	{ sortable: true, field: 'iGrade', title: '科目层级', width: '90' },
	{ sortable: true, field: 'balanceDirection', title: '余额方向', width: '90' },
	{ sortable: true, field: 'supplementary', title: '辅助核算', width: '150' },
	{ sortable: true, field: 'hasQuantity', title: '数量核算', width: '90', formatter: (row: any) => (row.hasQuantity ? '是' : '否') },
	{ sortable: true, field: 'relationAccountName', title: '网银别名', width: '130' },
	{ sortable: true, field: 'remark', title: '备注', width: '75' },
	{ sortable: true, field: 'createdUserName', title: '创建人', width: '75' },
	{ sortable: true, field: 'createdTime', title: '创建时间', width: '135' },
	{ sortable: true, field: 'modifiedUserName', title: '修改人', width: '75' },
	{ sortable: true, field: 'modifiedTime', title: '修改时间', width: '135' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: 'auto',
		field:'**************',
		btnList: [
			{ title: '新增', handle: onAddMethod },
			{ title: '编辑', handle: onEdit },
			{ title: '删除', handle: onDelete, isDisabled: (row: any) => isDisabled(row, 1) },
		],
	},
]);

const getAllDept = async () => {};
onMounted(() => {
	getAllDept();
});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.custom-cascader {
	margin: 0 3px 5px 0;
}
</style>
