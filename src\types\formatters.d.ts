declare namespace Formatters {
	interface formatters<T> {
		formatTime: VxeColumnPropTypes.Formatter<T>;
		formatDate: VxeColumnPropTypes.Formatter<T>;
		formatBoolean: VxeColumnPropTypes.Formatter<T>;
		formatPlatform: VxeColumnPropTypes.Formatter<T>;
		formatLinkProcode: VxeColumnPropTypes.Formatter<T>;
		fmtAmt0: VxeColumnPropTypes.Formatter<T>;
		fmtAmt2: VxeColumnPropTypes.Formatter<T>;
		fmtAmt4: VxeColumnPropTypes.Formatter<T>;
		fmtPercent: VxeColumnPropTypes.Formatter<T>;
		fmtNum: VxeColumnPropTypes.Formatter<T>;
	}
}
