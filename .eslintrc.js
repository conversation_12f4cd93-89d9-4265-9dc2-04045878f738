/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,// 作用是指定这是根目录，不再往上查找
  'extends': [// 继承的规则
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting'
  ],
  parserOptions: {
    ecmaVersion: 'latest'// 指定要使用的ECMAScript版本
  },
  rules: {// 规则
    'no-console': 'off',
    'no-debugger': 'off',
    'no-unused-vars': 'off',
    'no-undef': 'off',
    'no-constant-condition': 'off',
    'no-empty': 'off',
    'no-irregular-whitespace': 'off',
    'no-prototype-builtins': 'off',
    'no-unreachable': 'off',
    'no-unsafe-finally': 'off',
    'no-unsafe-negation': 'off',
    'no-useless-escape': 'off',
    'no-async-promise-executor  ': 'off',
    'no-constant-condition': 'off',
    'no-empty': 'off',
    'no-irregular-whitespace': 'off',
    'no-prototype-builtins': 'off',
    'no-unreachable': 'off',
    'no-unsafe-finally': 'off',
  },
  ignorePatterns: ['node_modules', 'dist', 'public', 'src/assets', 'src/components', 'src/router', 'src/store', 'src/views', 'src/App.vue', 'src/main.ts', 'src/shims-vue.d.ts', 'src/vue.d.ts', 'src/typings.d.ts', 'src/global.d.ts'],// 忽略的文件
  overrides: [// 覆盖规则
    {
      files: ['*.ts', '*.tsx'],
      rules: {
        'no-unused-vars': 'off',
        'no-undef': 'off',
        'no-constant-condition': 'off',
        'no-empty': 'off',
        'no-irregular-whitespace': 'off',
        'no-prototype-builtins': 'off',
        'no-unreachable': 'off',
        'no-unsafe-finally': 'off',
        'no-unsafe-negation': 'off',
        'no-useless-escape': 'off',
        'no-async-promise-executor  ': 'off',
        'no-constant-condition': 'off',
        'no-empty': 'off',
        'no-irregular-whitespace': 'off',
        'no-prototype-builtins': 'off',
        'no-unreachable': 'off',
        'no-unsafe-finally': 'off',
      }
    }
  ]
}
