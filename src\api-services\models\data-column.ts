/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DataSetDateTime } from './data-set-date-time';
import { DataTable } from './data-table';
import { IContainer } from './icontainer';
import { ISite } from './isite';
import { MappingType } from './mapping-type';
import { Type } from './type';
 /**
 * 
 *
 * @export
 * @interface DataColumn
 */
export interface DataColumn {

    /**
     * @type {ISite}
     * @memberof DataColumn
     */
    site?: ISite;

    /**
     * @type {IContainer}
     * @memberof DataColumn
     */
    container?: IContainer;

    /**
     * @type {boolean}
     * @memberof DataColumn
     */
    designMode?: boolean;

    /**
     * @type {boolean}
     * @memberof DataColumn
     */
    allowDBNull?: boolean;

    /**
     * @type {boolean}
     * @memberof DataColumn
     */
    autoIncrement?: boolean;

    /**
     * @type {number}
     * @memberof DataColumn
     */
    autoIncrementSeed?: number;

    /**
     * @type {number}
     * @memberof DataColumn
     */
    autoIncrementStep?: number;

    /**
     * @type {string}
     * @memberof DataColumn
     */
    caption?: string | null;

    /**
     * @type {string}
     * @memberof DataColumn
     */
    columnName?: string | null;

    /**
     * @type {string}
     * @memberof DataColumn
     */
    prefix?: string | null;

    /**
     * @type {Type}
     * @memberof DataColumn
     */
    dataType?: Type;

    /**
     * @type {DataSetDateTime}
     * @memberof DataColumn
     */
    dateTimeMode?: DataSetDateTime;

    /**
     * @type {any}
     * @memberof DataColumn
     */
    defaultValue?: any | null;

    /**
     * @type {string}
     * @memberof DataColumn
     */
    expression?: string | null;

    /**
     * @type {{ [key: string]: any; }}
     * @memberof DataColumn
     */
    extendedProperties?: { [key: string]: any; } | null;

    /**
     * @type {number}
     * @memberof DataColumn
     */
    maxLength?: number;

    /**
     * @type {string}
     * @memberof DataColumn
     */
    namespace?: string | null;

    /**
     * @type {number}
     * @memberof DataColumn
     */
    ordinal?: number;

    /**
     * @type {boolean}
     * @memberof DataColumn
     */
    readOnly?: boolean;

    /**
     * @type {DataTable}
     * @memberof DataColumn
     */
    table?: DataTable;

    /**
     * @type {boolean}
     * @memberof DataColumn
     */
    unique?: boolean;

    /**
     * @type {MappingType}
     * @memberof DataColumn
     */
    columnMapping?: MappingType;
}
