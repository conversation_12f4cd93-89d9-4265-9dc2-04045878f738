<template>
	<div style="width: 100%; height: 100%" v-loading="loading">
		<Container>
			<template #header>
				<div class="topCss">
					<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss"
						style="width: 200px" />
					<el-select v-model="query.flowType" placeholder="支出方向" class="publicCss" clearable
						style="width: 100px">
						<el-option label="收入" value="收入" />
						<el-option label="支出" value="支出" />
					</el-select>
					<el-select v-model="query.feeType" placeholder="支出类型" class="publicCss" clearable
						style="width: 100px">
						<el-option label="冲正" value="冲正" />
						<el-option label="过账" value="过账" />
					</el-select>
					<div class="pb5">
						<el-button @click="getList" type="primary">查询</el-button>
						<el-button @click="exportProps" type="primary">导出</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="**************" :tableCols="tableCols" isNeedDisposeProps
					@disposeProps="disposeProps" :query="query" showsummary :query-api="QueryRectify" />
			</template>
		</Container>

		<el-dialog v-model="detailDialog" title="查看" width="610" draggable overflow>
			<div>
				<el-table :data="detailData" style="width: 100%" height="350px">
					<el-table-column prop="occurenceDate" label="交易日期" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="toAccountName" label="对方姓名/账户名" width="150" :show-overflow-tooltip="true" />
					<el-table-column prop="toAccount" label="对方卡号" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="flowType" label="支出方向" width="100" :show-overflow-tooltip="true" />
					<el-table-column prop="expectedTodayRecharge" label="金额">
						<template #default="{ row, $index }">
							{{ row.inAmount ? row.inAmount : row.outAmount ? row.outAmount : 0 }}
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div style="display: flex; justify-content: end; margin-top: 10px">
				<el-button @click="detailDialog = false" type="primary">关闭</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { QueryBankFlow } from '/@/api/cwManager/bankFlow';
import { QueryRectify, ExportRectify } from '/@/api/cwManager/rectify';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const detailDialog = ref(false);
const loading = ref(false);
const detailData = ref([]);
const formatAmount = (amount: any) => {
	if (amount === null || amount === undefined) {
		return '';
	} else if (amount === 0) {
		return '0.00';
	} else {
		return parseFloat(amount).toLocaleString('en-US', {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
	}
};

const handleMatching = async (row: any) => {
	loading.value = true;
	const { data, success } = await QueryBankFlow({ id: row.relationBankFlowRecordId });
	loading.value = false;
	if (!success) return;
	detailData.value = data.list;
	detailData.value.forEach((item: any) => {
		item.occurenceDate = dayjs(item.occurenceDate).format('YYYY-MM-DD');
	});
	detailDialog.value = true;
};

const isDisabled1 = (row: any, type: number) => {
	return row.relationBankFlowRecordId ? false : true;
};

const tableCols = ref<VxeTable.Columns[]>([
	//列头
	{ field: 'busName', title: '别名' },
	{ sortable: true, field: 'occurenceDate', title: '交易日期', formatter: 'formatDate' },
	{ sortable: true, field: 'toAccountName', title: '对方姓名/账户名' },
	{ sortable: true, field: 'toAccount', title: '对方卡号' },
	{ sortable: true, field: 'flowType', title: '支出方向' },
	{
		sortable: true,
		field: 'amount',
		title: '金额',
		align: 'right',
		formatter: (row: any) => {
			const amount = row.flowType == '收入' ? row.inAmount : row.flowType == '支出' ? row.outAmount : row.amount;
			return formatAmount(amount);
		},
	},
	{ field: 'relationFeeType', title: '支出类别' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		field:'**************',
		fixed: 'right',
		width: '60',
		btnList: [{ title: '匹配数据', handle: handleMatching, isDisabled: (row: any) => isDisabled1(row, 2) }],
	},
	{ sortable: true, field: 'relationRemark', title: '备注' },
]);
const options = ref<Public.options[]>([]);
const table = ref();
const query = ref({
	startTime: dayjs().startOf('month').format('YYYY-MM-DD'),
	endTime: dayjs().endOf('month').format('YYYY-MM-DD'),
	maxNum: null,
	minNum: null,
	ddnum: '',
	area: '',
	status: '',
	flowType: '',
	feeType: '',
});
const exportProps = async () => {
	await ExportRectify({ ...query.value });
	window.$message.success('下载任务已创建，稍后点击头像-下载管理，进行下载！');
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};
const disposeProps = async (data: any, callback: Function) => {
	if (data && data.data && data.data.summary) {
		data.data.summary.forEach((item:any)=>{
			item.amount = item.amount !== null ? item.amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '';
		})
	}
	callback(data);
};
</script>

<style scoped lang="scss"></style>
