/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface GetDingTalkCurrentEmployeesRosterListInput
 */
export interface GetDingTalkCurrentEmployeesRosterListInput {

    /**
     * 员工的userId列表，多个userid之间使用逗号分隔，一次最多支持传100个值。
     *
     * @type {string}
     * @memberof GetDingTalkCurrentEmployeesRosterListInput
     */
    useridList?: string | null;

    /**
     * 需要获取的花名册字段field_code值列表，多个字段之间使用逗号分隔，一次最多支持传100个值。
     *
     * @type {string}
     * @memberof GetDingTalkCurrentEmployeesRosterListInput
     */
    fieldFilterList?: string | null;

    /**
     * 应用的AgentId
     *
     * @type {string}
     * @memberof GetDingTalkCurrentEmployeesRosterListInput
     */
    agentId?: string | null;
}
