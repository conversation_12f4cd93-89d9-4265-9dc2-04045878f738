import dayjs from 'dayjs';
import { text } from 'stream/consumers';

const dateRangeShortcuts = [
	{
		text: '近一周',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			return [start, end];
		},
	},
	{
		text: '近一个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			return [start, end];
		},
	},
	{
		text: '近三个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
			return [start, end];
		},
	},
];

const dateTimeShortcuts = [
	{
		text: '最近24小时',
		value: () => {
			const end = new Date();
			const start = new Date();
			end.setHours(start.getHours() - 24);
			return [start, end];
		},
	},
	{
		text: '最近48小时',
		value: () => {
			const end = new Date();
			const start = new Date();
			end.setHours(start.getHours() - 48);
			return [start, end];
		},
	},
	{
		text: '最近7天',
		value: () => {
			const end = new Date();
			const start = new Date();
			end.setDate(start.getDate() - 7);
			return [start, end];
		},
	},
	{
		text: '最近30天',
		value: () => {
			const end = new Date();
			const start = new Date();
			end.setDate(start.getDate() - 30);
			return [start, end];
		},
	},
];

const dateShortcuts = [
	{
		text: '昨天',
		value: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	},
	{
		text: '7天前',
		value: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
	},
	{
		text: '30天前',
		value: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	},
	{
		text: '90天前',
		value: dayjs().subtract(90, 'day').format('YYYY-MM-DD'),
	},
];
export { dateRangeShortcuts, dateTimeShortcuts, dateShortcuts };
