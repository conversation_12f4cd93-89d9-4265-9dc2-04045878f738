<template>
	<div class="numRange">
		<el-input-number v-model="minNum" :min="props.min" :max="props.max" :controls="false" :precision="props.precision" :placeholder="props.minPlaceHolder" />
		<div>-</div>
		<el-input-number v-model="maxNum" :min="props.min" :max="props.max" :controls="false" :precision="props.precision" :placeholder="props.maxPlaceHolder" />
	</div>
</template>

<script setup lang="ts" name="">
const props = defineProps({
	max: {
		type: Number,
		default: 10000000000,
	},
	min: {
		type: Number,
		default: -10000000,
	},
	precision: {
		type: Number,
		default: 0,
	},
	minPlaceHolder: {
		type: String,
		default: '最小值',
	},
	maxPlaceHolder: {
		type: String,
		default: '最大值',
	},
});
const minNum = defineModel('minNum');
const maxNum = defineModel('maxNum');
</script>

<style scoped lang="scss">
.numRange {
	display: flex;
	align-items: center;
}
</style>
