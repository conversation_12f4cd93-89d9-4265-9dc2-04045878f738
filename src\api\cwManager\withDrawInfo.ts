import request from '/@/utils/yhrequest';
//提现信息
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawInfo/`;
//公司账户余额
const apiPrefixa = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawCompanyAccountBalance/`;
//网银余额
const apiPrefixb = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawOnlineBankBalance/`;
//店铺信息
const apiPrefixc = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawShopInfo/`;
//流水明细
const apiPrefixd = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawFlow/`;
//流水汇总
const apiPrefixe = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawBankCard/`;

const apiPrefixf = `${import.meta.env.VITE_APP_BASE_API_CwManage}/CwComputeTime/`;

const apiPrefixg = `${import.meta.env.VITE_APP_BASE_API_CwManage}/WithDrawInfoTemp/`;

//提现信息-查询
export const QueryWithDrawInfo = (params: any, config = {}) => request.post(apiPrefix + 'QueryWithDrawInfo', params, config);

//提现信息-更新
export const UpdateWithDrawInfo = (params: any, config = {}) => request.post(apiPrefix + 'UpdateWithDrawInfo', params, config);

//提现信息-导出
export const ExportWithDrawInfo = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportWithDrawInfo', params, config);

//公司账户余额-查询
export const QueryWithDrawCompanyAccountBalance = (params: any, config = {}) => request.post(apiPrefixa + 'QueryWithDrawCompanyAccountBalance', params, config);

//公司账户余额-更新
export const UpdateWithDrawCompanyAccountBalance = (params: any, config = {}) => request.post(apiPrefixa + 'UpdateWithDrawCompanyAccountBalance', params, config);

//公司账户余额-导出
export const ExportWithDrawCompanyAccountBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixa + 'ExportWithDrawCompanyAccountBalance', params, config);

//网银余额-查询
export const QueryWithDrawOnlineBankBalance = (params: any, config = {}) => request.post(apiPrefixb + 'QueryWithDrawOnlineBankBalance', params, config);

//网银余额-更新
export const UpdateWithDrawOnlineBankBalance = (params: any, config = {}) => request.post(apiPrefixb + 'UpdateWithDrawOnlineBankBalance', params, config);

//网银余额-计算
export const ComputeWithDrawOnlineBankBalance = (params: any, config = {}) => request.post(apiPrefixb + 'ComputeWithDrawOnlineBankBalance', params, config);

//网银余额-导出
export const ExportWithDrawOnlineBankBalance = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixb + 'ExportWithDrawOnlineBankBalance', params, config);

//店铺信息-查询
export const QueryWithDrawShopInfo = (params: any, config = {}) => request.post(apiPrefixc + 'QueryWithDrawShopInfo', params, config);

//店铺信息-更新
export const UpdateWithDrawShopInfo = (params: any, config = {}) => request.post(apiPrefixc + 'UpdateWithDrawShopInfo', params, config);

//店铺信息-同步
export const SyncWithDrawShopInfo = (params: any, config = {}) => request.post(apiPrefixc + 'SyncWithDrawShopInfo', params, config);

//店铺信息-导出
export const ExportWithDrawShopInfo = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixc + 'ExportWithDrawShopInfo', params, config);

//店铺信息-更改店铺状态
export const UpdateWithDrawShopInfoStatus = (params: any, config = {}) => request.post(apiPrefixc + 'UpdateWithDrawShopInfoStatus', params, config);

//店铺信息-获取店铺列表
export const GetWithDrawShopList = (params: any, config = {}) => request.post(apiPrefixc + 'GetWithDrawShopList', params, config);

//店铺信息-导入
export const ImportWithDrawShopBankCard = (params: any, config = {}) => request.post(apiPrefixc + 'ImportWithDrawShopBankCard', params, config);

//流水明细-明细-查询
export const QueryWithDrawFlow = (params: any, config = {}) => request.post(apiPrefixd + 'QueryWithDrawFlow', params, config);

//流水明细-明细-发起审批
export const CommitApplicationMoney = (params: any, config = {}) => request.post(apiPrefixd + 'CommitApplicationMoney', params, config);

//流水明细-明细-导出
export const ExportWithDrawInfoTurnover = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixd + 'ExportWithDrawInfo', params, config);

//流水明细-汇总-查询
export const QueryWithDrawBankCard = (params: any, config = {}) => request.post(apiPrefixe + 'QueryWithDrawBankCard', params, config);

// 流水明细-汇总-导出
// export const ExportWithDrawInfoTurnover = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixe + 'ExportWithDrawInfo', params, config);

//流水明细-汇总-登记
export const WithDrawFlowRegister = (params: any, config = {}) => request.post(apiPrefixe + 'WithDrawFlowRegister', params, config);

//流水明细-汇总-标记切换
export const UpdateWithDrawBankCardWarning = (params: any, config = {}) => request.post(apiPrefixe + 'UpdateWithDrawBankCardWarning', params, config);

//流水明细-汇总-一键复核
export const BatchVerifyWithDrawFlow = (params: any, config = {}) => request.post(apiPrefixe + 'BatchVerifyWithDrawFlow', params, config);

//流水明细-汇总-复核
export const VerifyWithDrawFlow = (params: any, config = {}) => request.post(apiPrefixd + 'VerifyWithDrawFlow', params, config);

//公司账户余额/提现网银日报表-计算时间
export const QueryCwComputeTime = (params: any, config = {}) => request.post(apiPrefixf + 'QueryCwComputeTime', params, config);

//公司账户余额-计算
export const ComputeWithDrawCompanyAccountBalance = (params: any, config = {}) => request.post(apiPrefixa + 'ComputeWithDrawCompanyAccountBalance', params, config);

//流水导入
export const ImportWithDrawInfo = (params: any, config = {}) => request.post(apiPrefix + 'ImportWithDrawInfo', params, config);

//提现登记-查询
export const QueryWithDrawInfoTemp = (params: any, config = {}) => request.post(apiPrefixg + 'QueryWithDrawInfoTemp', params, config);

//提现登记-编辑
export const UpdateWithDrawInfoTemp = (params: any, config = {}) => request.post(apiPrefixg + 'UpdateWithDrawInfoTemp', params, config);

//提现登记-提交
export const SyncWithDrawInfoTemp = (params: any, config = {}) => request.post(apiPrefixg + 'SyncWithDrawInfoTemp', params, config);

//批量提现 BatchGetWithDrawShopData
export const BatchGetWithDrawShopData = (params: any, config = {}) => request.post(apiPrefixg + 'BatchGetWithDrawShopData', params, config);

//批量提现 BatchUpdateWithDrawShopData
export const BatchUpdateWithDrawShopData = (params: any, config = {}) => request.post(apiPrefixg + 'BatchUpdateWithDrawShopData', params, config);
