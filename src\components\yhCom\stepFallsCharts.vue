<template>
    <div>
        <div v-if="analysisData && analysisData.series && analysisData.series != null && analysisData.series.length > 0"
            :id="'buschar' + randrom" :style="thisStyle" v-loading="loading"></div>
        <div v-else>没有可供展示的图表数据！</div>
    </div>
</template>

<script setup lang="ts" name="">
import { ref, defineProps, onBeforeMount, onMounted, defineExpose } from 'vue'
import { formatters } from '/@/utils/vxetableFormats';
import * as echarts from 'echarts';
const randrom = ref('')
const loading = ref(false)
const option = ref({})
const chatProps = ref({
    legend: [],
    xAxis: [],
    series: [],
    title: ''
})
const props = defineProps({
    analysisData: {
        type: Object,
        required: true
    },
    thisStyle: {
        type: Object,
        default: function () {
            return {
                width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
            }
        }
    },
    showX: {
        type: Boolean,
        default: false
    },
    showY: {
        type: Boolean,
        default: false
    }
})
const initCharts = (chatProps: any) => {
    option.value = {
        title: { text: chatProps.title },
        tooltip: {
            trigger: 'axis',
            formatter: function (params: any) {
                let tar;
                tar = params.filter((item: any) => item.seriesName !== 'Placeholder')
                let str = tar?.length > 0 ? formatters.formatDate(tar[0].name) + '</br>' : '--'
                return str += tar.map((item: any) => item.seriesName + ' : ' + formatters.fmtAmt0(item.value) + '</br>').join('\n')
            }
        },
        legend: {
            data: chatProps.legend
        },
        xAxis: {
            type: 'category',
            data: chatProps.xAxis,
            show: props.showX
        },
        yAxis: { type: 'value', show: props.showY },
        series: chatProps.series
    }
}

const createChart = () => {
    const chartDom = document.getElementById('buschar' + randrom.value);
    const myChart = echarts.init(chartDom);
    option.value && myChart.setOption(option.value);
    window.addEventListener('resize', () => {
        myChart.resize()
    });
}

const reSetChart = (val: any) => {
    const chartDom = document.getElementById('buschar' + randrom.value);
    const myChart = echarts.init(chartDom);
    myChart && myChart.dispose()
    initCharts(val)
    createChart()
}

onBeforeMount(() => {
    var e = 10;
    var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
        a = t.length,
        n = "";
    for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
    randrom.value = n;
    chatProps.value = JSON.parse(JSON.stringify(props.analysisData))
    console.log(chatProps.value, 'chatProps.value')
})
onMounted(() => {
    initCharts(chatProps.value)
    createChart()
})

defineExpose({
    reSetChart
})
</script>

<style scoped lang="scss"></style>
