import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/CashierSet/`;

//查询出纳人员设置
export const QueryCashierUserSet = (params: any, config = {}) => request.post(apiPrefix + 'queryCashierUserSet', params, config);

//新增或更新出纳人员设置
export const InsertOrUpdateCashierUserSet = (params: any, config = {}) => request.post(apiPrefix + 'InsertOrUpdateCashierUserSet', params, config);

//删除出纳人员设置
export const DeleteCashierUserSet = (params: any, config = {}) => request.post(apiPrefix + 'DeleteCashierUserSet', params, config);

//查询网银设置
export const QueryOnlineBankSet = (params: any, config = {}) => request.post(apiPrefix + 'QueryOnlineBankSet', params, config);

//新增或更新网银设置
export const InsertOrUpdateOnlineBankSet = (params: any, config = {}) => request.post(apiPrefix + 'InsertOrUpdateOnlineBankSet', params, config);

//删除银行设置
export const DeleteOnlineBankSet = (params: any, config = {}) => request.post(apiPrefix + 'DeleteOnlineBankSet', params, config);

//查询流水关联
export const QueryCashierFeeType = (params: any, config = {}) => request.post(apiPrefix + 'QueryCashierFeeType', params, config);

///新增或更新流水关联
export const InsertOrUpdateCashierFeeType = (params: any, config = {}) => request.post(apiPrefix + 'InsertOrUpdateCashierFeeType', params, config);

//删除流水关联
export const DeleteCashierFeeType = (params: any, config = {}) => request.post(apiPrefix + 'DeleteCashierFeeType', params, config);

//网银下拉 QueryOnlineBankSetSelect
export const QueryOnlineBankSetSelect = (params: any, config = {}) => request.post(apiPrefix + 'QueryOnlineBankSetSelect', params, config);

//获取所有账号下拉
export const GetAllCgAccount = () => request.get(apiPrefix + 'GetAllCgAccount');

//收入类型设置-新增或编辑 InsertOrUpdateRevenueAccount
export const InsertOrUpdateRevenueAccount = (params: any, config = {}) => request.post(apiPrefix + 'InsertOrUpdateRevenueAccount', params, config);

//收入类型设置-查询 QueryRevenueAccount
export const QueryRevenueAccount = (params: any, config = {}) => request.post(apiPrefix + 'QueryRevenueAccount', params, config);

//收入类型设置-导入 ImportRevenueAccount
export const ImportRevenueAccount = (params: any, config = {}) => request.post(apiPrefix + 'ImportRevenueAccount', params, config);

//收入类型设置-删除 DeleteRevenueAccount
export const DeleteRevenueAccount = (params: any, config = {}) => request.post(apiPrefix + 'DeleteRevenueAccount', params, config);

//导出网银别名
export const ExportOnlineBankSet = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportOnlineBankSet', params, config);

//导入网银别名
export const ImportOnlineBankSet = (params: any, config = {}) => request.post(apiPrefix + 'ImportOnlineBankSet', params, config);

//导出收款类型
export const ExportTotalReportInAmount  = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTotalReportInAmount ', params, config);

//查询费用类型汇总 QueryCashierFeeTypeSum
export const QueryCashierFeeTypeSum = (params: any, config = {}) => request.post(apiPrefix + 'QueryCashierFeeTypeSum', params, config);

//查询费用类型 QueryCashierFeeTypeCasCade
export const QueryCashierFeeTypeCasCade = (params: any, config = {}) => request.post(apiPrefix + 'QueryCashierFeeTypeCasCade', params, config);