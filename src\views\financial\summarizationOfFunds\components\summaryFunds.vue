<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.start" v-model:endDate="query.end" style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(false)">查询</el-button>
					<el-button type="primary" @click="openComputed">计算财务资金汇总</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="20250312140209"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="PageAllFund"
				:export-api="ExportAllFund"
				:asyncExport="{ title: '总资产', isAsync: false }"
				@footerCellClick="onSummaryTotalMap"
			>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="dialogVisible" title="Tips" width="400">
		<div style="display: flex; justify-content: center">
			<el-date-picker v-model="date" type="date" placeholder="请选择日期" value-format="YYYY-MM-DD" :clearable="false" />
		</div>
		<div style="display: flex; justify-content: center; margin-top: 10px">
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button type="primary" @click="cumputedSubmit"> 确定 </el-button>
		</div>
	</el-dialog>

	<el-dialog v-model="trendChart.totalMapVisible" width="60%" draggable overflow @close="lengendObject = {}">
		<div>
			<dataRange
				v-model:startDate="trendChart.start"
				v-model:endDate="trendChart.end"
				:clearable="false"
				startPlaceholder="开始时间"
				endPlaceholder="结束时间"
				style="width: 260px"
				@change="onTrendChartMethod()"
			/>
			<lineChart
				v-if="trendChart.totalMapVisible"
				:chartData="trendChart.analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '600px',
					'box-sizing': 'border-box',
					'line-height': '600px',
				}" @onLegendMethod="onLegendMethod" :lengendObject="lengendObject"
			/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent,nextTick } from 'vue';
import { platformlist } from '/@/utils/tools';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import { QueryAllFundCharAnalysis } from '/@/api/financewh/fundsAnalysis';
import { PageAllFund, ExportAllFund, Compute } from '/@/api/financewh/funds';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const query = ref({
	start: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
	platform: null,
});
const lengendObject = ref({});
const table = ref();
const dialogVisible = ref(false);
const date = ref('');
const sumChart = ref(); //总资产趋势图组件
const trendChart = ref<{
	start: string;
	end: string;
	totalMapVisible: boolean;
	analysisData: any;
	columns: string;
}>({
	start: '',
	end: '',
	totalMapVisible: false,
	analysisData: [],
	columns: '',
});

const onLegendMethod = (val: any) => {
  lengendObject.value = val
};

const onSummaryTotalMap = async (row: any, column: any) => {
	trendChart.value.columns = column;
	trendChart.value.end = query.value.end;
	trendChart.value.start = dayjs(trendChart.value.end).subtract(30, 'day').format('YYYY-MM-DD');
	onTrendChartMethod();
};

const onTrendChartMethod = async () => {
	const res = await QueryAllFundCharAnalysis({
		start: trendChart.value.start,
		end: trendChart.value.end,
		column: trendChart.value.columns,
	});
	trendChart.value.analysisData = res;
	trendChart.value.totalMapVisible = true;
	nextTick(() => {
		if (sumChart.value) {
			sumChart.value.reSetChart(res);
		}
	});
};

const openComputed = () => {
	date.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
	dialogVisible.value = true;
};
const cumputedSubmit = async () => {
	if (!date.value) {
		return window.$message.error('请选择日期');
	}
	const { success } = await Compute({ date: date.value });
	if (!success) return;
	dialogVisible.value = false;
	window.$message.success('计算成功');
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'receiptDate', title: '日期', width: '90', formatter: 'formatDate' },
	{
		summaryEvent: true,
		sortable: true,
		field: 'totalAssets',
		title: '资产合计',
		width: '110',
		align: 'right',
		tipmesg: '总资金+仓储资产+固定资产净值+待摊费用余额',
		formatter: 'fmtAmt0',
	},
	{
		title: '平台汇总',
		align: 'center',
		field: '20250608094042',
		children: [
			{ summaryEvent: true, sortable: true, field: 'z_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'z_Tg_Amount', title: '推广余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'z_Bzj_Amount', title: '保证金余额', width: '100', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '平台冻结',
		field: '20250608094054',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'platFreezeFund', title: '冻结资金', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'platFreezeFundNew', title: '新增冻结资金', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'platUnFreezeFund', title: '解除冻结资金', width: '115', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '分销',
		field: '20250608094105',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'fx_Hk_Amount', title: '货款余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'fx_FreezeFund', title: '冻结资金', width: '115', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		summaryEvent: true,
		sortable: true,
		title: '总经办',
		width: '95',
		field: 'managerFund',
		align: 'right',
		formatter: 'fmtAmt0',
		tipmesg: '总资金-年度预存汇总-月度预存汇总-拼多多冻结资金-分销商冻结资金+提现自用-其他数据（小对话框，取总金额）',
	},
	{ summaryEvent: true, sortable: true, field: 'withdrawBalance', title: '提现账户余额', width: '115', align: 'right', formatter: 'fmtAmt0' },
	{ summaryEvent: true, sortable: true, field: 'perWithdraw', title: '提现自用', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{ summaryEvent: true, sortable: true, field: 'chunaBalance', title: '出纳余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{ summaryEvent: true, sortable: true, field: 'duiGongBalance', title: '对公余额', width: '90', align: 'right', formatter: 'fmtAmt0' },
	{
		summaryEvent: true,
		sortable: true,
		field: 'totalAmount',
		title: '总资金',
		width: '95',
		align: 'right',
		formatter: 'fmtAmt0',
		tipmesg: '货款余额+推广余额+保证金余额+提现账户余额+出纳余额+对公余额',
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'withdrawFund',
		title: '提现资金',
		width: '110',
		align: 'right',
		formatter: 'fmtAmt0',
		tipmesg: '提现余额+出纳余额+分销商余额+（昨日淘系余额-今天淘系提现金额-150万）-分销商冻结资金',
	},
	{ summaryEvent: true, sortable: true, field: 'useFullFund', title: '可用资金', width: '110', align: 'right', formatter: 'fmtAmt0', tipmesg: '提现资金-年度预存汇总-月度预存汇总' },
	{ summaryEvent: true, sortable: true, field: 'promiseAmount', title: '保证金及押金', width: '115', align: 'right', formatter: 'fmtAmt0' },
	{
		title: '预存',
		align: 'center',
		field: '20250608094133',
		children: [
			{ summaryEvent: true, sortable: true, field: 'yearFeature', title: '年度预存', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'yearFeatureTotal', title: '年度预存汇总', width: '115', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'monthFeature', title: '月度预存', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'monthFeatureTotal', title: '月度预存汇总', width: '115', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '固定资产',
		field: '20250608094147',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'depreciation', title: '每日折旧', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'regularPeoperty', title: '净值', width: '70', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '待摊费用',
		field: '20250608094159',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'daiTanDayAmount', title: '每日摊销', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'daiTanDayBalance', title: '余额', width: '70', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{
		title: '仓储资产',
		field: '20250608094211',
		align: 'center',
		children: [
			{ summaryEvent: true, sortable: true, field: 'inventory', title: '国内库存', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'buyTransit', title: '采购在途', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'saleTransit', title: '销售在途', width: '90', align: 'right', formatter: 'fmtAmt0' },
			{ summaryEvent: true, sortable: true, field: 'deliveryBalance', title: '快递面单余额', width: '115', align: 'right', formatter: 'fmtAmt0' },
		],
	},
	{ sortable: true, field: 'updateTime', title: '更新时间', width: '140', align: 'right', formatter: 'formatTime' },
]);
</script>

<style scoped lang="scss"></style>
