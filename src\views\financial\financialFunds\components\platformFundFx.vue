<template>
	<Container v-loading="pageLoading">
		<template #header>
			<div class="topCss">
				<dataRange
					v-model:startDate="query.startDate"
					v-model:endDate="query.endDate"
					class="publicCss"
					:clearable="false"
					startPlaceholder="开始时间"
					endPlaceholder="结束时间"
					style="width: 230px"
				/>
				<el-select v-model="query.status" placeholder="请选择状态" class="publicCss" style="width: 170px" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in statusList" :key="item" :label="item" :value="item"></el-option>
				</el-select>
				<el-select v-model="query.checkStatus" placeholder="验算" clearable class="publicCss">
					<el-option label="验算正确" value="验算正确" />
					<el-option label="验算错误" value="验算错误" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
					<el-button @click="pullProps" type="primary">拉取</el-button>
					<el-button @click="Payouts" type="primary">提现</el-button>
					<el-button @click="check" type="primary">验算</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<div class="container-relative">
				<div class="radio-group-absolute">
					<el-button @click="exportProps" type="primary" :disabled="rootDisabled">导出</el-button>
					<el-button @click="onLockUnlock('lock')" type="primary" class="ml5" :disabled="rootDisabled">锁定</el-button>
					<el-button @click="onLockUnlock('unlock')" type="primary" class="ml5" :disabled="rootDisabled">解锁</el-button>
				</div>
				<vxetable
					showsummary
					ref="table"
					id="levelSubjectsOne202412221701"
					:tableCols="tableCols"
					:pageSize="50"
					:query="query"
					:isAsc="false"
					isNeedCheckBox
					@select="checkboxChange"
					:queryApi="GetFXDailyBalance"
					@footerCellClick="onSummaryTotalMap"
					isNeedDisposeProps
					@disposeProps="disposeProps"
				>
					<template #endBalance="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="endBalance" />
					</template>
					<template #openingBalance="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="openingBalance" />
					</template>
					<template #withDrawAmount="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="withDrawAmount" />
					</template>
					<template #frozenAmount="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="frozenAmount" />
					</template>
					<template #totalIncome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalIncome" :min="0" :max="99999999" />
					</template>
					<template #totalOutcome="{ row }">
						<editableNumber :model-value="row" @update:model-value="(val: any) => Object.assign(row, val)" field="totalOutcome" :min="-99999999" :max="0" />
					</template>
				</vxetable>
			</div>
		</template>
	</Container>

	<el-dialog v-model="totalMapVisible" width="60%" draggable overflow>
		<div>
			<dataRange
				v-model:startDate="trendChart.startDate"
				v-model:endDate="trendChart.endDate"
				:clearable="false"
				startPlaceholder="开始时间"
				endPlaceholder="结束时间"
				style="width: 260px"
				@change="onTrendChartMethod(2)"
			/>
			<lineChart
				v-if="totalMapVisible"
				:chartData="analysisData"
				ref="sumChart"
				:thisStyle="{
					width: '100%',
					height: '600px',
					'box-sizing': 'border-box',
					'line-height': '600px',
				}"
			/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import {
	GetFXDailyBalance,
	ExportFXDailyBalance,
	LockDailyBalanceData,
	UnLockDailyBalanceData,
	GetFXGoodsAccountTotalMap,
	EditFXGoodsBalance,
	InitShopData,
	SyncShopDataWithDrawAmount,
	CheckShopDataDailyBalance,
} from '/@/api/cwManager/cwFundsDailyBalance';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
import dayjs from 'dayjs';
import { ElMessageBox, ElMessage } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const editableNumber = defineAsyncComponent(() => import('/@/components/yhCom/editableNumber.vue'));
const pageLoading = ref(false);
const timeRange = ref('');
const statusList = ref(['待导入', '已导入', '已锁定']);
const checkboxList = ref<any>([]);
const table = ref();
const rootDisabled = ref(false);
const totalMapName = ref('');
const totalMapVisible = ref(false);
const analysisData = ref<any>({});
const sumChart = ref();
const trendChart = ref({
	startDate: '',
	endDate: '',
	shopIdList: [] as Array<string>,
	status: [] as Array<string>,
	platformShopId: '',
});
const query = ref({
	startDate: '',
	endDate: '',
	status: [],
	checkStatus: '',
});
const tableData = ref<any[]>([]);
const backupTableData = ref<any[]>([]);

const findRowIndex = (row: any) => tableData.value.findIndex((item: any) => item.mesgID === row.mesgID);

// 获取当前选中的表格
const getCurrentTable = () => {
	return table;
};
const pullProps = async () => {
	ElMessageBox.confirm(`${query.value.startDate}-${query.value.endDate}的已导入数据会丢失，是否确认拉取？`, '确认消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await InitShopData({ startDate: query.value.startDate, endDate: query.value.endDate, platform: 11 });
			if (success) {
				ElMessage.success('拉取成功');
				getList();
			} else {
				ElMessage.error('拉取失败，请稍后再试！');
			}
		})
		.catch(() => {});
};

const Payouts = async () => {
	const { success, data } = await SyncShopDataWithDrawAmount({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('提现成功');
	} else {
		ElMessage.error('提现失败，请稍后再试！');
	}
};

const check = async () => {
	const { success, data } = await CheckShopDataDailyBalance({ startDate: query.value.startDate, endDate: query.value.endDate });
	if (success) {
		ElMessage.success('验算成功');
	} else {
		ElMessage.error('验算失败，请稍后再试！');
	}
};

// 更新表格数据并同步 UI
const updateTableData = (row: any, status: boolean) => {
	if (status && row.status === '已锁定') {
		ElMessage.warning('已锁定数据不能编辑，请解锁后编辑');
		return;
	}
	const index = findRowIndex(row);
	if (index !== -1) {
		tableData.value[index].statusVerify = status;
		getCurrentTable()?.value.onAssignedData(tableData.value);
	}
};

// 编辑
const handleEdit = (row: any) => updateTableData(row, true);

// 取消
const handleCancel = (row: any) => {
	const index = findRowIndex(row);
	if (index !== -1) {
		// 恢复备份数据
		BACKUP_FIELDS.forEach((field) => {
			tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
		});
		updateTableData(row, false);
	}
};

// 保存
const handleSave = async (row: any) => {
	let a = BACKUP_FIELDS;
	const index = findRowIndex(row);
	if (index === -1) return;
	updateTableData(row, false);
	// 删除备份字段
	BACKUP_FIELDS.forEach((field) => {
		delete row[`${field}_Backup`];
	});
	try {
		const response = await EditFXGoodsBalance({ ...row });
		// 处理 API 返回的数据
		if (response.success) {
			backupTableData.value = [...tableData.value]; // 深拷贝，避免数据引用问题
			ElMessage.success('保存成功');
			// 创建新的备份
			BACKUP_FIELDS.forEach((field) => {
				tableData.value[index][`${field}_Backup`] = tableData.value[index][field];
			});
			getCurrentTable()?.value.getList();
		} else {
			// ElMessage.error(response.msg || '保存失败');
			a.forEach((field) => {
				tableData.value[index][field] = tableData.value[index][`${field}_Backup`];
			});
		}
	} catch (error) {
		console.error('API 请求失败:', error);
		ElMessage.error('保存请求失败，请稍后再试！');
	}
};

const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

const onLockUnlock = async (type: 'lock' | 'unlock') => {
	if (checkboxList.value.length === 0) {
		ElMessage.warning('请选择需要锁定/解锁的数据');
		return;
	}
	ElMessageBox.confirm(`确定要${type === 'lock' ? '锁定' : '解锁'}这些数据吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const params = {
				platform: 11,
				mesgIdList: checkboxList.value.map((item: any) => item.mesgID),
			};
			const { success } = type === 'lock' ? await LockDailyBalanceData(params) : await UnLockDailyBalanceData(params);
			if (success) {
				ElMessage.success('操作成功');
				getList();
			}
		})
		.catch(() => {
			ElMessage.info(`已取消${type === 'lock' ? '锁定' : '解锁'}`);
		});
};

const exportProps = async () => {
	const handleExport = async (exportFunc: any, fileNamePrefix: string) => {
		try {
			const data = await exportFunc({
				...query.value,
			});
			if (data) {
				const aLink = document.createElement('a');
				const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.download = `${fileNamePrefix}-${new Date().toLocaleString()}.xlsx`;
				document.body.appendChild(aLink); // 兼容Firefox
				aLink.click();
				document.body.removeChild(aLink);
			}
		} finally {
			rootDisabled.value = false;
		}
	};
	await handleExport(ExportFXDailyBalance, '分销');
};

const onSummaryTotalMap = async (row: any, column: any, shopId: string) => {
	trendChart.value.status = query.value.status ? query.value.status : [];
	onTrendChartMethod(1);
};

const onTrendChartMethod = async (val: number) => {
	const config = {
		name: '货款余额趋势图',
		api: GetFXGoodsAccountTotalMap,
	};
	totalMapName.value = config.name;
	if (val === 1) {
		trendChart.value.startDate = dayjs(query.value.endDate).subtract(1, 'month').format('YYYY-MM-DD');
		trendChart.value.endDate = query.value.endDate;
	} else {
		trendChart.value.startDate = dayjs(trendChart.value.startDate).format('YYYY-MM-DD');
		trendChart.value.endDate = dayjs(trendChart.value.endDate).format('YYYY-MM-DD');
	}
	const params = {
		...query.value,
		startDate: trendChart.value.startDate,
		endDate: trendChart.value.endDate,
		status: trendChart.value.status,
	};
	const res = await config.api(params);
	analysisData.value = res;
	sumChart.value?.reSetChart(analysisData.value);
	totalMapVisible.value = true;
};

const onTotalMap = async (row: any) => {
	onTrendChartMethod(1);
};

const getList = () => {
	table.value.clearSelection();
	table.value.query.currentPage = 1;
	table.value.getList();
	checkboxList.value = [];
};

const BACKUP_FIELDS = ['endBalance', 'openingBalance', 'withDrawAmount', 'totalIncome', 'totalOutcome', 'frozenAmount'] as const;

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		item.statusVerify = false;
		// 创建备份
		BACKUP_FIELDS.forEach((field) => {
			item[`${field}_Backup`] = item[field];
		});
	});
	tableData.value = data.data.list;
	backupTableData.value = JSON.parse(JSON.stringify(data.data.list));
	callback(data);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'date', title: '日期', width: '85', formatter: 'formatDate' },
	{ sortable: true, field: 'rpaDate', title: '抓取时间', width: '135' },
	{ summaryEvent: true, sortable: true, field: 'openingBalance', title: '期初余额', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'endBalance', title: '期末余额', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'withDrawAmount', title: '提现', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'frozenAmount', title: '冻结金额', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalIncome', title: '总收入', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'totalOutcome', title: '总支出', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ summaryEvent: true, sortable: true, field: 'netCashFlow', title: '净现金流入', width: 'auto', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'checkStatus', title: '验算', width: '70' },
	{ sortable: true, field: 'status', title: '状态', width: '70' },
	{
		align: 'center',
		type: 'btnList',
		field:'20250608095311',
		width: '130',
		fixed: 'right',
		btnList: [
			{ title: '趋势图', handle: onTotalMap },
			{ title: '编辑', handle: handleEdit, isDisabled: (row) => row.statusVerify, permissions: 'FinancialFundsEditor' },
			{ title: '保存', handle: handleSave, isDisabled: (row) => !row.statusVerify, permissions: 'FinancialFundsEditor' },
			{ title: '取消', handle: handleCancel, isDisabled: (row) => !row.statusVerify, permissions: 'FinancialFundsEditor' },
		],
	},
]);

onMounted(() => {
	if (!timeRange.value) {
		timeRange.value = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		query.value.startDate = timeRange.value;
		query.value.endDate = timeRange.value;
	}
});
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 65px;
}

::v-deep .custom-cascader .el-tag.is-closable.el-tag--info.el-tag--small.el-tag--light {
	max-width: 90px;
}

.container-relative {
	position: relative;
	width: 100%;
	height: 100%;
}

.radio-group-absolute {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	display: flex;
	align-items: center;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}
</style>
