<template>
	<el-config-provider :size="getGlobalComponentSize" :locale="getGlobalI18n" :zIndex="999">
		<router-view v-show="setLockScreen" />
		<LockScreen v-if="CwglThemeConfig.isLockScreen" />
		<Setings ref="setingsRef" v-show="setLockScreen" />
		<CloseFull v-if="!CwglThemeConfig.isLockScreen" />
		<!-- <Upgrade v-if="needUpdate" /> -->
		<!-- <Sponsors /> -->
	</el-config-provider>
</template>

<script setup lang="ts" name="app">
import { defineAsyncComponent, computed, ref, onBeforeMount, onMounted, onUnmounted, nextTick, watch, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import { Local, Session } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';
import setIntroduction from '/@/utils/setIconfont';
import { PageReqLog } from '/@/api/admin/loginLog';
// 引入组件
const LockScreen = defineAsyncComponent(() => import('/@/layout/lockScreen/index.vue'));
// const Setings = defineAsyncComponent(() => import('/@/layout/navBars/topBar/setings.vue'));
const Setings = defineAsyncComponent(() => import('/@/layout/navBars/topBar/settings.vue'));
const CloseFull = defineAsyncComponent(() => import('/@/layout/navBars/topBar/closeFull.vue'));
// const Upgrade = defineAsyncComponent(() => import('/@/layout/upgrade/index.vue'));
// const Sponsors = defineAsyncComponent(() => import('/@/layout/sponsors/index.vue'));

// 定义变量内容
const { messages, locale } = useI18n();
const setingsRef = ref();
const route = useRoute();
const stores = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { CwglThemeConfig } = storeToRefs(storesThemeConfig);
const needUpdate = ref(false);

// 设置锁屏时组件显示隐藏
const setLockScreen = computed(() => {
	// 防止锁屏后，刷新出现不相关界面
	// https://gitee.com/lyt-top/vue-next-admin/issues/I6AF8P
	return CwglThemeConfig.value.isLockScreen ? CwglThemeConfig.value.lockScreenTime > 1 : CwglThemeConfig.value.lockScreenTime >= 0;
});
const pageOperateLog = ref({
	action: '',
	modul: '',
	pageName: '',
	pageTab: '',
});
// // 获取版本号
// const getVersion = computed(() => {
// 	let isVersion = false;
// 	if (route.path !== '/login') {
// 		// @ts-ignore
// 		if ((Local.get('version') && Local.get('version') !== __NEXT_VERSION__) || !Local.get('version')) isVersion = true;
// 	}
// 	return isVersion;
// });

// checkUpdate(() => {
// 	needUpdate.value = true;
// }, 60000);

const state = reactive({
	pagedata: {},
});

// 获取全局组件大小
const getGlobalComponentSize = computed(() => {
	return other.globalComponentSize();
});
// 获取全局 i18n
const getGlobalI18n = computed(() => {
	return messages.value[locale.value];
});
// 设置初始化，防止刷新时恢复默认
onBeforeMount(() => {
	// 设置批量第三方 icon 图标
	setIntroduction.cssCdn();
	// 设置批量第三方 js
	setIntroduction.jsCdn();
});
// 页面加载时
onMounted(() => {
	window.addEventListener('mousedown', downclilog);
	nextTick(() => {
		// 监听布局配'置弹窗点击打开
		mittBus.on('openSettingsDrawer', () => {
			setingsRef.value.openDrawer();
		});
		// 获取缓存中的布局配置
		if (Local.get('CwglThemeConfig')) {
			storesThemeConfig.setThemeConfig({ CwglThemeConfig: Local.get('CwglThemeConfig') });
			document.documentElement.style.cssText = Local.get('CwglThemeConfigStyle');
		}
		// 获取缓存中的全屏配置
		if (Session.get('isTagsViewCurrenFull')) {
			stores.setCurrenFullscreen(Session.get('isTagsViewCurrenFull'));
		}
	});
});

const downclilog = (res: any) => {
	if (res.target.textContent) {
		pageOperateLog.value.action = res.target?.textContent;
		const lis = document.querySelectorAll('.el-breadcrumb__inner');
		let actives = document.querySelectorAll('.is-active')
		setTimeout(async () => {
			for (var i = 0; i < lis.length; i++) {
				if (lis.length == i + 1) {
					pageOperateLog.value.modul = (lis[0] as HTMLElement)?.innerText;
					pageOperateLog.value.pageName = (lis[lis.length - 1] as HTMLElement)?.innerText;
				}
			}
			if (pageOperateLog.value.pageName) {
				pageOperateLog.value.pageTab = actives[actives.length - 1].textContent as string;
			}
			if (checkAndTrimString(pageOperateLog.value.action)) {
				await PageReqLog(pageOperateLog.value.modul, pageOperateLog.value.pageName, pageOperateLog.value.pageTab ? pageOperateLog.value.pageTab : pageOperateLog.value.pageName, checkAndTrimString(pageOperateLog.value.action));
			}
		}, 500);
	}
};

const isAllDigits = (str: string) => {
	let value = str.trim();
	var noArr = ['自动计算', '全部'];
	if (noArr.includes(value)) {
		return false;
	}
	if (typeof value === 'number') {
		return !Number.isFinite(value);
	}

	if (typeof value === 'string') {
		return !/^-?\d+(\.\d+)?$/.test(value);
	}
	return true;
};
const checkAndTrimString = (str: any) => {
	if (pageOperateLog.value.action == pageOperateLog.value.pageTab) {
		//点击一级页签 放行
		return str;
	}
	if (pageOperateLog.value.action == pageOperateLog.value.pageName) {
		//点击左侧菜单归动作查询 放行
		return '查询';
	}
	if (str.length > 10) {
		//超过10长度 限行
		return false;
	}
	let isnext = nameisExitfuc(str);
	if (!isnext) {
		//不包含于关键字 限行
		return false;
	}
	const trimmedStr = str.replace(/\s/g, '');
	return trimmedStr;
};
const nameisExitfuc = (name: any) => {
	//判断字符串包含
	let nameisExit = false;
	let namearr = ['取', '确', '打', '增', '添', '新', '删', '改', '修', '提', '审', '保', '编', '撤', '批', '上', '操', '采', '日', '查', '导'];
	namearr.map((item) => {
		if (name.indexOf(item) != -1) {
			nameisExit = true;
		}
	});
	return nameisExit;
};
// 页面销毁时，关闭监听布局配置/i18n监听
onUnmounted(() => {
	mittBus.off('openSetingsDrawer', () => { });
	let _this = state;
	window.removeEventListener('mousedown', downclilog);
});
// 监听路由的变化，设置网站标题
watch(
	() => route.path,
	() => {
		other.useTitle();
	},
	{
		deep: true,
	}
);
</script>

<style lang="scss">
.topCss {
	display: flex;
	flex-wrap: wrap;
	align-items: center;

	.publicCss {
		margin: 0 10px 5px 0;
		width: 150px;
	}
}

.el-form--inline {
	.el-form-item {
		.el-select {
			width: 171px !important;
		}

		.el-select__wrapper {
			line-height: 22px !important;
		}

		.el-date-editor {
			--el-date-editor-width: 171px !important;
		}

		.el-input {
			width: 171px !important;
		}
	}
}
</style>
