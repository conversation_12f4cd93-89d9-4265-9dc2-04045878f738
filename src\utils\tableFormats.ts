import { VxeUI } from 'vxe-pc-ui';
import dayjs from 'dayjs';
import { platformlist, platformLink } from './tools';

VxeUI.formats.add('formatPlatform', {
	cellFormatMethod({ cellValue, row }, digits = 2) {
		return platformlist.find((item) => item.value === cellValue)?.label || '';
	},
});
//格式化时间
VxeUI.formats.add('formatDate', {
	cellFormatMethod({ cellValue, row }, digits = 2) {
		return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
	},
});

//格式化日期
VxeUI.formats.add('formatTime', {
	cellFormatMethod({ cellValue, row }, digits = 2) {
		return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
	},
});

//格式化日期
VxeUI.formats.add('formatBoolean', {
	cellFormatMethod({ cellValue, row }, digits = 2) {
		return cellValue ? '是' : '否';
	},
});

//格式化日期
VxeUI.formats.add('formatLinkProcode', {
	cellFormatMethod({ cellValue, row }, digits = 2) {
		if (row.procode && row.platform) {
			const link = platformLink[row.platform];
			return `<a href="${link}${row.procode}" target="_blank" style="color: #1000ff;">${row.procode}</a>`;
		}
		return row.procode;
	},
});
