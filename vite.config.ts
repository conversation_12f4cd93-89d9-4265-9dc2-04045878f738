import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv } from 'vite';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import compression from 'vite-plugin-compression2';
import { buildConfig } from './src/utils/build';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { CodeInspectorPlugin } from 'code-inspector-plugin';
import fs from 'fs';
import { visualizer } from 'rollup-plugin-visualizer';
import { webUpdateNotice } from '@plugin-web-update-notification/vite';
const pathResolve = (dir: string) => {
	return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
	'/@': pathResolve('./src/'),
	'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};
const viteConfig = defineConfig((mode: ConfigEnv) => {
	const env = loadEnv(mode.mode, process.cwd());
	const domain = env.VITE_APP_NET_BASE_API;
	fs.writeFileSync('./public/config.js', `window.__env__ = ${JSON.stringify(env, null, 2)} `);
	return {
		plugins: [
			visualizer({ open: false }), // 开启可视化分析页面
			CodeInspectorPlugin({
				bundler: 'vite',
				hotKeys: ['shiftKey'],
			}),
			vue(),
			vueJsx(),
			webUpdateNotice({
				notificationConfig: {
					placement: 'topLeft',
				},
				notificationProps: {
					title: '📢 系统更新',
					description: '系统更新啦，请刷新页面！',
					buttonText: '刷新',
					dismissButtonText: '忽略',
				},
			}),
			vueSetupExtend(),
			compression({
				deleteOriginalAssets: false, // 是否删除源文件
				threshold: 5120, // 对大于 5KB 文件进行 gzip 压缩，单位Bytes
				skipIfLargerOrEqual: true, // 如果压缩后的文件大小等于或大于原始文件，则跳过压缩
			}),
			JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null,
		],
		root: process.cwd(),
		resolve: { alias },
		base: '/',
		optimizeDeps: { exclude: ['vue-demi'] },
		server: {
			host: '0.0.0.0',
			port: 9002,
			// open: true,
			proxy: {
				['^' + env.VITE_APP_BASE_API]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_MsgCenter]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Express]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Financial]: {
					// target: domain,
          target: 'http://**************:8060',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Order]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_OperateManage]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Inventory]: {
					target: domain,
					// target: 'http://192.168.16.80:8081',
					// target: 'http://192.168.104.10:8040',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_ImportInventory]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_OpenPlatform]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Warning]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_OldErp]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_BookKeeper]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_MonthBookKeeper]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_CustomerService]: {
					// target: domain,
					target: 'http://192.168.104.12:8100',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Profit]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Media]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Distribution]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_PddPlatform]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_PddOperateManage]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_PackProcess]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_VerifyOrder]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_JAVA_API_BLADEGATEWAY]: {
					target: domain,
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_CwManage]: {
					// target: domain,
					// target: 'http://192.168.104.12:8160',
					// target: 'http://192.168.104.23:8160',
					target: 'http://192.168.16.80:7000',
					// target: 'http://192.168.104.122:8160',
					// target: 'http://192.168.104.40:8160',
					// target: 'http://192.168.104.23:8160',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_Financewh]: {
					target: 'http://192.168.16.20:30178',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
				['^' + env.VITE_APP_BASE_API_UpLoadNew]: {
					target: 'https://nanc.yunhanmy.com:10010',
					ws: true,
					changeOrigin: true,
					logLevel: 'debug',
				},
			},
		},
		build: {
			outDir: 'dist',
			chunkSizeWarningLimit: 1500,
			assetsInlineLimit: 5000, // 小于此阈值的导入或引用资源将内联为 base64 编码
			sourcemap: false, // 构建后是否生成 source map 文件
			extractComments: false, // 移除注释
			minify: 'terser', // 启用后 terserOptions 配置才有效
			terserOptions: {
				compress: {
					drop_console: true, // 生产环境时移除console
					drop_debugger: true,
				},
			},
			rollupOptions: {
				output: {
					chunkFileNames: 'assets/js/[name]-[hash].js', // 引入文件名的名称
					entryFileNames: 'assets/js/[name]-[hash].js', // 包的入口文件名称
					assetFileNames: 'assets/[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!.moduleName ?? 'vender';
						}
					},
				},
				...(JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}),
			},
		},
		css: { preprocessorOptions: { css: { charset: false } } },
		define: {
			__VUE_I18N_LEGACY_API__: JSON.stringify(false),
			__VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
			__INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
			__NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
		},
	};
});

export default viteConfig;
