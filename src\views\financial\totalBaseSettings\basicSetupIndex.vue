<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="凭证科目设置" name="first" style="height: 100%">
					<basicCredentialSettings />
				</el-tab-pane>
				<el-tab-pane label="辅助核算设置" name="second" style="height: 100%" lazy>
					<auxiliaryAccounting />
				</el-tab-pane>
				<el-tab-pane label="现金支出科目" name="third" style="height: 100%" lazy>
					<cashDisbursementAccount />
				</el-tab-pane>
				<el-tab-pane label="公司开票信息" name="fourth" style="height: 100%" lazy>
					<companyInvoicingInformation />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const basicCredentialSettings = defineAsyncComponent(() => import('./components/basicCredentialSettings.vue'));
const auxiliaryAccounting = defineAsyncComponent(() => import('./components/auxiliaryAccounting.vue'));
const cashDisbursementAccount = defineAsyncComponent(() => import('./components/cashDisbursementAccount.vue'));
const companyInvoicingInformation = defineAsyncComponent(() => import('./components/companyInvoicingInformation.vue'));
const activeName = ref('first');
</script>
