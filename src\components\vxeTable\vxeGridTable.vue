<template>
	<div style="height: 100%; width: 100%; margin: 0; display: block">
		<vxe-grid
			:id="props.id"
			ref="gridRef"
			v-bind="gridOptions"
			@checkbox-change="checkboxChange"
			@checkbox-all="checkboxAll"
			@checkbox-range-end="checkboxRangeEnd"
			@cell-click="cellClick"
			:footer-data="footerData"
			@span-method="spanMethod"
			@row-style="rowStyle"
		>
			<!-- 工具栏左侧按钮插槽 -->
			<template #toolbar_buttons>
				<slot name="toolbar_buttons" />
			</template>
			<!-- 图片 -->
			<template #image="{ row, column }">
				<!-- 字符串图片 -->
				<el-badge
					v-if="row[column.field] && !Array.isArray(row[column.field]) && row[column.field][0] != '[' && row[column.field].split(',').length > 0"
					:value="row[column.field].split(',').length > 1 ? row[column.field].split(',').length : ''"
					class="item"
				>
					<el-image
						style="width: 50px; height: 50px"
						:src="row[column.field].split(',')[0]"
						:zoom-rate="1.2"
						:max-scale="7"
						:min-scale="0.2"
						:preview-src-list="row[column.field].split(',')"
						:initial-index="4"
						fit="cover"
					/>
				</el-badge>
				<!-- 数组图片 -->
				<el-badge v-else-if="row[column.field] && Array.isArray(row[column.field]) && row[column.field].length > 0" :value="row[column.field].length > 1 ? row[column.field].length : ''" class="item">
					<el-image
						style="width: 50px; height: 50px"
						:src="row[column.field][0]"
						:zoom-rate="1.2"
						:max-scale="7"
						:min-scale="0.2"
						:preview-src-list="row[column.field]"
						:initial-index="4"
						fit="cover"
					/>
				</el-badge>
				<!-- JSON数组图片 -->
				<el-badge
					v-else-if="row[column.field] && !Array.isArray(row[column.field]) && row[column.field][0] == '[' && JSON.parse(row[column.field]).length > 0"
					:value="JSON.parse(row[column.field]).length > 1 ? JSON.parse(row[column.field]).length : ''"
					class="item"
				>
					<el-image
						style="width: 50px; height: 50px"
						:src="JSON.parse(row[column.field])[0]"
						:zoom-rate="1.2"
						:max-scale="7"
						:min-scale="0.2"
						:preview-src-list="JSON.parse(row[column.field])"
						:initial-index="4"
						fit="cover"
					/>
				</el-badge>
			</template>
			<!-- 文字按钮 -->
			<template #click="{ row, column }">
				<div v-for="item in props.tableCols" class="alignDisplay" :style="{ justifyContent: column.align == 'center' ? 'center' : column.align == 'right' ? 'end' : 'start' }">
					<el-button v-if="item.title === column.title && item.colType === 'click'" type="primary" link @click="item.handle && item.handle(row)" :disabled="item.isDisabled && item.isDisabled(row)">{{
						column.title
					}}</el-button>
				</div>
			</template>
			<!-- 多个文字按钮 -->
			<template #btnList="{ row, column }">
				<div v-for="item in props.tableCols" class="alignDisplay" :style="{ justifyContent: column.align == 'center' ? 'center' : column.align == 'right' ? 'end' : 'start' }">
					<div v-if="item.btnList && Array.isArray(item.btnList) && item.title === column.title" v-for="btnItem in item.btnList" class="publicMargin">
						<el-button type="primary" link @click="btnItem.handle && btnItem.handle(row)" :disabled="btnItem.isDisabled && btnItem.isDisabled(row)">{{ btnItem.title }}</el-button>
					</div>
				</div>
			</template>
		</vxe-grid>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, defineExpose, onMounted } from 'vue';
import type { VxeGridInstance, VxeTablePropTypes } from 'vxe-table';
const props = defineProps({
	id: { type: String, default: '' },
	isNeedDisposeProps: { type: Boolean, default: false },
	currentPage: { type: Number, default: 1 }, //当前页
	pageSize: { type: Number, default: 50 }, //每页显示条数
	pageSizes: { type: Array, default: () => [50, 100, 200, 300] }, //页码尺寸
	zoom: { type: Boolean, default: true }, //全屏
	refresh: { type: Boolean, default: false }, //刷新
	custom: { type: Boolean, default: true }, //工具栏
	border: { type: Boolean, default: true }, //边框
	showOverflow: { type: Boolean, default: true }, //超出隐藏
	tableCols: { type: Array<VxeTable.Columns>, default: () => [] }, //列头
	labelField: { type: String, default: 'name' }, //复选框显示的值,对应字段
	remoteSort: { type: Boolean, default: true }, //是否服务端排序
	sortMultiple: { type: Boolean, default: false }, //是否允许多列排序
	proxyProps: { type: Object, default: () => {} }, //代理配置
	queryFunc: { type: Function, default: () => {} }, //查询接口
	query: { type: Object, default: () => {} }, //查询参数
	keyField: { type: String, default: 'id' }, //配置主键字段
	showsummary: { type: Boolean, default: false }, //显示表尾
	showHeaderOverflow: { type: Boolean, default: true }, //表头内容过长时是否显示省略号
	sameRow: { type: String, default: () => '' }, //合并行或列
	autoHidden: { type: Boolean, default: false }, //自动隐藏 只有一页数据就自动隐藏
});
const footerData = ref<VxeTablePropTypes.FooterData>([]); //表尾数据
const query = ref({
	currentPage: 1,
	pageSize: 50,
	orderBy: '',
	isAsc: false,
});
const emit = defineEmits(['getList', 'select', 'cellClick', 'disposeProps', 'rowStyle']);
const gridRef = ref<VxeGridInstance>();
const gridOptions = reactive({
	border: props.border, //边框
	stripe: true, //斑马条纹
	showOverflow: props.showOverflow, //超出隐藏
	showHeaderOverflow: props.showHeaderOverflow, //表头内容过长时是否显示省略号
	showFooter: props.showsummary, //显示表尾
	round: true, //边框是否圆角
	height: 'auto', //高度
	printConfig: {}, //打印配置
	scrollY: {
		//纵向虚拟滚动条
		enabled: true,
		gt: 0,
	},
	scrollX: {
		//横向虚拟滚动条
		enabled: true,
		gt: 0,
	},
	customConfig: {
		storage: {
			visible: true, //启用显示/隐藏列状态缓存
			resizable: true, //启用列宽拖动
			sort: true, //启用排序状态缓存
			fixed: true, //启用固定列状态缓存
		},
	},
	columnConfig: {
		useKey: true,
		minWidth: '40px',
		isCurrent: true, //高亮当前列
		resizable: true, //列宽拖动
		maxFixedSize: 300,
	},
	pagerConfig: {
		//配置分页,只有配置了分页才能使用刷新按钮
		currentPage: props.currentPage, //当前页
		pageSize: props.pageSize, //每页显示条数
		enabled: true, //是否启用分页
		pageSizes: props.pageSizes, //页码尺寸
		autoHidden: props.autoHidden, //自动隐藏 只有一页数据就自动隐藏
	},
	rowConfig: {
		useKey: true, //启用键盘导航
		keyField: props.keyField, //配置主键字段
		isHover: true, //当鼠标移到行时，是否要高亮当前行
		isCurrent: true, //是否要高亮当前行
	},
	checkboxConfig: {
		labelField: props.labelField, //配置复选框显示的值,对应字段
		checkField: 'checked', //配置复选框绑定的值,对应字段
		highlight: true, //高亮勾选行
		reserve: true, //保留勾选行
		range: true, //启用范围选择
		strict: true, //严格模式，当数据为空或全部禁用时，列头的复选框为禁用状态
	},
	toolbarConfig: {
		export: true, //导出
		print: true, //打印
		zoom: props.zoom, //全屏
		refresh: props.refresh, //刷新
		custom: props.custom, //工具栏
		slots: {
			buttons: 'toolbar_buttons',
		},
	},
	columns: props.tableCols, //列头
	proxyConfig: {
		//代理配置
		props: {
			result: 'data',
			total: 'total',
		},
		sort: true, //启用排序代理，当点击排序时会自动触发 query 行为
		seq: true, //启用动态序号代理，每一页的序号会根据当前页数变化
		ajax: {
			//调用查询接口
			query: async ({ page, sort }: any) => {
				query.value = {
					currentPage: page.currentPage,
					pageSize: page.pageSize,
					orderBy: sort.field ? sort.field : query.value.orderBy,
					isAsc: sort.order === 'asc' ? true : false,
					...props.query,
				};
				let data = [];
				const {
					data: { list, total, summary },
				} = await props.queryFunc(query.value);
				if (props.showsummary && summary) {
					footerData.value.push(summary);
				}
				if (props.isNeedDisposeProps) {
					emit('disposeProps', list, (val: any) => {
						data = val;
					});
				} else {
					data = list;
				}
				//返回值与代理配置对应
				return {
					data,
					total,
				};
			},
		},
	},
	sortConfig: {
		remote: props.remoteSort, //是否服务端排序
		multiple: props.sortMultiple, //是否允许多列排序
		defaultSort: {
			field: query.value.orderBy,
			order: query.value.isAsc ? 'asc' : 'desc',
		},
	},
});
//点击复选框
const checkboxChange = () => {
	console.log('checkboxChange');
	const row = gridRef.value?.getCheckboxRecords();
	emit('select', row);
};
//全选
const checkboxAll = ({ records }: any) => {
	emit('select', records);
};
//范围选择结束
const checkboxRangeEnd = ({ records }: any) => {
	console.log('checkboxRangeEnd');
	emit('select', records);
};
//点击行
const cellClick = ({ row }: any) => {
	emit('cellClick', row);
};
//行样式
const rowStyle = ({ row }: any) => {
	let styleitem = null;
	emit('rowStyle', row, (val: any) => {
		styleitem = val;
	});
	return styleitem;
};
// 通用行合并函数（将相同多列数据合并为一行）
const spanMethod = ({ row, _rowIndex, column, visibleData }: any) => {
	const fields = [...props.sameRow];
	const cellValue = row[column.property];
	if (cellValue && fields.includes(column.property)) {
		const prevRow = visibleData[_rowIndex - 1];
		let nextRow = visibleData[_rowIndex + 1];
		if (prevRow && prevRow[column.property] === cellValue) {
			return { rowspan: 0, colspan: 0 };
		} else {
			let countRowspan = 1;
			while (nextRow && nextRow[column.property] === cellValue) {
				nextRow = visibleData[++countRowspan + _rowIndex];
			}
			if (countRowspan > 1) {
				return { rowspan: countRowspan, colspan: 1 };
			}
		}
	}
};

defineExpose({
	getList: () => {
		const sorts: any = gridRef.value?.getSortColumns(); // 获取当前的排序信息
		query.value = {
			...query.value,
			orderBy: sorts?.field || query.value.orderBy,
			isAsc: sorts?.order === 'asc' || query.value.isAsc,
		};
		gridRef.value?.commitProxy('query');
	},
});
</script>
<style lang="scss" scoped>
.alignDisplay {
	display: flex;
	justify-content: center;
}

.publicMargin {
	margin-right: 10px;
}

.item {
	::v-deep .el-badge__content.is-fixed {
		top: 8px;
	}
}
</style>
