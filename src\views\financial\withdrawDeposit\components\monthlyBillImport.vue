<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startOccurenceTime" v-model:endDate="query.endOccurenceTime" style="width: 200px" :clearable="false" />
				<el-select v-model="query.bankType" placeholder="行名" class="publicCss" filterable clearable>
					<el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.account" placeholder="账号" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.accountName" placeholder="别名" class="publicCss" filterable clearable>
					<el-option v-for="item in props.expenseTypeList" :key="item.account" :label="item.accountName" :value="item.accountName" />
				</el-select>
				<el-select v-model="query.flowType" placeholder="交易类型" class="publicCss" filterable clearable>
					<el-option key="收入" label="收入" value="收入" />
					<el-option key="支出" label="支出" value="支出" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="startImport">导入</el-button>
					<el-button @click="exportProps" type="primary">导出</el-button>
					<el-button @click="computeProps(true)" type="primary">全量计算</el-button>
					<!-- <el-dropdown @command="computeProps" size="large" style="margin-left: 10px">
						<el-button type="primary">
							计算<el-icon class="el-icon--right"><arrow-down /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item :command="true">全量</el-dropdown-item>
								<el-dropdown-item :command="false">部分</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown> -->
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="**************" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="GetBankFlowMonthRecordList"></vxetable>
			<el-dialog title="导入数据" v-model="dialogVisible" width="600px" draggable :close-on-click-modal="false" center destroy-on-close>
				<div class="import-dialog-content">
					<div class="form-group">
						<el-date-picker v-model="upForm.importDate" type="date" placeholder="日期" :clearable="false" style="width: 150px" format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="publicCss" />
						<span class="required-mark">*</span>
						<el-select v-model="upForm.account" placeholder="行名" style="width: 180px" class="publicCss" filterable clearable>
							<el-option v-for="item in props.expenseTypeList" :key="item.account" :label="item.accountName" :value="item.account" />
						</el-select>
						<div class="alipay-select" v-if="upForm.account && onAlipay(upForm.account)">
							<span class="required-mark">*</span>
							<el-select v-model="upForm.isAlipay" class="publicCss" placeholder="交易类型" style="width: 120px" clearable>
								<el-option label="余额" value="余额" />
								<el-option label="余额宝" value="余额宝" />
								<el-option label="诚信赊" value="诚信赊" />
							</el-select>
						</div>
					</div>
					<el-upload
						ref="uploadFile"
						class="upload-demo"
						:auto-upload="false"
						:multiple="false"
						:limit="1"
						action=""
						accept=".xlsx,.xls,.csv"
						:file-list="fileLists"
						:data="fileparm"
						:http-request="onUploadFile"
						:on-success="onUploadSuccess"
						:on-change="onUploadChange"
						:on-remove="onUploadRemove"
					>
						<template #trigger>
							<el-button size="default" type="primary">选取文件</el-button>
						</template>
						<el-button style="margin-left: 10px" size="default" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>
							{{ uploadLoading ? '上传中' : '上传' }}
						</el-button>
					</el-upload>
				</div>
				<div style="display: flex; justify-content: end; align-items: center">
					<el-button @click="dialogVisible = false">关闭</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import { GetBankFlowMonthRecordList, ExportBankFlowMonthRecord, ComputeFee, ImportBankFlowMonth } from '/@/api/cwManager/bankFlowMonth';
import { ElMessageBox } from 'element-plus';
import { bankList } from '/@/utils/tools';
const props = defineProps({
	expenseTypeList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
});
const query = ref({
	startOccurenceTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endOccurenceTime: dayjs().format('YYYY-MM-DD'),
	bankType: null,
	account: null,
	accountName: null,
	flowType: null,
});
const upForm = ref({
	account: null as string | null,
	isAlipay: null,
	importDate: '',
	accountName: '',
});
const dialogVisible = ref(false);
const fileLists = ref([]);
const fileparm = ref({});
const uploadLoading = ref(false);
const uploadFile = ref();

const table = ref();
const loading = ref(false);
const onAlipay = (account: string) => {
	if (!account) return;
	return props.expenseTypeList.find((item) => item.account == account)?.accountName?.includes('支付宝');
};
const computeProps = async (command: boolean) => {
	ElMessageBox.confirm(`是否计算${command ? '全量' : '部分'}数据?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			loading.value = true;
			const { success } = await ComputeFee({ isCompleted: command });
			loading.value = false;
			if (success) {
				window.$message.success('计算成功');
				table.value.getList();
			}
		})
		.catch(() => {});
};

const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	form.append('account', upForm.value.account || '');
	form.append('isAlipay', onAlipay(upForm.value.account || '') ? upForm.value.isAlipay || '' : '');
	form.append('importDate', upForm.value.importDate);
	try {
		var res = await ImportBankFlowMonth(form);
		if (res?.success) {
			window.$message({ message: res.data || '上传成功,正在导入中...', type: 'success' });
			dialogVisible.value = false;
			table.value.getList();
		}
	} finally {
		uploadLoading.value = false;
	}
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return;
	}
	let verification = onAlipay(upForm.value.account || '');
	if (!upForm.value.account) {
		window.$message({ message: '请先选择行名', type: 'warning' });
		return false;
	}
	if (verification && !upForm.value.isAlipay) {
		window.$message({ message: '请先选择交易类型', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
	upForm.value.account = null;
	upForm.value.isAlipay = null;
	upForm.value.importDate = dayjs().format('YYYY-MM-DD');
};

const exportProps = async () => {
	const { success, msg } = await ExportBankFlowMonthRecord({ ...query.value, ...table.value.query });
	if (success) {
		window.$message({ message: msg || '导出成功', type: 'success' });
	} else {
		window.$message({ message: msg, type: 'warning' });
	}
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'occurenceTime', title: '交易时间', width: '135' },
	{ sortable: true, field: 'bankType', title: '行名', align: 'center' },
	{ sortable: true, field: 'account', title: '账号', width: '160', align: 'center' },
	{ sortable: true, field: 'accountName', title: '别名', width: '150', align: 'center' },
	{ sortable: true, field: 'accountBalance', title: '账户余额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'inAmount', title: '收入金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'outAmount', title: '支出金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'toAccount', title: '对方账号', width: '150', align: 'center' },
	{ sortable: true, field: 'toAccountName', title: '对面名称', width: '200', align: 'center' },
	{ sortable: true, field: 'flowType', title: '交易类型', align: 'center' },
	{ sortable: true, field: 'bankRemark', title: '交易备注', width: '200', align: 'center' },
]);
</script>

<style scoped lang="scss">
.itemCss {
	width: 110px;
	margin: 0 3px 5px 0;
}

.import-dialog-content {
	padding: 10px 0;
}

.form-group {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 20px;
}

.required-mark {
	color: red;
	margin: 0 2px;
}

.alipay-select {
	display: flex;
	align-items: center;
	gap: 5px;
}
</style>
