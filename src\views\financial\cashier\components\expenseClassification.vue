<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-select v-model="query.name" placeholder="费用类型" class="publicCss" clearable filterable>
					<el-option v-for="item in expenseList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.type" placeholder="收支类型" class="publicCss" clearable filterable>
					<el-option key="收入" label="收入" value="收入" />
					<el-option key="支出" label="支出" value="支出" />
				</el-select>
				<el-select v-model="query.sumType" placeholder="费用类型汇总" class="publicCss" clearable filterable>
					<el-option key="未知" label="未知" value="未知" />
					<el-option v-for="item in expenseCollectList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202409251536" :tableCols="tableCols" :query="query" isNeedDisposeProps @disposeProps="disposeProps" :queryApi="QueryCashierFeeType">
				<template #toolbar_buttons>
					<el-button @click="handleAdd(null)" type="primary">新增</el-button>
				</template>
			</vxetable>
		</template>
	</Container>

	<el-dialog v-model="addVisible" :title="isEdit ? '编辑' : '新增'" width="15%" draggable :close-on-click-modal="false">
		<el-form label-width="auto" :model="ruleForm" :rules="rules" style="max-width: 600px">
			<el-form-item label="费用类型:" label-position="right" v-if="isEdit">
				<el-input v-model="ruleForm.name" class="publicCss" placeholder="费用类型" clearable maxlength="50" />
			</el-form-item>
			<el-form-item :label="ruleForm.pId ? '子类型' : '费用类型'" label-position="right" v-if="!isEdit">
				<el-input v-model="ruleForm.name" class="publicCss" :placeholder="ruleForm.pId ? '子类型' : '费用类型'" clearable maxlength="50" />
			</el-form-item>
			<el-form-item label="收支类型" label-position="right" prop="type">
				<el-select v-model="ruleForm.type" placeholder="收支类型" class="publicCss" clearable>
					<el-option label="收入" value="收入" />
					<el-option label="支出" value="支出" />
				</el-select>
			</el-form-item>
			<el-form-item label="费用类型汇总" label-position="right" prop="sumType">
				<el-input v-model="ruleForm.sumType" class="publicCss" placeholder="费用类型汇总" clearable maxlength="50" />
			</el-form-item>
		</el-form>
		<div style="display: flex; justify-content: center; margin-top: 20px">
			<el-button @click="addVisible = false">取消</el-button>
			<el-button type="primary" @click="Submit" v-reclick="1000">确定</el-button>
		</div>
	</el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { ElMessageBox, FormRules } from 'element-plus';
import { QueryCashierFeeType, DeleteCashierFeeType, InsertOrUpdateCashierFeeType, QueryCashierFeeTypeSum } from '/@/api/cwManager/cashierSet';
import { s } from 'vite/dist/node/types.d-aGj9QkWt';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const addVisible = ref(false);
const isEdit = ref(false);
const table = ref();
interface ExpenseType {
	name?: string;
	value: string;
	label: string;
}
const tableData = ref<ExpenseType[]>([]);
const expenseList = ref<ExpenseType[]>([]);
const expenseCollectList = ref<ExpenseType[]>([]);
const query = ref({
	name: '',
	type: '',
	sumType: '',
});
const ruleForm = ref({
	name: '',
	pId: '',
	type: '',
	sumType: '',
});
const rules = ref<FormRules>({
	type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
});
const clear = () => {
	ruleForm.value = {
		name: '',
		pId: '',
		type: '',
		sumType: '',
	};
};
const handleAdd = (row: any) => {
	clear();
	isEdit.value = false;
	if (row) {
		ruleForm.value.pId = row.id;
	}
	addVisible.value = true;
};
const edit = async (row: any) => {
	clear();
	isEdit.value = true;
	ruleForm.value = row ? JSON.parse(JSON.stringify(row)) : ruleForm.value;
	ruleForm.value.type = row.type;
	addVisible.value = true;
};
const handleDelete = async (row: any) => {
	ElMessageBox.confirm('此操作将删除该数据,是否继续?', '提示消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await DeleteCashierFeeType(row);
			if (success) {
				window.$message.success('删除成功');
				getList();
			}
		})
		.catch(() => {
			window.$message.info('已取消删除');
		});
};
const Submit = async () => {
	if (!isEdit) {
		for (let i = 0; i < tableData.value.length; i++) {
			if (ruleForm.value.name == tableData.value[i].name) {
				window.$message.error('该费用类型已存在');
				return;
			}
		}
	}
	if (!ruleForm.value.type) {
		window.$message.error('请选择类型');
		return;
	}
	const { success } = await InsertOrUpdateCashierFeeType(ruleForm.value);
	if (success) {
		addVisible.value = false;
		window.$message.success(isEdit.value ? '保存成功' : '新增成功');
		getList();
		setTimeout(() => {
			getAllDept();
		}, 0);
	}
};
const getList = () => {
	table.value.getList();
};
const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'name', title: '费用类型' },
	{ sortable: true, field: 'type', title: '收支类型' },
	{ sortable: true, field: 'sumType', title: '费用类型汇总' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		btnList: [
			// { title: '新增', handle: handleAdd },
			{ title: '编辑', handle: edit },
			{ title: '删除', handle: handleDelete },
		],
	},
]);
const disposeProps = async (data: any, callback: Function) => {
	tableData.value = data.data.list;
	callback(data);
};
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryCashierFeeType({ currentPage: 1, pageSize: 10000000 });
	if (!success1) return;
	tableData.value = data1.list;
	expenseList.value = Array.from(new Set(tableData.value.map((item: any) => item.name).filter(Boolean))).map((item) => ({ label: item, value: item }));
	expenseCollectList.value = Array.from(new Set(tableData.value.map((item: any) => item.sumType).filter(Boolean))).map((item) => ({ label: item, value: item }));
};
onMounted(async () => {
	getAllDept();
});
</script>

<style scoped lang="scss"></style>
