import request from '/@/utils/yhrequest';
//凭证基础设置
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/AccountsChart/`;

//辅助核算
const apiPrefixa = `${import.meta.env.VITE_APP_BASE_API_CwManage}/AccountsAuxiliary/`;

//现金支出科目
const apiPrefixb = `${import.meta.env.VITE_APP_BASE_API_CwManage}/CashOutlayChart/`;

//凭证基础设置-查询
export const QueryAccountsChart = (params: any, config = {}) => request.post(apiPrefix + 'QueryAccountsChart', params, config);

//凭证基础设置-导出
export const ExportAccountsChart = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAccountsChart', params, config);

//凭证基础设置-新增
export const InsertAccountsChart = (params: any, config = {}) => request.post(apiPrefix + 'InsertAccountsChart', params, config);

//凭证基础设置-编辑
export const UpdateAccountsChart = (params: any, config = {}) => request.post(apiPrefix + 'UpdateAccountsChart', params, config);

//凭证基础设置-删除
export const DeleteAccountsChart = (params: any, config = {}) => request.post(apiPrefix + 'DeleteAccountsChart?id=' + params.id, params, config);

//辅助核算-查询
export const QueryAccountsAuxiliary = (params: any, config = {}) => request.post(apiPrefixa + 'QueryAccountsAuxiliary', params, config);

//辅助核算-导出
export const ExportAccountsChartiary = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixa + 'ExportAccountsChart', params, config);

//辅助核算-新增
export const InsertAccountsAuxiliary = (params: any, config = {}) => request.post(apiPrefixa + 'InsertAccountsAuxiliary', params, config);

//辅助核算-编辑
export const UpdateAccountsAuxiliary = (params: any, config = {}) => request.post(apiPrefixa + 'UpdateAccountsAuxiliary', params, config);

//辅助核算-删除
export const DeleteAccountsAuxiliary = (params: any, config = {}) => request.post(apiPrefixa + 'DeleteAccountsAuxiliary?id=' + params.id, params, config);

//现金支出科目-查询
export const QueryCashOutlayChart = (params: any, config = {}) => request.post(apiPrefixb + 'QueryCashOutlayChart', params, config);

//现金支出科目-导出
export const ExportCashOutlayChart = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefixb + 'ExportCashOutlayChart', params, config);

//现金支出科目-新增
export const InsertCashOutlayChart = (params: any, config = {}) => request.post(apiPrefixb + 'InsertCashOutlayChart', params, config);

//现金支出科目-编辑
export const UpdateCashOutlayChart = (params: any, config = {}) => request.post(apiPrefixb + 'UpdateCashOutlayChart', params, config);

//现金支出科目-删除
export const DeleteCashOutlayChart = (params: any, config = {}) => request.post(apiPrefixb + 'DeleteCashOutlayChart?id=' + params.id, params, config);
