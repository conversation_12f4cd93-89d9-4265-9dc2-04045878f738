<template>
	<div>
		<el-select
			v-model="innerValue"
			:clearable="props.clearable"
			filterable
			remote
			:style="props.cststyle"
			reserve-keyword
			:placeholder="props.placeholder"
			:remote-method="remoteMethod"
			@clear="clear"
			:loading="remoteLoading"
			@change="changeValue"
		>
			<el-option v-for="item in combinedOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
	</div>
</template>

<script setup lang="ts" name="">
import { QueryAllDDUserTop100 } from '/@/api/admin/deptuser';
import { ref, defineProps, onMounted, computed } from 'vue';
interface OptionItem {
	label: string;
	value: string | number;
	name?: string;
	phone?: string;
	deptName?: string;
}
const props = defineProps({
	clearable: {
		type: Boolean,
		default: true,
	},
	cststyle: {
		type: String,
		default: 'width: 100%;',
	},
	placeholder: {
		type: String,
		default: '请输入姓名',
	},
	isDdUserId: {
		type: Boolean,
		default: true,
	},
	// 允许从父组件传入默认值
	defaultValues: {
		type: Array,
		default: () => [], // 默认为空数组
	},
});
const innerValue = defineModel('value');
const innerLabel = defineModel('label');
const phone = defineModel('phone');
const deptName = defineModel('deptName');
import { ElMessage } from 'element-plus';
const remoteLoading = ref(false);
const userOptions = ref<OptionItem[]>([]); // 初始化为一个空数组
const combinedOptions = computed<OptionItem[]>(() => {
	console.log([...props.defaultValues, ...userOptions.value] as OptionItem[], 'userOptions.value');
	return [...props.defaultValues, ...userOptions.value] as OptionItem[];
});
const changeValue = (value: any) => {
	const obj = userOptions.value!.find((item: any) => item.value === value);
	innerLabel.value = value ? userOptions.value!.find((item: any) => item.value === value)?.name : '';
	phone.value = value ? userOptions.value!.find((item: any) => item.value === value)?.phone : '';
	deptName.value = value ? userOptions.value!.find((item: any) => item.value === value)?.deptName : '';
};
const remoteMethod = async (keywords: any) => {
	if (keywords && keywords.length > 20) return ElMessage.error('输入内容过长');
	remoteLoading.value = true;
	if (keywords !== '') {
		let { data, success } = await QueryAllDDUserTop100({ keywords });
		if (data && success) {
			userOptions.value = data?.map((item: any) => {
				return {
					label: item.userName + `(${item.position ? item.position + ',' : ''}${item.empStatusText ? item.empStatusText : ''}${item.jstUserName ? ',' + item.jstUserName : ''} )` + item.deptName,
					value: props.isDdUserId ? item.ddUserId : item.userId,
					name: item.userName,
					phone: item.mobile,
					deptName: item.deptName,
				};
			});
			innerLabel.value = userOptions.value?.find((item: any) => item.value === innerValue.value)?.name;
		}
	}

	remoteLoading.value = false;
};
const clear = () => {
	innerValue.value = null;
	innerLabel.value = '';
	userOptions.value = [];
};
onMounted(async () => {
	if (innerLabel.value) {
		remoteMethod(innerLabel.value);
	}
});
</script>

<style scoped lang="scss"></style>
