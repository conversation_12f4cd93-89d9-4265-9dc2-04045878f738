/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DataItem } from './data-item';
 /**
 * 获取消息模板列表
 *
 * @export
 * @interface MessageTemplateSendInput
 */
export interface MessageTemplateSendInput {

    /**
     * 订阅模板Id
     *
     * @type {string}
     * @memberof MessageTemplateSendInput
     */
    templateId: string;

    /**
     * 接收者的OpenId
     *
     * @type {string}
     * @memberof MessageTemplateSendInput
     */
    toUserOpenId: string;

    /**
     * 模板数据，格式形如 { \"key1\": { \"value\": any }, \"key2\": { \"value\": any } }
     *
     * @type {{ [key: string]: DataItem; }}
     * @memberof MessageTemplateSendInput
     */
    data: { [key: string]: DataItem; };

    /**
     * 模板跳转链接
     *
     * @type {string}
     * @memberof MessageTemplateSendInput
     */
    url?: string | null;

    /**
     * 所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar）
     *
     * @type {string}
     * @memberof MessageTemplateSendInput
     */
    miniProgramPagePath?: string | null;
}
