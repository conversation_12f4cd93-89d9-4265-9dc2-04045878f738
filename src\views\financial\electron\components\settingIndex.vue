<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%" tab-position="left">
				<el-tab-pane label="区域设置" name="first" style="height: 100%">
					<areaSet />
				</el-tab-pane>
				<el-tab-pane label="快递公司设置" name="second" style="height: 100%" lazy>
					<kdCompanySet />
				</el-tab-pane>
				<el-tab-pane label="行别设置" name="third" style="height: 100%" lazy>
					<bankTypeSet />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const areaSet = defineAsyncComponent(() => import('./areaSet.vue'));
const kdCompanySet = defineAsyncComponent(() => import('./kdCompanySet.vue'));
const bankTypeSet = defineAsyncComponent(() => import('./bankTypeSet.vue'));
const activeName = ref('first');
</script>
