import request from '/@/utils/yhrequest';
//凭证基础设置
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Financewh}/Funds/`;

//PageAllFund 查询总资金
export const PageAllFund = (params: any, config = {}) => {
	if (!params.orderBy) {
		delete params.orderBy;
	}
	return request.get(apiPrefix + 'PageAllFund', { params, ...config });
};

//PagePlatformFund
export const PagePlatformFund = (params: any, config = {}) => {
	if (!params.orderBy) {
		delete params.orderBy;
	}
	return request.get(apiPrefix + 'PagePlatformFund', { params, ...config });
};

//PagePlatformCashFlow
export const PagePlatformCashFlow = (params: any, config = {}) => {
	if (!params.orderBy) {
		delete params.orderBy;
	}
	return request.get(apiPrefix + 'PagePlatformCashFlow', { params, ...config });
};

//资金概览 QueryPlatformCharAnalysis
export const QueryPlatformCharAnalysis = (params: any, config = {}) => {
	return request.get(apiPrefix + 'QueryPlatformCharAnalysis', { params, ...config });
};

//查询总现金流 PageCashFlow
export const PageCashFlow = (params: any, config = {}) => {
	if (!params.orderBy) {
		delete params.orderBy;
	}
	return request.get(apiPrefix + 'PageCashFlow', { params, ...config });
};

//查询出纳资金 PageCNFund
export const PageCNFund = (params: any, config = {}) => {
	if (!params.orderBy) {
		delete params.orderBy;
	}
	return request.get(apiPrefix + 'PageCNFund', { params, ...config });
};

//导出总资产
export const ExportAllFund = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportAllFund', params, config);

//导出总现金流
export const ExportCashFlow = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportCashFlow', params, config);

//导出平台资金
export const ExportPlatformFund = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportPlatformFund', params, config);

//导出出纳资金
export const ExportCNFund = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportCNFund', params, config);

export const Compute = (params: any, config = {}) => request.post(apiPrefix + 'Compute', params, config);
