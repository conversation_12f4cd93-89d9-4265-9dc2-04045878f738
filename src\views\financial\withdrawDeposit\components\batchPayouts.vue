<template>
	<div style="width: 100%; height: 100%; padding: 10px">
		<div v-if="batchType == 1">
			<dataRange v-model:date="batchPayoutsInfo.dateTime" :clearable="false" type="date" class="publicCss" placeholder="提现日期" style="width: 230px; margin-bottom: 10px" />
			<el-input v-model="textarea" :rows="20" type="textarea" @input="changeTextArea" placeholder="请输入店铺名,多个店铺使用换行隔开" />
		</div>
		<el-alert v-if="batchType == 2" :title="messageConfig" type="error" :closable="false" />
		<vxetable
			v-if="batchType == 2"
			ref="table"
			id="************"
			:tableCols="tableCols"
			:isNeedQueryApi="false"
			:data="batchPayoutsList"
			:remote="false"
			showsummary
			style="height: 600px"
			:isNeedPager="false"
		>
			<template #account="{ row }">
				<el-select v-model="row.account" placeholder="提现账户名" class="btnGroup" clearable filterable @change="accountChnene(row, 1)">
					<el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</template>
			<template #aliPayAccount="{ row }">
				<el-select v-model="row.aliPayAccount" placeholder="提现账户名" class="btnGroup" clearable filterable @change="accountChnene(row, 2)">
					<el-option v-for="item in withdrawAlipay" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</template>
			<template #shopBalance="{ row }">
				<el-input-number v-model="row.shopBalance" :min="0" :max="***********" :precision="2" :controls="false" />
			</template>
			<template #withDrawAmount="{ row }">
				<el-input-number v-model="row.withDrawAmount" :min="0" :max="***********" :precision="2" :controls="false" />
			</template>
			<template #occurrenceTime="{ row }">
				<el-date-picker v-model="row.occurrenceTime" type="datetime" placeholder="请选择时间" :clearable="false" style="width: 150px" value-format="YYYY-MM-DD HH:mm:ss" />
			</template>
		</vxetable>
		<div style="display: flex; justify-content: center; margin-top: 10px">
			<el-button @click="emit('close')">取消</el-button>
			<el-button type="primary" @click="batchPayoutsSubmit"> 确定 </el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, defineEmits, onMounted } from 'vue';
import { BatchGetWithDrawShopData, BatchUpdateWithDrawShopData } from '/@/api/cwManager/withDrawInfo';
import { QueryOnlineBankSet } from '/@/api/cwManager/cashierSet';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const batchPayoutsInfo = ref<{
	shopName: string;
	dateTime: string;
}>({
	shopName: '',
	dateTime: dayjs().format('YYYY-MM-DD'),
});
const nameList = ref<any[]>([]);
const emit = defineEmits(['changeWidth', 'close']);
const batchPayoutsList = ref<any[]>([]);
const batchPayoutsVisible = ref<boolean>(false);
const messageConfig = ref<string>('');
const batchType = ref<number>(1);
const textarea = ref<string>('');
const withdrawAlipay = ref<Public.options[]>([]);
const table = ref<any>();
const changeTextArea = () => {
	let res = textarea.value?.split('\n').filter((item) => item != '');
	if (res?.length > 100) {
		res.splice(100);
		window.$message.warning(`最多支持100条数据,已自动截断至第100条,店铺名为${res[99]}`);
		textarea.value = res.join('\n');
	}
	batchPayoutsInfo.value.shopName = res.join(',');
};

const accountChnene = (e: any, val: number) => {
	if (val == 1) {
		e.aliPayAccount = '';
	} else {
		e.account = '';
	}
};
const batchPayoutsSubmit = async () => {
	if (!textarea.value) return window.$message.error('请输入店铺名');
	if (batchType.value == 1) {
		try {
			const { data, success } = await BatchGetWithDrawShopData(batchPayoutsInfo.value);
			if (!success) return;
			messageConfig.value = data.extData.shopName;
			data.list.forEach((item: any) => {
				item.occurrenceTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
			});
			batchPayoutsList.value = data.list;
			batchType.value = 2;
			emit('changeWidth');
		} catch (error) {
			console.log(error);
		}
	} else if (batchType.value == 2) {
		try {
			const { success } = await BatchUpdateWithDrawShopData(batchPayoutsList.value);
			if (!success) return;
			window.$message.success('操作成功');
			emit('close');
		} catch (error) {
			console.log(error);
		}
	}
};

const handleDelete = (row: any) => {
	ElMessageBox.confirm('此操作将删除这条数据,是否继续?', '提示消息', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			batchPayoutsList.value = batchPayoutsList.value.filter((item) => item.id != row.id);
			table.value.changeTableData(batchPayoutsList.value);
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: 'Delete canceled',
			});
		});
};
const getAllDept = async () => {
	const { data: data1, success: success1 } = await QueryOnlineBankSet({ currentPage: 1, pageSize: ******** });
	if (!success1) return;
	withdrawAlipay.value = data1.list.filter((item: any) => item.bankType.trim() === '支付宝').map((item: any) => ({ label: item.accountName, value: item.account }));
	nameList.value = data1.list
		.filter((item: any) => {
			if (!item.cardType) return false;
			const cardTypes = item.cardType.split(',');
			return cardTypes.includes('提现网银') || cardTypes.includes('其他对公网银的数据');
		})
		.map((item: any) => ({ label: item.accountName, value: item.account }));
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'withDrawTime', title: '日期', formatter: 'formatDate', width: '90' },
	{ sortable: true, field: 'platform', title: '平台', formatter: 'formatPlatform', width: '90' },
	{ sortable: true, field: 'shopName', title: '店铺名' },
	{ sortable: true, field: 'platformShopId', title: '平台店铺ID' },
	{ sortable: true, field: 'account', title: '提现账户名(别名)' },
	{ sortable: true, field: 'aliPayAccount', title: '支付宝账号(别名)' },
	{ sortable: true, field: 'shopBalance', title: '店铺余额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'withDrawAmount', title: '提现金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'occurrenceTime', title: '操作时间', width: '200' },
	{
		title: '操作',
		align: 'center',
		width: '90',
		type: 'btnList',
		minWidth: '90',
		direction: 'column',
		btnList: [{ title: '删除', handle: handleDelete }],
		fixed: 'right',
	},
]);

onMounted(() => {
	getAllDept();
});
</script>
