<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.orderNo" placeholder="订单编号" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
				<el-select v-model="query.companyOrPersonal" placeholder="请选择公司/个人" class="publicCss" clearable filterable>
					<el-option :key="'公司'" label="公司" :value="'公司'" />
					<el-option :key="'个人'" label="个人" :value="'个人'" />
				</el-select>
				<el-input v-model.trim="query.invoiceHeader" placeholder="发票抬头" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.taxID" placeholder="税号" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.openingBank" placeholder="开户行" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.accountNumber" placeholder="账号" maxlength="50" clearable class="publicCss" />
				<el-select v-model="query.dataType" placeholder="类型" class="publicCss" clearable filterable>
					<el-option key="普通发票" label="普通发票" value="普通发票" />
					<el-option key="增值税专用发票" label="增值税专用发票" value="增值税专用发票" />
				</el-select>
				<el-input v-model.trim="query.registrantName" placeholder="登记人名称" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.invoicingCompany" placeholder="开票公司" maxlength="50" clearable class="publicCss" />
				<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable filterable>
					<el-option :key="'已登记待审核'" label="已登记待审核" :value="'已登记待审核'" />
					<el-option :key="'财务审核驳回'" label="财务审核驳回" :value="'财务审核驳回'" />
					<el-option :key="'已通过待开票'" label="已通过待开票" :value="'已通过待开票'" />
					<el-option :key="'已通过开票成功'" label="已通过开票成功" :value="'已通过开票成功'" />
					<el-option :key="'已通过开票失败'" label="已通过开票失败" :value="'已通过开票失败'" />
					<el-option :key="'待红冲'" label="待红冲" :value="'待红冲'" />
					<el-option :key="'已红冲'" label="已红冲" :value="'已红冲'" />
				</el-select>
				<el-select v-model="query.invoiceType" placeholder="开票类型" class="publicCss" clearable filterable>
					<el-option key="常规发票" label="常规发票" value="常规发票" />
					<el-option key="红冲发票" label="红冲发票" value="红冲发票" />
				</el-select>
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" startPlaceholder="登记开始时间" endPlaceholder="登记结束时间" style="width: 225px" />
				<dataRange v-model:startDate="query.auditStartTime" v-model:endDate="query.auditEndTime" class="publicCss" startPlaceholder="审核开始时间" endPlaceholder="审核结束时间" style="width: 225px" />
				<el-select v-model="query.isRepeatOrder" placeholder="是否重复登记" class="publicCss" clearable filterable>
					<el-option key="是" label="是" value="是" />
					<el-option key="否" label="否" value="否" />
				</el-select>
				<el-select v-model="query.isUrgent" placeholder="是否加急" class="publicCss" clearable filterable>
					<el-option key="是" label="是" value="是" />
					<el-option key="否" label="否" value="否" />
				</el-select>
				<dataRange
					v-model:startDate="query.invoiceStartDate"
					v-model:endDate="query.invoiceEndDate"
					class="publicCss"
					startPlaceholder="开票开始时间"
					endPlaceholder="开票结束时间"
					style="width: 225px"
				/>
				<div class="pb5">
					<el-button @click="getList" type="primary">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				showsummary
				ref="table"
				id="levelSubjectsOne202412221701"
				:tableCols="tableCols"
				:pageSize="50"
				:query="query"
				:isAsc="false"
				:queryApi="GetAfterSalesInvoiceManageFinancialPage"
				v-loading="pageLoading"
				:export-api="ExportAfterSalesInvoiceManageFinancial"
				:asyncExport="{ title: '售后发票管理', isAsync: false }"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				isNeedCheckBox
				@select="parentCheckboxChange"
			>
				<template #orderNo="{ row }">
					<div v-if="!row.verify" style="width: 100%; text-align: left">{{ row.orderNo }}</div>
					<div v-else style="width: 100%; text-align: left; cursor: pointer; color: #64c5b1" @click="openOrderDetail(row)">
						{{ row.orderNo }}
					</div>
				</template>
				<template #exportFileUrl="{ row }">
					<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
						<el-image
							v-if="row.exportFileUrl"
							:src="'https://nanc.yunhanmy.com:10010/media/video/20250328/1905557996632793089.jpg'"
							style="width: 30px; height: 30px; cursor: pointer"
							@click="downloadAttachment(row, 1)"
						/>
					</div>
				</template>
				<template #invoiceFileUrl="{ row }">
					<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center">
						<el-image
							v-if="row.invoiceFileUrl"
							:src="'https://nanc.yunhanmy.com:10010/media/video/20250415/1912067771609825280.png'"
							style="width: 30px; height: 30px; cursor: pointer"
							@click="downloadAttachment(row, 2)"
						/>
					</div>
				</template>
				<template #toolbar_buttons>
					<el-button type="primary" @click="batchReject">批量驳回</el-button>
					<el-button type="primary" @click="batchAudit">批量审核</el-button>
				</template>
			</vxetable>

			<el-dialog v-model="examineDialog" title="审核" width="25%" draggable overflow style="margin-top: -20vh !important" :close-on-click-modal="false">
				<el-form ref="formRef" :model="examineForm" :rules="rules" label-width="120px" class="demo-examineForm" status-icon style="height: 200px">
					<el-form-item label="审核状态" prop="auditStatus">
						<el-select v-model.number="examineForm.auditStatus" placeholder="审核" class="form_Css" clearable @change="changeAuditStatus">
							<el-option :key="1" label="通过" :value="1" />
							<el-option :key="-1" label="驳回" :value="-1" />
						</el-select>
					</el-form-item>
					<el-form-item label="开票公司" :prop="examineForm.auditStatus === 1 ? 'invoicingCompanyId' : undefined" v-if="examineForm.auditStatus === 1">
						<el-select
							v-model.trim="examineForm.invoicingCompanyId"
							filterable
							clearable
							remote
							reserve-keyword
							placeholder="请输入模糊输入并选择开票公司"
							:remote-method="remoteMethod"
							:loading="remoteLoading"
							class="form_Css"
							@change="changeCorporation($event, 1)"
						>
							<el-option v-for="item in corporationList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="红冲编号" v-if="examineForm.auditStatus === 1">
						<div class="form_Css">{{ examineForm.redChongId }}</div>
						<div class="flex">
							<el-button type="primary" :disabled="!examineForm.redChongId" @click="examineForm.redChongId = ''">清除</el-button>
							<el-button type="primary" @click="getRedChongId">获取红冲编号</el-button>
						</div>
					</el-form-item>
					<el-form-item label="审批备注" :prop="examineForm.auditStatus === -1 ? 'auditRemark' : undefined" v-if="examineForm.auditStatus === -1">
						<el-input
							v-model.trim="examineForm.auditRemark"
							placeholder="请输入审批备注"
							class="form_Css"
							clearable
							maxlength="300"
							:autosize="{ minRows: 6, maxRows: 6 }"
							type="textarea"
							show-word-limit
						/>
					</el-form-item>
				</el-form>
				<el-dialog v-model="innerVisible" width="70%" title="红冲编号" append-to-body draggable overflow>
					<div style="display: flex; align-items: center; justify-content: space-between">
						<div style="display: flex; align-items: center">
							<el-input v-model.trim="innerQuery.orderNo" placeholder="订单编号" maxlength="50" clearable style="width: 200px; margin-right: 10px" />
							<el-button type="primary" @click="getInnerList">查询</el-button>
						</div>
						<div style="color: red">请选择一个红冲编号</div>
						<div style="width: 200px"></div>
					</div>
					<div style="height: 500px">
						<vxetable
							v-if="innerVisible"
							ref="innertable"
							id="inner202503271522"
							:tableCols="innerTableCols"
							:pageSize="50"
							:query="innerQuery"
							:isAsc="false"
							:queryApi="GetOpenSuccessAfterSalesInvoiceManageFinancialPage"
							v-loading="innerpageLoading"
							isNeedCheckBox
							@select="checkboxChange"
							:isNeedExpand="true"
						>
							<template #expandCols="{ row }">
								<div style="padding-left: 153px; display: block">
									<vxe-table
										class="mytable-style"
										border
										show-overflow
										keep-source
										:height="getTableHeight(row.orderDtls)"
										ref="tableRef"
										:data="row.orderDtls"
										:cell-style="cellStyle"
										:header-cell-style="{ 'text-align': 'center', background: '#D3D3D3' }"
									>
										<vxe-column field="orderNo" title="订单编号" width="120" align="center"></vxe-column>
										<vxe-column field="goodsName" title="商品名称" width="120" align="center"></vxe-column>
										<vxe-column field="goodsCount" title="商品数量" width="120" align="center"></vxe-column>
										<vxe-column field="spec" title="规格" width="120" align="center"></vxe-column>
										<vxe-column field="unit" title="单位" width="120" align="center"></vxe-column>
										<vxe-column field="actualInvoicedAmount" title="实际开票金额" width="120" align="right" :formatter="formatAmount"></vxe-column>
									</vxe-table>
								</div>
							</template>
						</vxetable>
					</div>
					<div class="flex" style="margin-top: 25px">
						<el-button @click="innerVisible = false">取消</el-button>
						<el-button type="primary" @click="onDefiniteSaveMethod">确定</el-button>
					</div>
				</el-dialog>
				<template #footer>
					<el-button @click="examineDialog = false">取消</el-button>
					<el-button type="primary" @click="onSaveMethod">确定</el-button>
				</template>
			</el-dialog>

			<el-dialog v-model="detailDialog" width="50%" title="订单详情" append-to-body draggable overflow style="margin-top: -15vh !important">
				<div>
					<el-table :data="orderDtls" style="width: 100%" border height="400">
						<el-table-column type="index" label="序号" width="55" align="center" />
						<el-table-column prop="orderNo" label="订单编号" width="150" align="center" show-overflow-tooltip />
						<el-table-column prop="goodsName" label="商品名称" width="auto" align="center" show-overflow-tooltip />
						<el-table-column prop="goodsCount" label="商品数量" width="100" align="center" show-overflow-tooltip />
						<el-table-column prop="spec" label="规格" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="unit" label="单位" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="actualInvoicedAmount" label="实际开票金额" width="120" align="center" show-overflow-tooltip>
							<template #default="scope">
								<span>{{ formatters.fmtAmt2(scope.row.actualInvoicedAmount) }}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-dialog>

			<el-dialog v-model="turnDown.visible" width="40%" title="批量驳回" append-to-body draggable overflow style="margin-top: -15vh !important">
				<div>
					<el-form :model="turnDown" ref="turnDownRef" :rules="turnrules" label-width="120px" class="demo-examineForm" status-icon style="height: 200px">
						<el-form-item label="驳回原因" prop="auditRemarks">
							<el-input
								v-model.trim="turnDown.auditRemarks"
								placeholder="请输入驳回原因"
								class="form_Css"
								clearable
								maxlength="300"
								:autosize="{ minRows: 8, maxRows: 8 }"
								type="textarea"
								show-word-limit
							/>
						</el-form-item>
					</el-form>
				</div>
				<template #footer>
					<el-button @click="turnDown.visible = false">取消</el-button>
					<el-button type="primary" @click="onBatchSaveMethod">确定</el-button>
				</template>
			</el-dialog>

			<el-dialog v-model="examine.visible" width="30%" title="批量审核" append-to-body draggable overflow style="margin-top: -15vh !important">
				<el-form :model="examine" ref="examineRef" :rules="examinerules" label-width="120px" class="demo-examineForm" status-icon style="height: 100px">
					<el-form-item label="开票公司" prop="invoicingCompanyId">
						<el-select
							v-model.trim="examine.invoicingCompanyId"
							filterable
							clearable
							remote
							reserve-keyword
							placeholder="请输入模糊输入并选择开票公司"
							:remote-method="remoteMethod"
							style="width: 100%"
							@change="changeCorporation($event, 2)"
						>
							<el-option v-for="item in corporationList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-form>
				<template #footer>
					<el-button @click="examine.visible = false">取消</el-button>
					<el-button type="primary" @click="onExamineSaveMethod">确定</el-button>
				</template>
			</el-dialog>

			<el-dialog v-model="invalidated.visible" width="30%" title="作废" append-to-body draggable overflow style="margin-top: -5vh !important">
				<el-form :model="invalidated" ref="invalidatedRef" :rules="invalidatedrules" label-width="80px" class="demo-examineForm" status-icon>
					<el-form-item label="作废原因" prop="remark">
						<el-input v-model="invalidated.remark" style="width: 100%" :autosize="{ minRows: 6, maxRows: 6 }" type="textarea" placeholder="请输入作废原因" maxlength="50" show-word-limit />
					</el-form-item>
				</el-form>
				<template #footer>
					<el-button @click="invalidated.visible = false">取消</el-button>
					<el-button type="primary" @click="onInvalidatedSaveMethod">确定</el-button>
				</template>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, reactive, onMounted, defineAsyncComponent, defineEmits, nextTick } from 'vue';
import {
	GetAfterSalesInvoiceManageFinancialPage,
	AuditAfterSalesInvoiceManage,
	GetCompanyInvoicingInfoPage,
	ClearAfterSalesInvoiceManageExportFile,
	ExportAfterSalesInvoiceManageFinancial,
	BatchRejectAfterSalesInvoiceManage,
	BatchAuditAfterSalesInvoiceManage,
	NullifyAfterSalesInvoiceManage,
	GetOpenSuccessAfterSalesInvoiceManageFinancialPage,
} from '/@/api/cwManager/afterSalesInvoiceManage';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
import { formatters } from '/@/utils/vxetableFormats';
import { ElMessage, ElMessageBox, FormRules, FormInstance } from 'element-plus';
const pageLoading = ref(false);
const examineDialog = ref(false);
const innerVisible = ref(false);
const formRef = ref();
const table = ref();
const innertable = ref();
const innerpageLoading = ref(false);
interface CheckboxItem {
	redChongId: string;
	[key: string]: any;
}
const checkboxList = ref<CheckboxItem[]>([]);
const parentCheckboxList = ref([]);
const corporationList = ref<Public.options[]>([]);
const remoteLoading = ref(false);
const detailDialog = ref(false);
const orderDtls = ref([]);
const shopCodes = ref<string[]>([]);
const query = ref({
	orderNo: '',
	goodsName: '',
	companyOrPersonal: '',
	invoiceHeader: '',
	taxID: '',
	openingBank: '',
	accountNumber: '',
	dataType: '',
	registrantName: '',
	invoicingCompany: '',
	status: '',
	startDate: '',
	endDate: '',
	invoiceType: '',
	auditStartTime: '',
	auditEndTime: '',
	isRepeatOrder: '',
	isUrgent: '',
	invoiceStartDate: '',
	invoiceEndDate: '',
});

const invalidated = ref({
	visible: false,
	id: '',
	remark: '',
});
const invalidatedRef = ref();
const invalidatedrules = ref({
	remark: [{ required: true, message: '请输入作废原因', trigger: 'blur' }],
});

const turnDown = ref({
	visible: false,
	auditRemarks: '',
});
const turnDownRef = ref();
const turnrules = ref({
	auditRemarks: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }],
});

const examine = ref({
	visible: false,
	invoicingCompanyId: '',
	invoicingCompanyName: '',
});
const examineRef = ref();
const examinerules = ref({
	invoicingCompanyId: [{ required: true, message: '请输入开票公司', trigger: 'blur' }],
});

const parentCheckboxChange = (val: any) => {
	parentCheckboxList.value = val;
};

const batchReject = async () => {
	if (parentCheckboxList.value.length) {
		turnDown.value.auditRemarks = '';
		turnDown.value.visible = true;
	} else {
		ElMessage.warning('请选择需要驳回的订单');
	}
};

const onBatchSaveMethod = async () => {
	turnDownRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const params = {
				auditRemarks: turnDown.value.auditRemarks,
				ids: parentCheckboxList.value.map((item: any) => item.id),
			};
			const { success } = await BatchRejectAfterSalesInvoiceManage(params);
			if (success) {
				ElMessage.success('驳回成功');
				parentCheckboxList.value = [];
				turnDown.value.visible = false;
				table.value.clearSelection();
				getList();
			}
		}
	});
};

const batchAudit = () => {
	if (parentCheckboxList.value.length) {
		examine.value.visible = true;
		examine.value.invoicingCompanyId = '';
		shopCodes.value = Array.from(new Set(parentCheckboxList.value.map((item: any) => item.shopCode)));
	} else {
		ElMessage.warning('请选择需要审核的订单');
	}
};

const onExamineSaveMethod = () => {
	examineRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const params = {
				invoicingCompanyId: examine.value.invoicingCompanyId,
				invoicingCompanyName: examine.value.invoicingCompanyName,
				ids: parentCheckboxList.value.map((item: any) => item.id),
			};
			const { success } = await BatchAuditAfterSalesInvoiceManage(params);
			if (success) {
				ElMessage.success('审核成功');
				parentCheckboxList.value = [];
				examine.value.visible = false;
				table.value.clearSelection();
				getList();
			}
		}
	});
};

// 千分位格式化函数
const formatAmount = ({ cellValue }: { cellValue: number }) => {
	if (!cellValue && cellValue != 0) return;
	return cellValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const cellStyle = () => {
	return { 'background-color': '#D3D3D3' };
};

const getTableHeight = (data: any) => {
	if (!data) {
		return 'auto';
	}
	const rowHeight = 36; // 每行的高度
	const headerHeight = 36; // 表头高度
	const extraSpace = 5; // 额外的间距
	let height = data.length * rowHeight + headerHeight + extraSpace;
	const minHeight = 10; // 最小高度
	if (height < minHeight) {
		height = minHeight;
	}
	return String(height);
};

const innerQuery = ref({
	orderNo: '',
	status: '已通过开票成功',
});

const examineForm = ref({
	auditRemark: '',
	id: '',
	invoicingCompanyId: '',
	redChongId: '',
	auditStatus: '' as number | '',
	invoicingCompanyName: '',
});

const rules = ref({
	auditStatus: [{ required: true, message: '请选择审核状态', trigger: 'blur' }],
	invoicingCompanyId: [{ required: true, message: '请输入开票公司', trigger: 'blur' }],
	redChongId: [{ required: true, message: '请输入红冲编号', trigger: 'blur' }],
	auditRemark: [{ required: true, message: '请输入审批备注', trigger: 'blur' }],
});

const getInnerList = () => {
	innertable.value.getList();
	nextTick(() => {
		innertable.value.handleClickExpend();
	});
};

const openOrderDetail = (row: any) => {
	orderDtls.value = row.orderDtls ? row.orderDtls : [];
	detailDialog.value = true;
};

// 选择审核状态
const changeAuditStatus = (val: any) => {
	examineForm.value.redChongId = '';
	examineForm.value.invoicingCompanyId = '';
	examineForm.value.invoicingCompanyName = '';
	examineForm.value.auditRemark = '';
};

// 选择红冲编号
const checkboxChange = (val: any) => {
	checkboxList.value = val;
};

// 确定
const onDefiniteSaveMethod = () => {
	if (!checkboxList.value.length) {
		ElMessage.warning('请选择红冲编号');
		return;
	} else if (checkboxList.value.length > 1) {
		ElMessage.warning('请选择一个红冲编号');
		return;
	} else {
		innerVisible.value = false;
		examineForm.value.redChongId = checkboxList.value[0].id;
	}
};

// 获取红冲编号
const getRedChongId = async () => {
	checkboxList.value = [];
	innerVisible.value = true;
	innerQuery.value.orderNo = '';
};

const onInvalidatedSaveMethod = async () => {
	invalidatedRef.value.validate(async (valid: boolean) => {
		if (valid) {
			ElMessageBox.confirm('确定作废该数据吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(async () => {
				const params = {
					id: invalidated.value.id,
					remark: invalidated.value.remark,
				};
				const { success } = await NullifyAfterSalesInvoiceManage(params);
				if (success) {
					ElMessage.success('作废成功');
					invalidated.value.visible = false;
					getList();
				}
			});
		}
	});
};

const handleVoid = async (row: any) => {
	invalidated.value.visible = true;
	invalidated.value.id = row.id;
	invalidated.value.remark = '';
	nextTick(() => {
		invalidatedRef.value.resetFields();
		invalidatedRef.value.clearValidate();
	});
};

// 审核
const examineProps = (row: any) => {
	examineDialog.value = true;
	nextTick(() => {
		formRef.value.resetFields();
		formRef.value.clearValidate();
		examineForm.value = {
			auditRemark: '', //审批备注
			id: row.id, //id
			invoicingCompanyId: '', //开票公司id
			invoicingCompanyName: '', //开票公司名称
			redChongId: '', //红冲编号
			auditStatus: 1, //审核状态
		};
		shopCodes.value = [row.shopCode];
	});
};

// 保存
const onSaveMethod = () => {
	formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			if (examineForm.value.auditStatus === 1) {
				if (!examineForm.value.invoicingCompanyId) {
					ElMessage.warning('请选择开票公司');
					return;
				}
			} else if (examineForm.value.auditStatus === -1) {
				if (!examineForm.value.auditRemark) {
					ElMessage.warning('请输入审批备注');
					return;
				}
			}
			const { success } = await AuditAfterSalesInvoiceManage(examineForm.value);
			if (success) {
				examineDialog.value = false;
				getList();
				ElMessage.success('保存成功');
			}
		}
	});
};

// 选择开票公司
const changeCorporation = (val: any, type: number) => {
	corporationList.value.forEach((item: any) => {
		if (item.value === val) {
			if (type === 1) {
				examineForm.value.invoicingCompanyName = item.label;
			} else if (type === 2) {
				examine.value.invoicingCompanyName = item.label;
			}
		}
	});
};

// 获取开票公司
const remoteMethod = async (account: string) => {
	remoteLoading.value = true;
	const { data, success } = await GetCompanyInvoicingInfoPage({ company: account, pageSize: 10000, currentPage: 1, shopCodes: shopCodes.value });
	if (success) {
		corporationList.value = data.list.map((item: any) => {
			return {
				label: item.company,
				value: item.id,
			};
		});
		remoteLoading.value = false;
	} else {
		corporationList.value = [];
		remoteLoading.value = false;
	}
};

// 清空附件
const clearAttachment = async (row: any) => {
	ElMessageBox.confirm('确定清空附件吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { success } = await ClearAfterSalesInvoiceManageExportFile({ id: row.id });
		if (success) {
			ElMessage.success('清空成功');
			getList();
		}
	});
};

// 下载附件
const downloadAttachment = (row: any, type: number) => {
	if (row.exportFileUrl && type === 1) {
		window.open(row.exportFileUrl, '_blank');
	} else if (row.invoiceFileUrl && type === 2) {
		window.open(row.invoiceFileUrl, '_blank');
	}
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.getList();
};

const disposeProps = async (data: any, callback: Function) => {
	data.data.list.forEach((item: any) => {
		if (!item.orderDtls?.length) return;
		const firstOrder = item.orderDtls[0];
		item.verify = item.orderDtls.length > 1;
		item.orderNo = firstOrder.orderNo;
		item.goodsName = firstOrder.goodsName;
		item.goodsCount = firstOrder.goodsCount;
		item.actualInvoicedAmount = firstOrder.actualInvoicedAmount;
		item.spec = firstOrder.spec;
		item.unit = firstOrder.unit;
	});
	callback(data);
};

const innerTableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'orderNo', title: '订单编号', width: 'auto', align: 'left' },
	{ field: 'goodsName', title: '商品名称', width: '100' },
	{ field: 'goodsCount', title: '商品数量', width: '100', align: 'right' },
	{ sortable: true, field: 'actualInvoicedAmount', title: '实际开票金额', width: '115', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'companyOrPersonal', title: '公司/个人', width: '95' },
	{ sortable: true, field: 'invoiceHeader', title: '发票抬头', width: '100' },
	{ sortable: true, field: 'invoicingCompany', title: '开票公司', width: '130' },
	{ sortable: true, field: 'taxID', title: '税号', width: '130' },
	{ sortable: true, field: 'registrantName', title: '登记人', width: '100' },
	{ sortable: true, field: 'drawerName', title: '开票人', width: '100' },
]);

const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'orderNo', title: '订单编号', width: '100' },
	{ field: 'goodsName', title: '商品名称', width: '100' },
	{ field: 'goodsCount', title: '商品数量', width: '100', align: 'right' },
	{ field: 'spec', title: '规格', width: '100' },
	{ field: 'unit', title: '单位', width: '100' },
	{ field: 'actualInvoicedAmount', title: '实际开票金额', width: '115', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'companyOrPersonal', title: '公司/个人', width: '100' },
	{ sortable: true, field: 'shopName', title: '店铺名称', width: '160' },
	{ sortable: true, field: 'shopCode', title: '店铺编码', width: '100' },
	{ sortable: true, field: 'invoiceHeader', title: '发票抬头', width: '100' },
	{ sortable: true, field: 'taxID', title: '税号', width: '100' },
	{ sortable: true, field: 'openingBank', title: '开户行', width: '100' },
	{ sortable: true, field: 'accountNumber', title: '账号', width: '100' },
	{ sortable: true, field: 'address', title: '地址', width: '100' },
	{ sortable: true, field: 'mobile', title: '电话', width: '100' },
	{ sortable: true, field: 'customerEmail', title: '客户邮箱', width: '100' },
	{ sortable: true, field: 'dataType', title: '类型', width: '100' },
	{ sortable: true, field: 'remark', title: '备注', width: '100' },
	// { sortable: true, field: 'registrantId', title: '登记人Id', width: '100' },
	{ sortable: true, field: 'registrantName', title: '登记人', width: '100' },
	// { sortable: true, field: 'invoicingCompanyId', title: '开票公司Id', width: '100' },
	{ sortable: true, field: 'invoicingCompany', title: '开票公司', width: '100' },
	{ sortable: true, field: 'status', title: '状态', width: '110' },
	{ sortable: true, field: 'invoiceType', title: '开票类型', width: '100' },
	{ sortable: true, field: 'auditRemarks', title: '审核备注', width: '100' },
	{ sortable: true, field: 'auditTime', title: '审核时间', width: '130' },
	{ sortable: true, field: 'invoiceFileUrl', title: '发票文件', width: '100' },
	{ sortable: true, field: 'billNumber', title: '票据号', width: '100' },
	{ sortable: true, field: 'redChongId', title: '红冲编号', width: '100' },
	// { sortable: true, field: 'drawerId', title: '开票人Id', width: '100' },
	{ sortable: true, field: 'drawerName', title: '开票人', width: '100' },
	{ sortable: true, field: 'exportFileUrl', title: '附件', width: '65' },
	{ sortable: true, field: 'isRepeatOrder', title: '是否重复登记', width: '100', color: (row: any) => (row.isRepeatOrder == '是' ? '#f56c6c' : '#000000') },
	{ sortable: true, field: 'isUrgent', title: '是否加急', width: '100' },
	{ sortable: true, field: 'invoiceDate', title: '开票时间', width: '130' },
	{ sortable: true, field: 'cancelTime', title: '作废时间', width: '130' },
	{ sortable: true, field: 'createdTime', title: '登记时间', width: '130' },
	{
		title: '操作',
		align: 'center',
		type: 'btnList',
		width: '110',
		fixed: 'right',
		field: '20250608093741',
		btnList: [
			{ title: '审核', handle: examineProps, isDisabled: (row) => row.status !== '已登记待审核' },
			{ title: '清空', handle: clearAttachment, isDisabled: (row) => (row.exportFileUrl && row.status == '已通过待开票' ? false : true) },
			{ title: '作废', handle: handleVoid, isDisabled: (row) => !['已登记待审核', '已通过待开票'].includes(row.status) },
		],
	},
]);

onMounted(() => {});
</script>

<style scoped lang="scss">
.btnGroup {
	width: 80%;
}

::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}

	.el-input__wrapper {
		padding-left: 7px;
	}
}

.form_Css {
	width: 80%;
}

.flex {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
