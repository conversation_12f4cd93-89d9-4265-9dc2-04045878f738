import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_CwManage}/TaxData/`;

//税费数据-查询
export const QueryTaxData = (params: any, config = {}) => request.post(apiPrefix + 'QueryTaxData ', params, config);

//税费数据-编辑
export const UpdateTaxData = (params: any, config = {}) => request.post(apiPrefix + 'UpdateTaxData', params, config);

//税费数据-提交
export const CommitTaxData = (params: any, config = {}) => request.post(apiPrefix + 'CommitTaxData', params, config);

//税费数据-合并提交
export const MergeCommitTaxData = (params: any, config = {}) => request.post(apiPrefix + 'MergeCommitTaxData', params, config);

//税费数据-导出
export const ExportTaxData = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportTaxData', params, config);

//税费数据-同步
export const GetTaxDataBusinessId = () => request.get(apiPrefix + 'GetTaxDataBusinessId');

//税费数据-修改状态
export const BatchUpdateTaxDataCommitStatus = (params: any, config = {}) => request.post(apiPrefix + 'BatchUpdateTaxDataCommitStatus', params, config);