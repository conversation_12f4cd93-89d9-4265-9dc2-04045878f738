/* tslint:disable */
/* eslint-disable */
/**
 * GoView
 * GoView 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 2.2.8
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../../base';
import { GoViewLoginInput } from '../models';
import { GoViewResultGoViewLoginOutput } from '../models';
/**
 * SysApi - axios parameter creator
 * @export
 */
export const SysApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary GoView 登录 🔖
         * @param {GoViewLoginInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiGoviewSysLoginPost: async (body?: GoViewLoginInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/goview/sys/login`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary GoView 退出 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiGoviewSysLogoutGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/goview/sys/logout`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysApi - functional programming interface
 * @export
 */
export const SysApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary GoView 登录 🔖
         * @param {GoViewLoginInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiGoviewSysLoginPost(body?: GoViewLoginInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<GoViewResultGoViewLoginOutput>>> {
            const localVarAxiosArgs = await SysApiAxiosParamCreator(configuration).apiGoviewSysLoginPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary GoView 退出 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiGoviewSysLogoutGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysApiAxiosParamCreator(configuration).apiGoviewSysLogoutGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysApi - factory interface
 * @export
 */
export const SysApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary GoView 登录 🔖
         * @param {GoViewLoginInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiGoviewSysLoginPost(body?: GoViewLoginInput, options?: AxiosRequestConfig): Promise<AxiosResponse<GoViewResultGoViewLoginOutput>> {
            return SysApiFp(configuration).apiGoviewSysLoginPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary GoView 退出 🔖
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiGoviewSysLogoutGet(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysApiFp(configuration).apiGoviewSysLogoutGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysApi - object-oriented interface
 * @export
 * @class SysApi
 * @extends {BaseAPI}
 */
export class SysApi extends BaseAPI {
    /**
     * 
     * @summary GoView 登录 🔖
     * @param {GoViewLoginInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysApi
     */
    public async apiGoviewSysLoginPost(body?: GoViewLoginInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<GoViewResultGoViewLoginOutput>> {
        return SysApiFp(this.configuration).apiGoviewSysLoginPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary GoView 退出 🔖
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysApi
     */
    public async apiGoviewSysLogoutGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysApiFp(this.configuration).apiGoviewSysLogoutGet(options).then((request) => request(this.axios, this.basePath));
    }
}
