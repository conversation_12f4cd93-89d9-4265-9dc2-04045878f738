<template>
	<Container style="width: 100%; height: 100%" v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-radio-group v-model="radio1" class="publicCss" style="width: 300px" @change="changeRadio">
					<el-radio-button label="近10天" :value="10" />
					<el-radio-button label="近一个月" :value="30" />
					<el-radio-button label="近半年" :value="180" />
				</el-radio-group>
			</div>
		</template>
		<template #content>
			<stepFallsCharts v-if="!loading" showX showY :analysisData="analysisData" ref="sumChart" :thisStyle="{
				width: '100%',
				height: '500px',
				'box-sizing': 'border-box',
				'line-height': '500px',
			}" />
			<div class="bottomChart">
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData1" @click="clickChart(analysisData1)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData2" @click="clickChart(analysisData2)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData3" @click="clickChart(analysisData3)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData4" @click="clickChart(analysisData4)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData5" @click="clickChart(analysisData5)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData6" @click="clickChart(analysisData6)" v-if="!loading" />
				<stepFallsCharts class="bottomChart_item" :thisStyle="{
					width: '100%',
					height: '200px',
					'box-sizing': 'border-box',
					'line-height': '200px',
				}" :analysisData="analysisData7" @click="clickChart(analysisData7)" v-if="!loading" />
			</div>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { QueryPlatformCharAnalysis } from '/@/api/financewh/funds';
import { onMounted, ref, defineAsyncComponent } from 'vue';
const stepFallsCharts = defineAsyncComponent(() => import('/@/components/yhCom/stepFallsCharts.vue'));
import dayjs from 'dayjs';
const days = ref({
	start: dayjs().subtract(10, 'day').format('YYYY-MM-DD'),
	end: dayjs().format('YYYY-MM-DD'),
});
const radio1 = ref(10);
const analysisData = ref<any>({});
const analysisData1 = ref<any>({});
const analysisData2 = ref<any>({});
const analysisData3 = ref<any>({});
const analysisData4 = ref<any>({});
const analysisData5 = ref<any>({});
const analysisData6 = ref<any>({});
const analysisData7 = ref<any>({});
const sumChart = ref();
const loading = ref(true);
const changeRadio = (val: any) => {
	days.value.start = dayjs().subtract(val, 'day').format('YYYY-MM-DD');
	days.value.end = dayjs().format('YYYY-MM-DD');
	initProps();
};

const getProp = async () => {
	const {
		data: { data: data1 },
	} = await QueryPlatformCharAnalysis(days.value);

	analysisData.value = disposeData(data1, true, '汇总');
	analysisData1.value = disposeData(JSON.parse(JSON.stringify(data1)), false, '汇总');
};
const getProp2 = async () => {
	const {
		data: { data: data2 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 2 });
	analysisData2.value = disposeData(data2, false, '拼多多');
};

const getProp3 = async () => {
	const {
		data: { data: data3 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 6 });
	analysisData3.value = disposeData(data3, false, '抖音');
};

const getProp4 = async () => {
	const {
		data: { data: data4 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 1 });
	analysisData4.value = disposeData(data4, false, '天猫');
};

const getProp5 = async () => {
	const {
		data: { data: data5 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 9 });
	analysisData5.value = disposeData(data5, false, '淘宝');
};

const getProp6 = async () => {
	const {
		data: { data: data6 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 8 });
	analysisData6.value = disposeData(data6, false, '淘工厂');
};

const getProp7 = async () => {
	const {
		data: { data: data7 },
	} = await QueryPlatformCharAnalysis({ ...days.value, platform: 7 });
	analysisData7.value = disposeData(data7, false, '京东');
};

const disposeData = (data: any, isNeedLegend: boolean, title: string) => {
	data.series.forEach((item: any) => {
		item.stack = 'Total';
		if (item.name == 'Placeholder') {
			item.itemStyle = {
				borderColor: 'transparent',
				color: 'transparent',
			};
			item.emphasis = {
				itemStyle: {
					borderColor: 'transparent',
					color: 'transparent',
				},
			};
			item.silent = true;
		}
	});
	if (!isNeedLegend) {
		data.legend = [];
	}
	data.title = title;
	return data;
};
const clickChart = (val: any) => {
	const res = JSON.parse(JSON.stringify(val));
	res.legend = ['收入', '支出', '提现'];
	sumChart.value.reSetChart(res);
};

const initProps = () => {
	loading.value = true;
	Promise.all([getProp(), getProp2(), getProp3(), getProp4(), getProp5(), getProp6(), getProp7()])
		.then(() => {
			loading.value = false;
		})
		.finally(() => {
			loading.value = false;
		});
};
onMounted(async () => {
	initProps();
});
</script>

<style scoped lang="scss">
#main {
	width: 100%;
	height: 100%;
}

.bottomChart {
	display: flex;
	justify-content: space-between;
	cursor: pointer;

	.bottomChart_item {
		flex: 1;
		box-sizing: border-box;
		margin-right: 10px;
		border: 1px solid #ccc;

		:last-child {
			margin-right: 0;
		}
	}
}
</style>
