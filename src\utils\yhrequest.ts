import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { Session, Local } from '@common/utils/storage'
import { Local, Session } from '/@/utils/storage';
import qs from 'qs';
// import { adminTokenKey } from '@common/api/admin/http-client'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
axios.defaults.headers['Cache-Control'] = 'no-cache';
axios.defaults.headers['Pragma'] = 'no-cache';

var adminTokenKey = Local.get('access-token');
var baseURL = `${import.meta.env.VITE_APP_NET_BASE_API}`;
// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	// baseURL: import.meta.env.VITE_API_URL,
	baseURL: '',
	timeout: 50000,
	// headers: { 'Content-Type': 'application/json' },
	// paramsSerializer: {
	//   serialize(params) {
	//     return qs.stringify(params, { allowDots: true })
	//   },
	// },
});

// 添加请求拦截器
service.interceptors.request.use(
	(config: any) => {
		// 在发送请求之前做些什么 token
		if (Session.get('token')) {
			config.headers!['Authorization'] = `Bearer ${Session.get('token')}`;
		}
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		const res = response.data;
		if (res.code !== 1 && res.code) {
			// `token` 过期或者账号已在别处登录
			if (res.code === 401 || res.code === 4001) {
				Local.remove(adminTokenKey);
				Session.clear();
				window.location.href = '/';
				ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
					.then(() => {})
					.catch(() => {});
			} else if (res.code !== 200) {
				ElMessage.error(res.msg);
			}

			// return Promise.reject(service.interceptors.response)
			return res;
		} else if (!res.success && res.msg) {
			ElMessage.error(res.msg);
		}

		return response.data;
	},
	(error) => {
		if (!error.response?.data?.success) {
			ElMessage.error(error.response?.data?.msg);
			// return Promise.reject(error)
		} else if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('网络超时');
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误');
		} else {
			if (error.response?.data) ElMessage.error(error.response?.statusText);
			else ElMessage.error('接口路径找不到');
		}

		if (error?.request?.status === 401) {
			Local.remove(adminTokenKey);
			Session.clear(); // 清除浏览器全部临时缓存
			window.location.href = '/'; // 去登录页
			ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
				.then(() => {})
				.catch(() => {});
		}
		// return Promise.reject(error)
		return error?.response?.data;
	}
);

// 导出 axios 实例
export default service;
